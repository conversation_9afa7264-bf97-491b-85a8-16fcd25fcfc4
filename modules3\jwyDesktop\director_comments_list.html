﻿<!DOCTYPE html>
<html lang="en-US">
<head>
    <meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/>
    <meta name="format-detection" content="telephone=no"/>
    <meta name="viewport" content="width=device-width,initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0,user-scalable=no"/>
    <!--CSS-->
    <link rel="stylesheet" href="../../frame3/css/bingotouch.css"/>
    <link rel="stylesheet" href="../../frame3/css/app.css"/>

    <!--JS-->
    <script src="../../frame3/js/cordova.js"></script>
    <script src="../../frame3/js/zepto.js"></script>
    <script src="../../frame3/js/iscroll.js"></script>
    <script src="../../frame3/js/baiduTemplate.js"></script>
    <script src="../../frame3/js/bingotouch.js"></script>
    <script src="../../frame3/js/require.js"></script>
    <script src="../../frame3/js/plugin/linkplugins.js"></script>
    <script src="../../frame3/js/app/app.js"></script>
    <script src="../../js/xh_public.js"></script>
    <script src="../../js/xh_iscroll.js"></script>

    <script src="../../js/SzgaPlugin.js"></script>

    <title>BingoTouch</title>

    <!--引用url定义文件-->
    <script src="url.js" type="text/javascript"></script>

    <script type="text/javascript">
        var loginId;
        var userName;
        var deptName;
        var userPosition;
        var userId;

        var scrollList;

        var page = 1;
        var pageSize = 10;

        var directorInfo = null;

        //Dom结构加载完成后执行
        app.page.onReady = function () {
            //to do
        }

        //页面所有元素加载完成后执行
        app.page.onLoad = function () {
            document.addEventListener("deviceready", function () {
                document.addEventListener("backbutton", function () {
                    app.back();
                }, false);
            }, false);

//            app.getMeid(function(result){
//                mMeid = result;
//            });

            scrollList = ui.IScroll.init(".pullrefresh-wrapper", {
                scrollBar : true,      //是否出现滚动条
                enablePullDown: false,   //是否允许下拉刷新
                enablePullUp: true,      //是否允许上拉刷新
                pullUpAction: loadMoreData
            });

            xh.getLoginInfo(function (res) {
                //alert("xh.getLoginInfo:\n" + JSON.stringify(res));
                loginId = res.loginId;
                userName = res.userName;
                userId = res.userId;

//                getDirectorInfo();

                loadData();
            });
        }

        //捕获全局js错误
        app.page.onError = function (msg, url, line) {
            //alert(msg);
        }

        function getDirectorInfo() {
            Cordova.exec(function (result) {
                directorInfo = eval('(' + result + ')');

                loadData();
            }, null, "LinkPlugin", "getDirectorInfo", []);

//            var param = {
//                URL_TYPE: "GAW",
//                APP_URL: "http://68.78.31.5:8380/unifieduser/external/user/v1/infoByCode",
//                userCode: "050001",
//                PostOrGet: "GET"
//            };
//
//            ui.showMask("加载中..");
//            xh.post(door_url, param, function (res) {
//                ui.hideMask();
//                var result = eval('(' + res.returnValue + ')');
//                if (result.success) {
//                    directorInfo = result.data;
//                } else {
//                    directorInfo = null;
//                }
//
//                loadData();
//            }, function (res) {
//                ui.hideMask();
//                app.alert("服务器访问出错，请刷新页面重试");
//            });
        }

        function loadData() {
            var param = {
                "from":"hiboard",
                "userId": userId,
                "pageSize": pageSize,
                "pageNum": page,
                types: 2
            };

            //alert(JSON.stringify(param));
            ui.showMask("加载中..");

            app.ajax({
                "url": "http://20.97.6.131:3001/momentsuiservice/v1/moments/directorComment/ui/getDirectorCommentByPage",
                "data": param,
                "timeout": 30,
                "method": "POST",
                contentType: "application/json",
                "success": function (res) {
                    ui.hideMask();
                    if (debug) {
                        alert(res.returnValue);
                    }
                    //alert(res.returnValue);

                    var result = eval('(' + res.returnValue + ')');
                    //alert(result.data.result.length);

                    var bt = baidu.template;
                    var html = "";

                    for (var i = 0; i < result.data.result.length; i++) {
//                        alert(result.data.result[i].createTime);
//                        alert(result.data.result[i].blogTitle);
                        var type = "";
                        var content = "";
                        for (var j = 0; j < result.data.result[i].types.length; j++) {
                            type = result.data.result[i].types[j];
                            if (result.data.result[i].types[j] == "comment") {
                                content = result.data.result[i].details[j].content;
                                break;
                            }
                        }

                        html += bt("director_comment_list_item", {
                            createTime: result.data.result[i].createTime,
                            blogTitle: result.data.result[i].blogTitle,
                            type: type,
                            content: content,
                            blogId: result.data.result[i].blogId
                        });
                    }

                    $("#director_comment_list").html(html);
                    $("#director_comment_list").uiwidget();

                    scrollList.refresh();
                },
                "fail": function (res) {
                    ui.hideMask();
                    app.alert("服务器访问出错，请刷新页面重试");
                    //alert(res.returnValue);
                },
                "async": true
            });
        }

        function loadMoreData(refreshCallback) {
            page++;
            var param = {
                "from":"hiboard",
                "userId": userId,
                "pageSize": pageSize,
                "pageNum": page,
                types: 2
            };

            app.ajax({
                "url": "http://20.97.6.131:3001/momentsuiservice/v1/moments/directorComment/ui/getDirectorCommentByPage",
                "data": param,
                "timeout": 30,
                "method": "POST",
                contentType: "application/json",
                "success": function (res) {
                    if (debug) {
                        alert(res.returnValue);
                    }

                    var result = eval('(' + res.returnValue + ')');

                    if (result.data.result.length == 0) {
                        page--;
                        if (refreshCallback) {
                            refreshCallback();
                        }
                        return;
                    }

                    var bt = baidu.template;
                    var html = "";

                    for (var i = 0; i < result.data.result.length; i++) {
//                        alert(result.data.result[i].createTime);
//                        alert(result.data.result[i].blogTitle);
                        var type = "";
                        var content = "";
                        for (var j = 0; j < result.data.result[i].types.length; j++) {
                            type = result.data.result[i].types[j];
                            if (result.data.result[i].types[j] == "comment") {
                                content = result.data.result[i].details[j].content;
                                break;
                            }
                        }

                        html += bt("director_comment_list_item", {
                            createTime: result.data.result[i].createTime,
                            blogTitle: result.data.result[i].blogTitle,
                            type: type,
                            content: content,
                            blogId: result.data.result[i].blogId
                        });
                    }

                    $("#director_comment_list").append(html);
                    $("#director_comment_list").uiwidget();

                    if (refreshCallback) {
                        refreshCallback();
                    }
                },
                "fail": function (res) {
                    //app.alert("服务器访问出错，请刷新页面重试");

                    page--;
                    if (refreshCallback) {
                        refreshCallback();
                    }
                    //alert(res.returnValue);
                },
                "async": true
            });
        }

        function addUserPhotoListById(id) {
            if (userPhotoList[id] == undefined) {
                userPhotoList[id] = "";
            }
        }

        function getUserPhotoById(id) {
            var param = {
                userId: id,
                APP_URL: 'http://172.28.0.56:9999/service/user/photo',
                URL_TYPE: "JMT"
            };

            //alert(JSON.stringify(param));

            xh.post("http://172.28.0.56:9999/service/user/photo", param, function (res) {
                //alert(res.returnValue);
                var key = eval("("+res.returnValue+")");
                if (key.flag == 1) {
                    userPhotoList[id] = key.obj;
                    var mHeadImg = "data:image/jpeg;base64," + key.obj;
                    $(".user_id_" + id).attr("src", mHeadImg);
                } else {
                    //getPinGaoPic(mAttrPic);
                }
            }, function (res) {
                alert(JSON.stringify(res));
            });
        }

        function getDateTime(timestamp) {
            var date = new Date(timestamp);
            var year = date.getFullYear();
            var month = date.getMonth() + 1;
            if (month < 10) {
                month = "0" + month;
            }
            var day = date.getDate();
            if (day < 10) {
                day = "0" + day;
            }
            var hour = date.getHours();
            if (hour < 10) {
                hour = "0" + hour;
            }
            var minute = date.getMinutes();
            if (minute < 10) {
                minute = "0" + minute;
            }
            var second = date.getSeconds();
            if (second < 10) {
                second = "0" + second;
            }

            return year + "-" + month + "-" + day + " " + hour + ":" + minute + ":" + second
        }

        function getDateTime2(timestamp) {
            var ret = "";

            var today = new Date();
            var now = today.getTime();
            var delta = (now - timestamp) / 1000;

            if (delta <= 60 * 60) {
                ret = delta / 60;
                ret = parseInt(ret);
                ret = ret + "分钟前";
            } else if (delta <= 60 * 60 * 24) {
                ret = delta / (60 * 60);
                ret = parseInt(ret);
                ret = ret + "小时前";
            } else {
                ret = delta / (60 * 60 * 24);
                ret = parseInt(ret);
                ret = ret + "天前";
            }

            return ret;
        }

        function openDongtaiDetail(blogId) {
            //alert(blogId);
            var params = {
                action: "xinghuo.mpaas.dynamic.ui.DynamicDefaultActivity",
                names: ["dynamicId"],
                values: [blogId]
            };

            app.szgaplugin.startActivityForResult(params, function (res) {
                //alert("success: " + res);
            }, function (err) {
                //alert("fail: " + err);
            });
        }


    </script>

    <style type="text/css">
        .font-size-22 {
            font-size: 22px;
        }
        .font-size-20 {
            font-size: 20px;
        }
        .font-size-18 {
            font-size: 18px;
        }
        .font-size-16 {
            font-size: 16px;
        }
        .font-color-c8c8c8 {
            color: #c8c8c8;
        }
        .font-color-333333 {
            color: #333333;
        }
        .font-color-4783e2 {
            color: #4783e2;
        }
        .font-color-a1a1a1 {
            color: #a1a1a1;
        }
        .test-line {
            width: 300px;
            height: 80px;
            border: 1px solid red;
            /*word-break: break-all;*/
            /*display: -webkit-box;*/
            /*-webkit-line-clamp: 3;  !*限制在一个块元素显示的文本的行数*!*/
            /*-webkit-box-orient: vertical;*/
            overflow: hidden;
            text-overflow:ellipsis;
            white-space: nowrap;
        }
    </style>

</head>
<body class="desktop">
    <div id="section_container">
        <section id="index_section" class="active">
            <div data-role="page">
                <!--Header-->
                <div class="header" data-fixed="top">
                    <div class="title row-box" style="background: #4783e2 !important;border-color: #4783e2 !important;text-shadow: none;">
                        <div class="box-left">
                            <div data-role="BTButton" data-type="image" onClick="app.back();">
                                <img src="image/icon_back.jpg" alt="" width="30" height="30"/>
                            </div>
                        </div>
                        <div class="span1">
                            <span style="font-size: 25px;">局长点评</span>
                        </div>
                        <div class="box-right">
                            <div data-role="BTButton" data-type="image" onclick="app.refresh();">
                                <img src="image/icon_refresh.jpg" alt="" width="30" height="30"/>
                            </div>
                        </div>
                    </div>
                </div>

                <!--Content-->
                <div class="content pullrefresh-wrapper" style="background: #f5f5f5;">

                   <div>
                       <div class="container-fluid">
                           <ul class="list-view" id="director_comment_list">

                           </ul>

                           <script type="text/html" id="director_comment_list_item">
                               <li style="margin-bottom: 20px;">
                                   <div data-role="BTButton" mousedown="false" onclick="openDongtaiDetail('<%=blogId%>');" style="border-radius: 10px;">
                                       <!--<div class="row-box">-->
                                           <!--<div class="box-left">-->
                                               <!--<% if (directorInfo == null) { %>-->
                                               <!--<img src="image/default_user_photo.png" style="width: 50px;height: 50px;border-radius: 25px;"/>-->
                                               <!--<% } else { %>-->
                                               <!--<img src="<%=directorInfo.userPhotoPath%>" style="width: 50px;height: 50px;border-radius: 25px;"/>-->
                                               <!--<% } %>-->
                                           <!--</div>-->
                                           <!--<div class="span1" style="padding-left: 20px;">-->
                                               <!--<div class="thumbnail-text" style="padding-top: 5px;">-->
                                                   <!--<% if (directorInfo == null) { %>-->
                                                   <!--<span class="font-size-20">局长</span>-->
                                                   <!--<% } else { %>-->
                                                   <!--<span class="font-size-20"><%=directorInfo.userName%></span>-->
                                                   <!--<% } %>-->
                                                   <!--<div style="padding-top: 8px;">-->
                                                       <!--<span class="font-size-18 font-color-c8c8c8"><%=getDateTime(createTime)%></span>-->
                                                   <!--</div>-->
                                               <!--</div>-->
                                           <!--</div>-->
                                       <!--</div>-->
                                       <div class="thumbnail-text" style="margin-top: 15px;padding: 0px 10px 0px 10px;">
                                           <h4>
                                               <% if (type=='thumpUp') { %>
                                               《<%=blogTitle%>》&nbsp;&nbsp;被局长点赞
                                               <% } %>
                                               <% if (type=='comment') { %>
                                               <%=content%>
                                               <% } %>
                                           </h4>
                                       </div>
                                       <div class="row-box" style="padding: 0px 10px 0px 10px;">
                                           <div style="width: 7%;">
                                               <img src="image/icon_director.png" width="22" height="22"/>
                                           </div>
                                           <div style="width: 10%;">
                                               <span class="font-color-4783e2 font-size-16">置顶</span>
                                           </div>
                                           <div>
                                               <span class="font-color-a1a1a1 font-size-16"><%=getDateTime2(createTime)%></span>
                                           </div>
                                       </div>
                                       <div style="background: #f6f6f6;padding: 10px;border-radius: 10px;">
                                           <div class="row-box">
                                               <div style="width: 20%;">
                                                   <img src="image/default_image_director_comment.png" style="width: 80%;background: #eeeeee;border-radius: 5px;"/>
                                               </div>
                                               <div>
                                                   <div style="padding-top: 5px;width: 320px;padding-left: 15px;overflow: hidden;text-overflow:ellipsis;white-space: nowrap;">
                                                       <span class="font-color-333333 font-size-20" style="line-height: 1.4;"><%=blogTitle%></span>
                                                   </div>
                                                   <!--<div style="padding-top: 10px;padding-left: 15px;">-->
                                                       <!--<span class="font-color-a9a9a9 font-size-18">赵东来</span>-->
                                                   <!--</div>-->
                                               </div>
                                           </div>
                                       </div>
                                   </div>
                               </li>
                           </script>

                           <script type="text/html" id="director_comment_list_item2">
                               <li style="padding-bottom: 20px;padding-top: 10px;">
                                   <div style="padding-right: 15px;" onclick="openDongtaiDetail('<%=blogId%>');">
                                       <div class="row-box">
                                           <div>
                                               <% if (directorInfo == null) { %>
                                               <img src="image/default_user_photo.png" style="width: 50px;height: 50px;border-radius: 25px;"/>
                                               <% } else { %>
                                               <img src="data:image/jpeg;base64,<%=directorInfo.userPhoto%>" style="width: 50px;height: 50px;border-radius: 25px;"/>
                                               <% } %>
                                           </div>
                                           <div style="padding-left: 20px;">
                                               <div style="padding-top: 5px;">
                                                   <% if (directorInfo == null) { %>
                                                   <span class="font-size-20">局长</span>
                                                   <% } else { %>
                                                   <span class="font-size-20"><%=directorInfo.userName%></span>
                                                   <% } %>
                                               </div>
                                               <div style="padding-top: 8px;">
                                                   <span class="font-size-18 font-color-c8c8c8"><%=getDateTime(createTime)%></span>
                                               </div>
                                           </div>
                                       </div>
                                       <div style="margin-top: 15px;padding: 0px 10px 10px 10px;">
                                       <span class="font-size-20" style="line-height: 1.4;">
                                           <% if (type=='thumpUp') { %>
                                           《<%=blogTitle%>》&nbsp;&nbsp;被局长点赞
                                           <% } %>
                                           <% if (type=='comment') { %>
                                           <%=content%>
                                           <% } %>
                                       </span>
                                       </div>
                                       <div style="background: #f6f6f6;padding: 10px;border-radius: 10px;">
                                           <div class="row-box">
                                               <div style="width: 20%;">
                                                   <img src="image/default_pic.jpg" style="width: 90%;"/>
                                               </div>
                                               <div>
                                                   <div style="padding-top: 5px;width: 350px;padding-left: 15px;overflow: hidden;text-overflow:ellipsis;white-space: nowrap;">
                                                       <span class="font-color-333333 font-size-22" style="line-height: 1.4;"><%=blogTitle%></span>
                                                   </div>
                                                   <div style="padding-top: 10px;padding-left: 15px;">
                                                       <span class="font-color-a9a9a9 font-size-18">赵东来</span>
                                                   </div>
                                               </div>
                                           </div>
                                       </div>
                                   </div>
                               </li>
                           </script>
                       </div>
                   </div>

                </div>

                <!--Footer-->
                <div class="footer" data-fixed="bottom"></div>
            </div>
        </section>


    </div>



</body>
</html>
