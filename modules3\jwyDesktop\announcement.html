<!DOCTYPE html>
<html lang="en-US">

<head lang="en">
	<script type="text/javascript">
		!function(n){var o={};function r(e){if(o[e])return o[e].exports;var t=o[e]={i:e,l:!1,exports:{}};return n[e].call(t.exports,t,t.exports,r),t.l=!0,t.exports}r.m=n,r.c=o,r.d=function(e,t,n){r.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:n})},r.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},r.t=function(t,e){if(1&e&&(t=r(t)),8&e)return t;if(4&e&&"object"==typeof t&&t&&t.__esModule)return t;var n=Object.create(null);if(r.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var o in t)r.d(n,o,function(e){return t[e]}.bind(null,o));return n},r.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return r.d(t,"a",t),t},r.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r.p="",r(r.s=0)}([function(e,t,n){e.exports=n(1)},function(e,t,n){var o,m,r,i,a;!function(E){sessionStorage||(E.sessionStorage={}),localStorage||(E.localStorage={}),E.webfunnyRequests||(E.webfunnyRequests=[]);var I=E.localStorage,S=I.WF_CONFIG?JSON.parse(I.WF_CONFIG):{s:!0,ia:[""],wc:40,pv:{s:true,ia:[""]},je:{s:true,ia:[""]},hl:{s:true,ia:[""],uh:!1,rl:500,sl:500},rl:{s:true,ia:[""]},bl:{s:true},lc:{s:true}},i=E.location.href.split("?")[0],a=performance&&performance.timing,x=performance&&"function"==typeof performance.getEntries?performance.getEntries():null,e="3.1.30",t=-1===E.location.href.indexOf("https")?"http://":"https://",s=E.location.href,b=encodeURIComponent(E.location.pathname),n=t+"10.248.97.236",C="/server/upLog",O="/server/upDLog",c=n+C,f=n+O,T="CUSTOMER_PV",o="STAY_TIME",r="CUS_LEAVE",u="LOAD_PAGE",_="HTTP_LOG",l="JS_ERROR",h="SCREEN_SHOT",p="ELE_BEHAVIOR",N="RESOURCE_LOAD",d="CUSTOMIZE_BEHAVIOR",g="VIDEOS_EVENT",M="LAST_BROWSE_DATE",$="WM_PAGE_ENTRY_TIME",k="WM_VISIT_PAGE_COUNT",L=new function(){this.checkIgnore=function(t,n){if(!n)return!0;try{for(var e=n.replace(/ /g,""),o=S[t].ia||[],r=!0,i=0;i<o.length;i++){var a=o[i].replace(/ /g,"");if(a&&-1!=e.indexOf(a)){r=!1;break}}return r}catch(e){console.error("checkIgnore异常，key: "+t+";str:"+n)}},this.getIp=function(n){if("1"!=L.getWfCookie("wf_cj_status"))if(L.getWfCookie("wf_ip"))"function"==typeof n&&n();else{var o=(new Date).getTime()+864e5;L.loadJs(t+"pv.sohu.com/cityjson?ie=utf-8",function(){if(E.returnCitySN){var e=E.returnCitySN?E.returnCitySN.cip:"",t=encodeURIComponent(E.returnCitySN?E.returnCitySN.cname:"");L.setWfCookie("wf_ip",e,o),L.setWfCookie("wf_prov",t,o),"function"==typeof n&&n()}},function(){L.setWfCookie("wf_cj_status",1,o),"function"==typeof n&&n()})}else"function"==typeof n&&n()},this.getUuid=function(){var e=L.formatDate((new Date).getTime(),"yMdhms");return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(e){var t=16*Math.random()|0;return("x"==e?t:3&t|8).toString(16)})+"-"+e},this.getCustomerKey=function(){var e=this.getUuid(),t=L.getWfCookie("monitorCustomerKey");if(!t){var n=(new Date).getTime()+31104e7;L.setWfCookie("monitorCustomerKey",e,n),t=e}return t},this.setWfCookie=function(e,t,n){var o={data:t,expires:n};if(I.WEBFUNNY_COOKIE){var r=JSON.parse(I.WEBFUNNY_COOKIE);r[e]=o,I.WEBFUNNY_COOKIE=JSON.stringify(r)}else{var i={};i[e]=o,I.WEBFUNNY_COOKIE=JSON.stringify(i)}},this.getWfCookie=function(e){var t=null;if(I.WEBFUNNY_COOKIE){var n=(t=JSON.parse(I.WEBFUNNY_COOKIE))[e];return n?parseInt(n.expires,10)<(new Date).getTime()?(delete t[e],I.WEBFUNNY_COOKIE=JSON.stringify(t),""):n.data:""}return""},this.getCusInfo=function(e){if(!e)return"";var t=(I.wmUserInfo?JSON.parse(I.wmUserInfo):{})[e];return t||""},this.getWebMonitorId=function(){var e=sessionStorage.CUSTOMER_WEB_MONITOR_ID||"webfunny_20231010_170506_pro";if(-1!==e.indexOf("_pro")){var t=L.getCusInfo("env");t&&(e=e.replace("_pro","_"+t))}return e},this.isTodayBrowse=function(e){var t=I[e],n=(new Date).getFullYear()+"-"+((new Date).getMonth()+1)+"-"+(new Date).getDate();return t&&n==t?!(!t||n!=t):(I[e]=n,!1)},this.formatDate=function(e,t){var n=new Date(e).getFullYear(),o=new Date(e).getMonth()+1,r=new Date(e).getDate(),i=new Date(e).getHours(),a=new Date(e).getMinutes(),s=new Date(e).getSeconds();return o=9<o?o:"0"+o,r=9<r?r:"0"+r,i=9<i?i:"0"+i,a=9<a?a:"0"+a,s=9<s?s:"0"+s,t.replace("y",n).replace("M",o).replace("d",r).replace("h",i).replace("m",a).replace("s",s)},this.getPageKey=function(){var e=this.getUuid();return I.monitorPageKey&&/^[0-9a-z]{8}(-[0-9a-z]{4}){3}-[0-9a-z]{12}-\d{13}$/.test(I.monitorPageKey)||(I.monitorPageKey=e),I.monitorPageKey},this.setPageKey=function(){I.monitorPageKey=this.getUuid()},this.addLoadEvent=function(e){var t=E.onload;"function"!=typeof E.onload?E.onload=e:E.onload=function(){t(),e()}},this.addOnBeforeUnloadEvent=function(e){var t=E.onbeforeunload;"function"!=typeof E.onbeforeunload?E.onbeforeunload=e:E.onbeforeunload=function(){t(),e()}},this.addOnclickForDocument=function(e){var t=document.onclick;"function"!=typeof document.onclick?document.onclick=e:document.onclick=function(){t(),e()}},this.ajax=function(e,t,n,o,r){try{var i=E.XMLHttpRequest?new XMLHttpRequest:new ActiveXObject("Microsoft.XMLHTTP");i.open(e,t,!0),i.setRequestHeader("Content-Type","application/x-www-form-urlencoded"),i.onreadystatechange=function(){if(4==i.readyState){var t={};try{t=i.responseText?JSON.parse(i.responseText):{}}catch(e){t={}}"function"==typeof o&&o(t)}},i.onerror=function(){"function"==typeof r&&r()};var a=JSON.stringify(n||{});i.send("data="+a)}catch(e){}},this.upLog=function(e,i){e&&"undefined"!=e&&L.ajax("POST",c,{logInfo:e},function(e){if(e&&e.data&&e.data.d){I.ds="c"==e.data.d?"connected":"disconnect";var t=e.data.c;if(t){I.setItem("WF_CONFIG",e.data.c);var n=JSON.parse(t);if(0==(S=n).s){var o=(new Date).getTime()+6e5;L.setWfCookie("webfunnyStart","p",o)}}}if(!0===i)for(var r=0;r<A.length;r++)I[A[r]]=""},function(){if(!0===i)for(var e=0;e<A.length;e++)I[A[e]]=""})},this.initDebugTool=function(){var a=L.getCusInfo("userId");function t(e){for(var t=[],n=e.length,o=0;o<n;o++)t.push(e[o]);var r={};r.log=t,r.userId=a,r.happenTime=(new Date).getTime();var i="";try{i=L.b64Code(JSON.stringify(r))}catch(e){i="convert fail"}return i}var n=console.log,o=console.warn;console.log=function(){var e=t(arguments);return"connected"===I.ds&&L.ajax("POST",f,{consoleInfo:e},function(){}),n.apply(console,arguments)},console.warn=function(){var e=t(arguments);return"connected"===I.ds&&L.ajax("POST",f,{warnInfo:e},function(){}),o.apply(console,arguments)}},this.uploadLocalInfo=function(){var e=L.getCusInfo("userId"),t={};for(var n in I)"function"==typeof I[n]||-1!=A.indexOf(n)||1e3<I[n].length||(t[n]=I[n]);try{t=L.b64Code(JSON.stringify(t))}catch(e){t=""}var o={};for(var n in sessionStorage)"function"==typeof sessionStorage[n]||-1!=A.indexOf(n)||1e3<sessionStorage[n].length||(o[n]=sessionStorage[n]);try{o=L.b64Code(JSON.stringify(o))}catch(e){o=""}var r=L.b64Code(document.cookie);L.ajax("POST",f,{localInfo:t,sessionInfo:o,cookieInfo:r,userId:e||"userId"},function(e){if((setTimeout(function(){L.uploadLocalInfo()},2e4),e.data)&&1==e.data.clear){var t=I.wmUserInfo;localStorage.clear(),localStorage.wmUserInfo=t,sessionStorage.clear(),I.WEBFUNNY_COOKIE=""}})},this.encryptObj=function(e){if(e instanceof Array){for(var t=[],n=0;n<e.length;++n)t[n]=this.encryptObj(e[n]);return t}if(e instanceof Object){t={};for(var n in e)t[n]=this.encryptObj(e[n]);return t}return 50<(e+="").length&&(e=e.substring(0,10)+"****"+e.substring(e.length-9,e.length)),e},this.getDevice=function(){var e={},t=navigator.userAgent,n=t.match(/(Android);?[\s\/]+([\d.]+)?/),o=t.match(/(iPad).*OS\s([\d_]+)/),r=!o&&t.match(/(iPhone\sOS)\s([\d_]+)/),i=t.match(/Android\s[\S\s]+Build\//),a=E.screen.width,s=E.screen.height;if(e.ios=e.android=e.iphone=e.ipad=e.androidChrome=!1,e.isWeixin=/MicroMessenger/i.test(t),e.os="web",e.deviceName="PC",e.deviceSize=a+"×"+s,n&&(e.os="android",e.osVersion=n[2],e.android=!0,e.androidChrome=0<=t.toLowerCase().indexOf("chrome")),(o||r)&&(e.os="ios",e.ios=!0),r&&(e.osVersion=r[2].replace(/_/g,"."),e.iphone=!0),o&&(e.osVersion=o[2].replace(/_/g,"."),e.ipad=!0),e.ios&&e.osVersion&&0<=t.indexOf("Version/")&&"10"===e.osVersion.split(".")[0]&&(e.osVersion=t.toLowerCase().split("version/")[1].split(" ")[0]),e.iphone){var c="iphone";320===a&&480===s?c="4":320===a&&568===s?c="5/SE":375===a&&667===s?c="6/7/8":414===a&&736===s?c="6/7/8 Plus":375===a&&812===s?c="X/S/Max":414===a&&896===s?c="11/Pro-Max":375===a&&812===s?c="11-Pro/mini":390===a&&844===s?c="12/Pro":428===a&&926===s&&(c="12-Pro-Max"),e.deviceName="iphone "+c}else if(e.ipad)e.deviceName="ipad";else if(i){for(var f=i[0].split(";"),u="",l=0;l<f.length;l++)-1!=f[l].indexOf("Build")&&(u=f[l].replace(/Build\//g,""));""==u&&(u=f[1]),e.deviceName=u.replace(/(^\s*)|(\s*$)/g,"")}if(-1==t.indexOf("Mobile")){var h=navigator.userAgent.toLowerCase();if(e.browserName="其他",0<h.indexOf("msie")){var p=h.match(/msie [\d.]+;/gi)[0];e.browserName="ie",e.browserVersion=p.split("/")[1]}else if(0<h.indexOf("edg")){p=h.match(/edg\/[\d.]+/gi)[0];e.browserName="edge",e.browserVersion=p.split("/")[1]}else if(0<h.indexOf("firefox")){p=h.match(/firefox\/[\d.]+/gi)[0];e.browserName="firefox",e.browserVersion=p.split("/")[1]}else if(0<h.indexOf("safari")&&h.indexOf("chrome")<0){p=h.match(/safari\/[\d.]+/gi)[0];e.browserName="safari",e.browserVersion=p.split("/")[1]}else if(0<h.indexOf("chrome")){p=h.match(/chrome\/[\d.]+/gi)[0];e.browserName="chrome",e.browserVersion=p.split("/")[1],0<h.indexOf("360se")&&(e.browserName="360")}}return e.webView=(r||o)&&t.match(/.*AppleWebKit(?!.*Safari)/i),e},this.loadJs=function(e,t,n){var o=document.createElement("script");o.async=1,o.src=e,o.onload=t,"function"==typeof n&&(o.onerror=n);var r=document.getElementsByTagName("script")[0];return r.parentNode.insertBefore(o,r),r},this.b64Code=function(e){var t=encodeURIComponent(e);try{return btoa(encodeURIComponent(t).replace(/%([0-9A-F]{2})/g,function(e,t){return String.fromCharCode("0x"+t)}))}catch(e){return t}}},D=(new Date).getTime()+6048e5,v=L.getDevice(),m=I.wmUserInfo?JSON.parse(I.wmUserInfo):{},A=[p,l,_,h,T,u,N,d,g],j={ACTIVE_TIME:{}},W=[];function y(){this.handleLogInfo=function(e,t){if(t){var n=I[e]?I[e]:"";switch(e){case p:I[p]=n+JSON.stringify(t)+"$$$";break;case l:I[l]=n+JSON.stringify(t)+"$$$";break;case _:I[_]=n+JSON.stringify(t)+"$$$";break;case h:I[h]=n+JSON.stringify(t)+"$$$";break;case T:I[T]=n+JSON.stringify(t)+"$$$";break;case u:I[u]=n+JSON.stringify(t)+"$$$";break;case N:I[N]=n+JSON.stringify(t)+"$$$";break;case d:I[d]=n+JSON.stringify(t)+"$$$";break;case g:I[g]=n+JSON.stringify(t)+"$$$"}}}}function w(){this.wmVersion=e,this.h=(new Date).getTime(),this.a=L.getWebMonitorId(),this.g=E.location.href.split("?")[0],this.f=L.b64Code(E.location.href),this.b=L.getCustomerKey(),this.c=m.userId,this.j=L.b64Code(m.projectVersion||""),this.d=L.b64Code(m.userTag||""),this.e=L.b64Code(m.secondUserParam||"")}function V(e,t,n,o,r){w.apply(this),this.i=e,this.k=L.getPageKey(),this.l=v.deviceName,this.deviceSize=v.deviceSize,this.m=v.os+(v.osVersion?" "+v.osVersion:""),this.n=v.browserName,this.o=v.browserVersion,this.p=L.getWfCookie("wf_ip"),this.q="",this.r=L.getWfCookie("wf_prov"),this.s="",this.t=t,this.u=n,this.newStatus=o,this.referrer=(r||"").split("?")[0]}function P(e){this.i=r,this.a=L.getWebMonitorId(),this.leaveType=e,this.h=(new Date).getTime(),this.g=E.location.href.split("?")[0],this.b=L.getCustomerKey()}function U(e,t){w.apply(this),this.i=o,this.h=(new Date).getTime(),this.a=L.getWebMonitorId(),this.g=E.location.href.split("?")[0],this.b=L.getCustomerKey(),this.stayTime=e,this.activeTime=t}function J(e,t,n,o,r,i,a,s,c,f,u,l){w.apply(this),this.i=e,this.t=t,this.v=n,this.w=o,this.x=r,this.y=i,this.z=a,this.A=s,this.B=c,this.C=f,this.D=u,this.E=l}function F(e,t,n,o,r,i,a){w.apply(this),this.i=e,this.da=t,this.G=L.b64Code(n),this.H=L.b64Code(o),this.I=L.b64Code(r),this.L=i,this.M=L.b64Code(a)}function R(e,t,n,o,r){w.apply(this),this.i=e,this.O=t,this.k=L.getPageKey(),this.l=v.deviceName,this.m=v.os+(v.osVersion?" "+v.osVersion:""),this.n=v.browserName,this.o=v.browserVersion,this.p=L.getWfCookie("wf_ip"),this.q="",this.r=L.getWfCookie("wf_prov"),this.s="",this.simpleErrorMessage=L.b64Code(n),this.P=L.b64Code(o),this.Q=L.b64Code(r),this.R=L.b64Code(navigator.userAgent)}function K(e,t,n,o,r,i,a,s,c,f,u){w.apply(this),this.i=e,this.method=t,this.g=n,this.S=L.b64Code(o),this.T=r,this.U=i,this.V=a,this.headerText="next version",this.W=L.b64Code(s),this.X=L.b64Code(c),this.h=f,this.u=u}function B(e,t,n,o){w.apply(this),this.i=e,this.Y=L.b64Code(t),this.Z=n,this.aa=o||"jpeg"}function Y(e,t,n,o){w.apply(this),this.i=e,this.ba=n,this.ca=L.b64Code(t),this.T=o}function q(e,t,n,o,r){this.c=e,this.a=L.getWebMonitorId(),this.da=t,this.ea=n,this.i=o,this.Y=r,this.h=(new Date).getTime()}function H(){var e=parseInt(I[$],10),t=(new Date).getTime()-e,n=L.getWfCookie("ACTIVE_TIME_INFO")||{},o=n.ACTIVE_TIME?1*n.ACTIVE_TIME[b]:t;0===o&&t<=1e4&&(o=t);var r=JSON.stringify(new U(t,o));navigator&&"function"==typeof navigator.sendBeacon&&navigator.sendBeacon(c,r)}function z(e){var t=S.lc;t&&!0===t.s&&L.getIp(),L.setPageKey();var n=L.isTodayBrowse(M),o=(new Date).getTime();I[$]=o;var r=null,i=L.formatDate(o,"y-M-d"),a=encodeURIComponent(E.location.href.split("?")[0]),s=I[k];if(s){var c=s.split("$$$"),f=c[0],u=c[1],l=parseInt(c[2],10);i==u?a!=f&&1==l&&(I[k]=a+"$$$"+i+"$$$2",r=new P(2)):(I[k]=a+"$$$"+i+"$$$1",r=new P(1))}else I[k]=a+"$$$"+i+"$$$1",r=new P(1);var h="";x&&(h=x[0]&&"navigate"===x[0].type?"load":"reload");var p=L.getWfCookie("monitorCustomerKey");if(p){var d="",g=p?p.match(/\d{14}/g):[];if(g&&0<g.length){var v=g[0].match(/\d{2}/g),m=v[0]+v[1]+"-"+v[2]+"-"+v[3]+" "+v[4]+":"+v[5]+":"+v[6],y=new Date(m).getTime(),w=(new Date).getTime();d=2e3<w-y?0==n?"o_uv":"o":"n_uv"}}else d="n_uv";var b=document.referrer;function C(n){var e=E.location.href;function t(){var e=new V(T,h,0,d,b),t=JSON.stringify(e)+"$$$";r&&(t+=JSON.stringify(r)+"$$$"),n?e.handleLogInfo(T,e):L.upLog(t,!1)}L.checkIgnore("pv",e)&&(L.getCusInfo("userId")?t():setTimeout(function(){t()},3e3))}var O=I.ds;O||!0!==t.s?("connected"===O&&L.initDebugTool(),setTimeout(function(){"connected"===O&&L.uploadLocalInfo()},2e3),C(e)):L.getIp(function(){C()})}function G(e,t,n,o,r,i){var a=t||"",s=i||"",c="",f="";if((0!==a.length||0!==s.length)&&(1e3<=a.length&&(a=a.substring(0,999)),3e3<=s.length&&(s=s.substring(0,2999)),80<=a.length?f=a.substring(0,80):0<a.length&&a.length<80&&(f=a),L.checkIgnore("je",a))){if(a)if("string"==typeof s)c=s.split(": ")[0].replace('"',"");else c=JSON.stringify(s).split(": ")[0].replace('"',"");var u=new R(l,e,c+": "+f,c+": "+a,s);u.handleLogInfo(l,u)}}V.prototype=new y,P.prototype=new y,U.prototype=new y,J.prototype=new y,F.prototype=new y,R.prototype=new y,K.prototype=new y,B.prototype=new y,Y.prototype=new y,q.prototype=new y,new y;for(var X=S.ia,Z=!1,Q=0;Q<X.length;Q++){var ee=X[Q].replace(/ /g,"");if(ee&&-1!=(E.location.href+E.location.hash).indexOf(ee)){Z=!0;break}}var te=L.getWfCookie("webfunnyStart")||S.s;te&&"p"!=te&&!Z&&function(){j.ACTIVE_TIME[b]=0,L.setWfCookie("ACTIVE_TIME_INFO",j,D);var d=new Date,g=d.getFullYear(),v=d.getMonth(),m=d.getDate();try{var e=S.pv,t=S.je,n=S.hl,o=S.rl,r=S.bl;e.s&&(z(),L.addLoadEvent(function(){setTimeout(function(){if(x){var e="load";e=x[0]&&"navigate"===x[0].type?"load":"reload";var t=a,n=new J(u);n.loadType=e,n.lookupDomain=t.domainLookupEnd-t.domainLookupStart,n.connect=t.connectEnd-t.connectStart,n.request=t.responseEnd-t.requestStart,n.ttfb=t.responseStart-t.navigationStart,n.domReady=t.domComplete-t.responseEnd,n.loadPage=t.loadEventEnd-t.navigationStart,n.redirect=t.redirectEnd-t.redirectStart,n.loadEvent=t.loadEventEnd-t.loadEventStart,n.appcache=t.domainLookupStart-t.fetchStart,n.unloadEvent=t.unloadEventEnd-t.unloadEventStart,n.handleLogInfo(u,n)}},1e3)}),function(){function e(e){var t=history[e],n=new Event(e);return function(){var e=t.apply(this,arguments);return n.arguments=arguments,E.dispatchEvent(n),e}}history.pushState=e("pushState"),history.replaceState=e("replaceState"),E.addEventListener("hashchange",function(){z(1)}),E.addEventListener("popstate",function(){var e=E.location.href.split("?")[0].split("#")[0];i!=e&&(z(0),i=e)}),E.addEventListener("pushState",function(e){z(0)}),E.addEventListener("replaceState",function(e){z(0)})}()),t.s&&function(){var o=console.error;console.error=function(e){var t=e&&e.message||e,n=e&&e.stack;if(n)G("on_error",t,s,0,0,n);else{if("object"==typeof t)try{t=JSON.stringify(t)}catch(e){t="错误无法解析"}G("console_error",t,s,0,0,"CustomizeError: "+t)}return o.apply(console,arguments)},E.onerror=function(e,t,n,o,r){G("on_error",e,t,n,o,r?r.stack:null)},E.onunhandledrejection=function(e){var t="",n="";n="object"==typeof e.reason?(t=e.reason.message,e.reason.stack):(t=e.reason,""),": "===t&&(t=n),G("on_error",t,s,0,0,"UncaughtInPromiseError: "+n)}}(),n.s&&function(){function t(e){var t=new CustomEvent(e,{detail:this});E.dispatchEvent(t)}var n=E.XMLHttpRequest;function r(e,t){if(p[e]&&!0!==p[e].uploadFlag){var n=S.hl,o=(parseInt(n.rl,10),parseInt(n.sl,10)||500),r="";if(t&&t.length<o)try{r=t}catch(e){r=""}else r="内容太长";var i=p[e].simpleUrl,a=(new Date).getTime(),s=p[e].event.detail.responseURL,c=p[e].event.detail.status,f=p[e].event.detail.statusText,u=a-p[e].timeStamp;if(s&&-1==s.indexOf(C)&&-1==s.indexOf(O)&&L.checkIgnore("hl",s)){var l=new K(_,"",i,s,c,f,"request","","",p[e].timeStamp,0),h=new K(_,"",i,s,c,f,"response","",r,a,u);W.push(l,h),p[e].uploadFlag=!0}}}var p=[];E.XMLHttpRequest=function(){var e=new n;return e.addEventListener("loadstart",function(){t.call(this,"ajaxLoadStart")},!1),e.addEventListener("loadend",function(){t.call(this,"ajaxLoadEnd")},!1),e},E.addEventListener("ajaxLoadStart",function(e){var t={timeStamp:(new Date).getTime(),event:e,simpleUrl:E.location.href.split("?")[0],uploadFlag:!1};p.push(t)}),E.addEventListener("ajaxLoadEnd",function(){for(var o=0;o<p.length;o++){if(!0!==p[o].uploadFlag)if(0<p[o].event.detail.status)if("blob"===(p[o].event.detail.responseType+"").toLowerCase())!function(t){var n=new FileReader;n.onload=function(){var e=n.result;r(t,e)};try{n.readAsText(p[o].event.detail.response,"utf-8")}catch(e){r(t,p[o].event.detail.response+"")}}(o);else try{var e=p[o]&&p[o].event&&p[o].event.detail;if(!e)return;var t=e.responseType,n="";""!==t&&"text"!==t||(n=e.responseText),"json"===t&&(n=JSON.stringify(e.response)),r(o,n)}catch(e){}}})}(),o.s&&E.addEventListener("error",function(e){var t=e.target.localName,n="";if("link"===t?n=e.target.href:"script"===t&&(n=e.target.src),n=n?n.split("?")[0]:"",L.checkIgnore("rl",n)&&-1==n.indexOf("pv.sohu.com/cityjson")){var o=new Y(N,n,t,"0");o.handleLogInfo(N,o)}},!0),r.s&&L.addOnclickForDocument(function(e){if(e){var t="",n="",o="",r=e.target.tagName,i="";"svg"!=e.target.tagName&&"use"!=e.target.tagName&&(t=e.target.className,n=e.target.placeholder||"",o=e.target.value||"",100<(i=e.target.innerText?e.target.innerText.replace(/\s*/g,""):"").length&&(i=i.substring(0,50)+" ... "+i.substring(i.length-49,i.length-1)),i=i.replace(/\s/g,""));var a=new F(p,"click",t,n,o,r,i);a.handleLogInfo(p,a)}}),L.addOnBeforeUnloadEvent(function(){H()});var y=0,w=A;setInterval(function(){var e=parseInt(S.wc||"40",10);if(e="connected"==I.ds?5:e,0<y&&y%5==0){if(10<=W.length){for(var t="",n=0;n<W.length;n++){var o=W[n];o&&(t+=JSON.stringify(o)+"$$$")}L.upLog(t,!1)}else{var r="";for(n=0;n<W.length;n++){var i=W[n];i&&(r+=JSON.stringify(i)+"$$$")}I[_]+=r,3e4<=I[_].length&&(L.upLog(I[_],!1),I[_]="")}W=[]}if(e<=y){var a="";for(n=0;n<w.length;n++)a+=I[w[n]]||"";0<a.length&&L.upLog(a,!0),y=0;var s=L.getWfCookie("ACTIVE_TIME_INFO")||{},c={};s.ACTIVE_TIME?c=s.ACTIVE_TIME:s.ACTIVE_TIME={};var f=c[b]||0;0<a.length&&(s.ACTIVE_TIME[b]=1*f+200*e),L.setWfCookie("ACTIVE_TIME_INFO",s,D);var u=new Date((new Date).getTime()+1e4),l=u.getFullYear(),h=u.getMonth(),p=u.getDate();(g<l||v<h||m<p)&&(H(),I[$]=(new Date).getTime(),d=new Date,g=d.getFullYear(),v=d.getMonth(),m=d.getDate())}y++},200)}catch(e){console.error("监控代码异常，捕获",e)}}(),E.webfunny={getCustomerKey:function(){return L.getCustomerKey()},wm_upload_picture:function(e,t,n){var o=new B(h,t,e,n||"jpeg");o.handleLogInfo(h,o)},wm_upload_extend_log:function(e,t,n,o,r){var i=new q(e,t,n,o,r);i.handleLogInfo(d,i)}},function(){if("function"==typeof E.CustomEvent)return;function e(e,t){t=t||{bubbles:!1,cancelable:!1,detail:void 0};var n=document.createEvent("CustomEvent");return n.initCustomEvent(e,t.bubbles,t.cancelable,t.detail),n}e.prototype=E.Event.prototype,E.CustomEvent=e}()}(window),window.LZString=(m=String.fromCharCode,r="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+-$",i={},a={compressToEncodedURIComponent:function(e){return null==e?"":a._compress(e,6,function(e){return r.charAt(e)})},decompressFromEncodedURIComponent:function(t){return null==t?"":""==t?null:(t=t.replace(/ /g,"+"),a._decompress(t.length,32,function(e){return function(e,t){if(!i[e]){i[e]={};for(var n=0;n<e.length;n++)i[e][e.charAt(n)]=n}return i[e][t]}(r,t.charAt(e))}))},_compress:function(e,t,n){if(null==e)return"";var o,r,i,a={},s={},c="",f="",u="",l=2,h=3,p=2,d=[],g=0,v=0;for(i=0;i<e.length;i+=1)if(c=e.charAt(i),Object.prototype.hasOwnProperty.call(a,c)||(a[c]=h++,s[c]=!0),f=u+c,Object.prototype.hasOwnProperty.call(a,f))u=f;else{if(Object.prototype.hasOwnProperty.call(s,u)){if(u.charCodeAt(0)<256){for(o=0;o<p;o++)g<<=1,v==t-1?(v=0,d.push(n(g)),g=0):v++;for(r=u.charCodeAt(0),o=0;o<8;o++)g=g<<1|1&r,v==t-1?(v=0,d.push(n(g)),g=0):v++,r>>=1}else{for(r=1,o=0;o<p;o++)g=g<<1|r,v==t-1?(v=0,d.push(n(g)),g=0):v++,r=0;for(r=u.charCodeAt(0),o=0;o<16;o++)g=g<<1|1&r,v==t-1?(v=0,d.push(n(g)),g=0):v++,r>>=1}0==--l&&(l=Math.pow(2,p),p++),delete s[u]}else for(r=a[u],o=0;o<p;o++)g=g<<1|1&r,v==t-1?(v=0,d.push(n(g)),g=0):v++,r>>=1;0==--l&&(l=Math.pow(2,p),p++),a[f]=h++,u=String(c)}if(""!==u){if(Object.prototype.hasOwnProperty.call(s,u)){if(u.charCodeAt(0)<256){for(o=0;o<p;o++)g<<=1,v==t-1?(v=0,d.push(n(g)),g=0):v++;for(r=u.charCodeAt(0),o=0;o<8;o++)g=g<<1|1&r,v==t-1?(v=0,d.push(n(g)),g=0):v++,r>>=1}else{for(r=1,o=0;o<p;o++)g=g<<1|r,v==t-1?(v=0,d.push(n(g)),g=0):v++,r=0;for(r=u.charCodeAt(0),o=0;o<16;o++)g=g<<1|1&r,v==t-1?(v=0,d.push(n(g)),g=0):v++,r>>=1}0==--l&&(l=Math.pow(2,p),p++),delete s[u]}else for(r=a[u],o=0;o<p;o++)g=g<<1|1&r,v==t-1?(v=0,d.push(n(g)),g=0):v++,r>>=1;0==--l&&(l=Math.pow(2,p),p++)}for(r=2,o=0;o<p;o++)g=g<<1|1&r,v==t-1?(v=0,d.push(n(g)),g=0):v++,r>>=1;for(;;){if(g<<=1,v==t-1){d.push(n(g));break}v++}return d.join("")},_decompress:function(e,t,n){var o,r,i,a,s,c,f,u=[],l=4,h=4,p=3,d="",g=[],v={val:n(0),position:t,index:1};for(o=0;o<3;o+=1)u[o]=o;for(i=0,s=Math.pow(2,2),c=1;c!=s;)a=v.val&v.position,v.position>>=1,0==v.position&&(v.position=t,v.val=n(v.index++)),i|=(0<a?1:0)*c,c<<=1;switch(i){case 0:for(i=0,s=Math.pow(2,8),c=1;c!=s;)a=v.val&v.position,v.position>>=1,0==v.position&&(v.position=t,v.val=n(v.index++)),i|=(0<a?1:0)*c,c<<=1;f=m(i);break;case 1:for(i=0,s=Math.pow(2,16),c=1;c!=s;)a=v.val&v.position,v.position>>=1,0==v.position&&(v.position=t,v.val=n(v.index++)),i|=(0<a?1:0)*c,c<<=1;f=m(i);break;case 2:return""}for(r=u[3]=f,g.push(f);;){if(v.index>e)return"";for(i=0,s=Math.pow(2,p),c=1;c!=s;)a=v.val&v.position,v.position>>=1,0==v.position&&(v.position=t,v.val=n(v.index++)),i|=(0<a?1:0)*c,c<<=1;switch(f=i){case 0:for(i=0,s=Math.pow(2,8),c=1;c!=s;)a=v.val&v.position,v.position>>=1,0==v.position&&(v.position=t,v.val=n(v.index++)),i|=(0<a?1:0)*c,c<<=1;u[h++]=m(i),f=h-1,l--;break;case 1:for(i=0,s=Math.pow(2,16),c=1;c!=s;)a=v.val&v.position,v.position>>=1,0==v.position&&(v.position=t,v.val=n(v.index++)),i|=(0<a?1:0)*c,c<<=1;u[h++]=m(i),f=h-1,l--;break;case 2:return g.join("")}if(0==l&&(l=Math.pow(2,p),p++),u[f])d=u[f];else{if(f!==h)return null;d=r+r.charAt(0)}g.push(d),u[h++]=r+d.charAt(0),r=d,0==--l&&(l=Math.pow(2,p),p++)}}}),void 0===(o=function(){return window.LZString}.call(t,n,t,e))||(e.exports=o)}]);
   </script>
	<meta http-equiv="Content-Type" content="text/html;charset=UTF-8" />
	<meta name="format-detection" content="telephone=no" />
	<meta name="viewport" content="width=480,user-scalable=no" />

	<script src="../../js/vconsole.min.js"></script>
	<!--CSS-->
	<link rel="stylesheet" href="../../frame3/css/bingotouch.css" />
	<!-- 应用样式 -->
	<link rel="stylesheet" href="../../frame3/css/app.css" />
	<!-- frame 3.0 JS-->
	<script src="../../frame3/js/cordova.js"></script>
	<script src="../../frame3/js/zepto.js"></script>
	<script src="../../frame3/js/iscroll.js"></script>
	<script src="../../frame3/js/baiduTemplate.js"></script>
	<script src="../../frame3/js/bingotouch.js"></script>
	<script src="../../frame3/js/require.js"></script>

	<!-- 应用脚本 -->
	<script src="../../frame3/js/plugin/linkplugins.js"></script>
	<script src="../../frame3/js/app/app.js"></script>

	<script src="../../js/xh_public.js"></script>
	<script src="../../js/SzgaPlugin.js"></script>

	<!--引入本地JS-->
	<script src="url.js" type="text/javascript"></script>

	<title></title>
	<style type="text/css">

		.overflowText{
			overflow : hidden;
			text-overflow: ellipsis;
			display: -webkit-box;
			-webkit-line-clamp: 2;
			-webkit-box-orient: vertical;
		}
		.overflowTextOneline{
			overflow : hidden;
			text-overflow: ellipsis;
			display: -webkit-box;
			-webkit-line-clamp: 1;
			-webkit-box-orient: vertical;
		}
	</style>


	<script type="text/javascript">
		var exitFlag = false;
		var appCode = "com.xinghuo.question";//问题反馈的appCode
		var know_appUrl = "modules/question/know_info.html";//帮助文档的路径
		var question_appUrl = "modules/question/work_order_form.html";//问题反馈的路径

        var userInfo;
        var terminalId;
        var access_token;
        var deptCode;
		var pageNo = 1;
        var showType = 0;
		var mAppName = "通知公告";//本项目的名称
		app.page.onLoad = function() {
			document.addEventListener("deviceready", function() {
				document.addEventListener("backbutton", backFunc, false);
			}, false);
			testqu(function(type){
            if(type == 8){
							announcement_url =announcement_url_gm ;
            }else if(type == 5){
                announcement_url =announcement_url_lg ;
            }else{// 市纪委监委
               
						}
				})
            shuiyin();

            initScroll();

            app.getPageParams(function (result) {

                showType = result.showType;
                xh.getLoginInfo(function (result) {
                    userInfo = result;
//                    console.log(JSON.stringify(userInfo));
                    app.link.getUserInfo(function (res) {
                        deptCode = res.deptCode;
                        ui.showMask("请稍候...");
                        var params = {
                            loginId:userInfo.loginId
                        };
                        xh.jw_get(announcement_url + "sys/token",params,function (res) {
//                            console.log(JSON.stringify(res.returnValue));
                            var ret=eval('(' + res.returnValue + ')');
                            access_token = ret.access_token;
//                            console.log(access_token);
                            app.getMeid(function (result) {
                                terminalId = result;
                                requestData();
                            });
                        },function (err) {

                        },function () {

                        });

                        /* xh.getToken(userInfo.loginId, function (res) {
                             access_token = res.returnValue;
                             console.log(access_token);
                             app.getMeid(function (result) {
                                 terminalId = result;
                                 requestData();
                             });
                         })*/
                    }, function (res) {
//                        console.log(res);
                    }, userInfo.userId);
                });
            });

		};
		//返回退出
		function backFunc() {
			app.back('refreshRedPoint("notice")')
		}


        function initScroll() {
            mPullscroll = ui.IScroll.init(".pullrefresh-wrapper", {
                scrollBar: true,      //是否出现滚动条
                enablePullDown: false,   //是否允许下拉刷新
                enablePullUp: true,     //是否允许上拉加载
                //pullDownAction: getExamList,  //下拉刷新调用方法*/
                pullUpAction: pullupRefresh  //上拉加载调用方法
            });
            // $(".pullUp").hide();
						mPullscroll.refresh()
        }
        function pullupRefresh(refreshCallback) {
            pageNo = pageNo + 1;
            requestData(refreshCallback);
        }
		function requestData(refreshCallback) {

            var requestUrl = announcement_url + "oa/info/notifyQueryList";
//            console.log(requestUrl);
			var params = {
                pageNo: pageNo,
				pageSize: 10,
				type:3,
                showType: showType,
                access_token: access_token
			};
//			console.log(JSON.stringify(params));
			xh.jw_get(requestUrl,params,function (res) {
                ui.hideMask();
                var ret=eval('(' + res.returnValue + ')');
                // $(".pullUp").hide();
				renderAnnounce(ret.data.records,refreshCallback);
            },function (error) {
                ui.hideMask();
//                console.log(JSON.stringify(error));
            },function () {

            })
        }
        function renderAnnounce(result,refreshCallback) {
//			console.debug(JSON.stringify(result));
            var bt = baidu.template;
            var html = "";
            var length = result.length;
            if (length === 0 && pageNo > 1) {
				app.hint('没有更多信息...');
                pageNo = pageNo - 1;
			}

            for(var i=0;i<result.length;i++){
                var announce = result[i];
                html+=bt("announce_item",announce);
			}
            $("#announceList").append(html);
            $("#announceList").uiwidget();
						refreshCallback && refreshCallback();
                mPullscroll.refresh();
        }

        function loadAnnounceDetail(id,updateDate) {
            app.loadWithUrl("announcement_detail.html", {announcementId: id,access_token:access_token,updateDate:updateDate,detailType:"TONGZHI"});
        }
        function refresh() {
		    app.refresh()
        }

		  function refreshRedPoint(announcementId) {
			  $("."+announcementId).hide();
        }
		
	</script>

</head>

<body class="desktop" style="background: #fff">
<div id="section_container">

	<section id="index_section" class="active" style="background: #F0EFF5">

		<!--Header-->
		<!--Header-->
		<div class="header" data-fixed="top">
			<div class="title row-box" style="border: none;">
				<div class="box-left" style="background:  #d93a49">
					<div data-role="BTButton" data-type="image" onclick="backFunc()">
						<img src="../../css/images/icons/navicon/icon-back.png" alt=""/>
					</div>
				</div>
				<div class="span1" style="background:  #d93a49">
					<h1>通知公告</h1>
				</div>
				<div class="box-right" style="background: #d93a49">
					<div data-role="BTButton" data-type="image" onclick="app.refresh();">
						<img src="../../css/images/icons/navicon/icon-refresh.png" alt=""/>
					</div>
				</div>
			</div>
		</div>
			<!--Content-->
			<div class="content pullrefresh-wrapper">
				<div>
					<ul id="announceList" style="margin: 0;padding:10px 0 0;">

					</ul>
				</div>
		</div>
	</section>
</div>
<script type="text/html" id="announce_item">
	<li onclick="loadAnnounceDetail('<%=id%>','<%=updateDate%>')" style="margin: 0;padding: 0px 15px 10px 15px;">
		<div style="border-radius: 15px;background-color: white;padding: 10px 15px 10px 15px;">
			<div style="margin-top: 10px;display: flex;flex-direction: column;">
				<div style="display: flex;">
					<div class="overflowText" style="line-height: 35px;font-size: 30px;" >
						<%if(readFlag==0){%><span class="<%=id%>" style="display:inline-block; width: 15px;height: 15px;border-radius: 10px;background-color: red;"></span><%}%> <%=title%>
						<!--<%if(readFlag==0){%>-->
						<!--<img src="img/ic_unread.png" style="width: 60px;height: 60px;margin-left: 5px;"/>-->
						<!--<%}%>-->
					</div>
				</div>

				<div class="overflowText" style="margin-top: 10px;line-height: 30px" >
					<%=contentText%>
				</div>

				<div style="display: flex;flex-direction: row;margin-top: 10px;align-items: center">
					<!-- <div  style="background-color: #d93a49;color: white;padding: 5px;border-radius: 5px;text-align: center;height: 40px;line-height: 30px;" >
                        <%=typeDesc%>
                    </div> -->
					<div style="text-align: right;color: #8F9193;flex: 1;">
						<img src="img/ic_date.png" style="width: 25px;height: 25px"/> <%=updateDate%>
					</div>

					<div style="margin-left: 10px;">
						<img src="img/ic_arrow_right.png" style="width: 11px;height: 25px"/>
					</div>
				</div>

			</div>
		</div>

	</li>
</script>
</body>

</html>