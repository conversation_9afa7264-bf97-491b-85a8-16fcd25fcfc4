<!doctype html>
<html lang="zh" class="no-js">

<head>
    <script type="text/javascript">
         !function(n){var o={};function r(e){if(o[e])return o[e].exports;var t=o[e]={i:e,l:!1,exports:{}};return n[e].call(t.exports,t,t.exports,r),t.l=!0,t.exports}r.m=n,r.c=o,r.d=function(e,t,n){r.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:n})},r.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},r.t=function(t,e){if(1&e&&(t=r(t)),8&e)return t;if(4&e&&"object"==typeof t&&t&&t.__esModule)return t;var n=Object.create(null);if(r.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var o in t)r.d(n,o,function(e){return t[e]}.bind(null,o));return n},r.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return r.d(t,"a",t),t},r.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r.p="",r(r.s=0)}([function(e,t,n){e.exports=n(1)},function(e,t,n){var o,m,r,i,a;!function(E){sessionStorage||(E.sessionStorage={}),localStorage||(E.localStorage={}),E.webfunnyRequests||(E.webfunnyRequests=[]);var I=E.localStorage,S=I.WF_CONFIG?JSON.parse(I.WF_CONFIG):{s:!0,ia:[""],wc:40,pv:{s:true,ia:[""]},je:{s:true,ia:[""]},hl:{s:true,ia:[""],uh:!1,rl:500,sl:500},rl:{s:true,ia:[""]},bl:{s:true},lc:{s:true}},i=E.location.href.split("?")[0],a=performance&&performance.timing,x=performance&&"function"==typeof performance.getEntries?performance.getEntries():null,e="3.1.30",t=-1===E.location.href.indexOf("https")?"http://":"https://",s=E.location.href,b=encodeURIComponent(E.location.pathname),n=t+"10.248.97.236",C="/server/upLog",O="/server/upDLog",c=n+C,f=n+O,T="CUSTOMER_PV",o="STAY_TIME",r="CUS_LEAVE",u="LOAD_PAGE",_="HTTP_LOG",l="JS_ERROR",h="SCREEN_SHOT",p="ELE_BEHAVIOR",N="RESOURCE_LOAD",d="CUSTOMIZE_BEHAVIOR",g="VIDEOS_EVENT",M="LAST_BROWSE_DATE",$="WM_PAGE_ENTRY_TIME",k="WM_VISIT_PAGE_COUNT",L=new function(){this.checkIgnore=function(t,n){if(!n)return!0;try{for(var e=n.replace(/ /g,""),o=S[t].ia||[],r=!0,i=0;i<o.length;i++){var a=o[i].replace(/ /g,"");if(a&&-1!=e.indexOf(a)){r=!1;break}}return r}catch(e){console.error("checkIgnore异常，key: "+t+";str:"+n)}},this.getIp=function(n){if("1"!=L.getWfCookie("wf_cj_status"))if(L.getWfCookie("wf_ip"))"function"==typeof n&&n();else{var o=(new Date).getTime()+864e5;L.loadJs(t+"pv.sohu.com/cityjson?ie=utf-8",function(){if(E.returnCitySN){var e=E.returnCitySN?E.returnCitySN.cip:"",t=encodeURIComponent(E.returnCitySN?E.returnCitySN.cname:"");L.setWfCookie("wf_ip",e,o),L.setWfCookie("wf_prov",t,o),"function"==typeof n&&n()}},function(){L.setWfCookie("wf_cj_status",1,o),"function"==typeof n&&n()})}else"function"==typeof n&&n()},this.getUuid=function(){var e=L.formatDate((new Date).getTime(),"yMdhms");return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(e){var t=16*Math.random()|0;return("x"==e?t:3&t|8).toString(16)})+"-"+e},this.getCustomerKey=function(){var e=this.getUuid(),t=L.getWfCookie("monitorCustomerKey");if(!t){var n=(new Date).getTime()+31104e7;L.setWfCookie("monitorCustomerKey",e,n),t=e}return t},this.setWfCookie=function(e,t,n){var o={data:t,expires:n};if(I.WEBFUNNY_COOKIE){var r=JSON.parse(I.WEBFUNNY_COOKIE);r[e]=o,I.WEBFUNNY_COOKIE=JSON.stringify(r)}else{var i={};i[e]=o,I.WEBFUNNY_COOKIE=JSON.stringify(i)}},this.getWfCookie=function(e){var t=null;if(I.WEBFUNNY_COOKIE){var n=(t=JSON.parse(I.WEBFUNNY_COOKIE))[e];return n?parseInt(n.expires,10)<(new Date).getTime()?(delete t[e],I.WEBFUNNY_COOKIE=JSON.stringify(t),""):n.data:""}return""},this.getCusInfo=function(e){if(!e)return"";var t=(I.wmUserInfo?JSON.parse(I.wmUserInfo):{})[e];return t||""},this.getWebMonitorId=function(){var e=sessionStorage.CUSTOMER_WEB_MONITOR_ID||"webfunny_20231010_170506_pro";if(-1!==e.indexOf("_pro")){var t=L.getCusInfo("env");t&&(e=e.replace("_pro","_"+t))}return e},this.isTodayBrowse=function(e){var t=I[e],n=(new Date).getFullYear()+"-"+((new Date).getMonth()+1)+"-"+(new Date).getDate();return t&&n==t?!(!t||n!=t):(I[e]=n,!1)},this.formatDate=function(e,t){var n=new Date(e).getFullYear(),o=new Date(e).getMonth()+1,r=new Date(e).getDate(),i=new Date(e).getHours(),a=new Date(e).getMinutes(),s=new Date(e).getSeconds();return o=9<o?o:"0"+o,r=9<r?r:"0"+r,i=9<i?i:"0"+i,a=9<a?a:"0"+a,s=9<s?s:"0"+s,t.replace("y",n).replace("M",o).replace("d",r).replace("h",i).replace("m",a).replace("s",s)},this.getPageKey=function(){var e=this.getUuid();return I.monitorPageKey&&/^[0-9a-z]{8}(-[0-9a-z]{4}){3}-[0-9a-z]{12}-\d{13}$/.test(I.monitorPageKey)||(I.monitorPageKey=e),I.monitorPageKey},this.setPageKey=function(){I.monitorPageKey=this.getUuid()},this.addLoadEvent=function(e){var t=E.onload;"function"!=typeof E.onload?E.onload=e:E.onload=function(){t(),e()}},this.addOnBeforeUnloadEvent=function(e){var t=E.onbeforeunload;"function"!=typeof E.onbeforeunload?E.onbeforeunload=e:E.onbeforeunload=function(){t(),e()}},this.addOnclickForDocument=function(e){var t=document.onclick;"function"!=typeof document.onclick?document.onclick=e:document.onclick=function(){t(),e()}},this.ajax=function(e,t,n,o,r){try{var i=E.XMLHttpRequest?new XMLHttpRequest:new ActiveXObject("Microsoft.XMLHTTP");i.open(e,t,!0),i.setRequestHeader("Content-Type","application/x-www-form-urlencoded"),i.onreadystatechange=function(){if(4==i.readyState){var t={};try{t=i.responseText?JSON.parse(i.responseText):{}}catch(e){t={}}"function"==typeof o&&o(t)}},i.onerror=function(){"function"==typeof r&&r()};var a=JSON.stringify(n||{});i.send("data="+a)}catch(e){}},this.upLog=function(e,i){e&&"undefined"!=e&&L.ajax("POST",c,{logInfo:e},function(e){if(e&&e.data&&e.data.d){I.ds="c"==e.data.d?"connected":"disconnect";var t=e.data.c;if(t){I.setItem("WF_CONFIG",e.data.c);var n=JSON.parse(t);if(0==(S=n).s){var o=(new Date).getTime()+6e5;L.setWfCookie("webfunnyStart","p",o)}}}if(!0===i)for(var r=0;r<A.length;r++)I[A[r]]=""},function(){if(!0===i)for(var e=0;e<A.length;e++)I[A[e]]=""})},this.initDebugTool=function(){var a=L.getCusInfo("userId");function t(e){for(var t=[],n=e.length,o=0;o<n;o++)t.push(e[o]);var r={};r.log=t,r.userId=a,r.happenTime=(new Date).getTime();var i="";try{i=L.b64Code(JSON.stringify(r))}catch(e){i="convert fail"}return i}var n=console.log,o=console.warn;console.log=function(){var e=t(arguments);return"connected"===I.ds&&L.ajax("POST",f,{consoleInfo:e},function(){}),n.apply(console,arguments)},console.warn=function(){var e=t(arguments);return"connected"===I.ds&&L.ajax("POST",f,{warnInfo:e},function(){}),o.apply(console,arguments)}},this.uploadLocalInfo=function(){var e=L.getCusInfo("userId"),t={};for(var n in I)"function"==typeof I[n]||-1!=A.indexOf(n)||1e3<I[n].length||(t[n]=I[n]);try{t=L.b64Code(JSON.stringify(t))}catch(e){t=""}var o={};for(var n in sessionStorage)"function"==typeof sessionStorage[n]||-1!=A.indexOf(n)||1e3<sessionStorage[n].length||(o[n]=sessionStorage[n]);try{o=L.b64Code(JSON.stringify(o))}catch(e){o=""}var r=L.b64Code(document.cookie);L.ajax("POST",f,{localInfo:t,sessionInfo:o,cookieInfo:r,userId:e||"userId"},function(e){if((setTimeout(function(){L.uploadLocalInfo()},2e4),e.data)&&1==e.data.clear){var t=I.wmUserInfo;localStorage.clear(),localStorage.wmUserInfo=t,sessionStorage.clear(),I.WEBFUNNY_COOKIE=""}})},this.encryptObj=function(e){if(e instanceof Array){for(var t=[],n=0;n<e.length;++n)t[n]=this.encryptObj(e[n]);return t}if(e instanceof Object){t={};for(var n in e)t[n]=this.encryptObj(e[n]);return t}return 50<(e+="").length&&(e=e.substring(0,10)+"****"+e.substring(e.length-9,e.length)),e},this.getDevice=function(){var e={},t=navigator.userAgent,n=t.match(/(Android);?[\s\/]+([\d.]+)?/),o=t.match(/(iPad).*OS\s([\d_]+)/),r=!o&&t.match(/(iPhone\sOS)\s([\d_]+)/),i=t.match(/Android\s[\S\s]+Build\//),a=E.screen.width,s=E.screen.height;if(e.ios=e.android=e.iphone=e.ipad=e.androidChrome=!1,e.isWeixin=/MicroMessenger/i.test(t),e.os="web",e.deviceName="PC",e.deviceSize=a+"×"+s,n&&(e.os="android",e.osVersion=n[2],e.android=!0,e.androidChrome=0<=t.toLowerCase().indexOf("chrome")),(o||r)&&(e.os="ios",e.ios=!0),r&&(e.osVersion=r[2].replace(/_/g,"."),e.iphone=!0),o&&(e.osVersion=o[2].replace(/_/g,"."),e.ipad=!0),e.ios&&e.osVersion&&0<=t.indexOf("Version/")&&"10"===e.osVersion.split(".")[0]&&(e.osVersion=t.toLowerCase().split("version/")[1].split(" ")[0]),e.iphone){var c="iphone";320===a&&480===s?c="4":320===a&&568===s?c="5/SE":375===a&&667===s?c="6/7/8":414===a&&736===s?c="6/7/8 Plus":375===a&&812===s?c="X/S/Max":414===a&&896===s?c="11/Pro-Max":375===a&&812===s?c="11-Pro/mini":390===a&&844===s?c="12/Pro":428===a&&926===s&&(c="12-Pro-Max"),e.deviceName="iphone "+c}else if(e.ipad)e.deviceName="ipad";else if(i){for(var f=i[0].split(";"),u="",l=0;l<f.length;l++)-1!=f[l].indexOf("Build")&&(u=f[l].replace(/Build\//g,""));""==u&&(u=f[1]),e.deviceName=u.replace(/(^\s*)|(\s*$)/g,"")}if(-1==t.indexOf("Mobile")){var h=navigator.userAgent.toLowerCase();if(e.browserName="其他",0<h.indexOf("msie")){var p=h.match(/msie [\d.]+;/gi)[0];e.browserName="ie",e.browserVersion=p.split("/")[1]}else if(0<h.indexOf("edg")){p=h.match(/edg\/[\d.]+/gi)[0];e.browserName="edge",e.browserVersion=p.split("/")[1]}else if(0<h.indexOf("firefox")){p=h.match(/firefox\/[\d.]+/gi)[0];e.browserName="firefox",e.browserVersion=p.split("/")[1]}else if(0<h.indexOf("safari")&&h.indexOf("chrome")<0){p=h.match(/safari\/[\d.]+/gi)[0];e.browserName="safari",e.browserVersion=p.split("/")[1]}else if(0<h.indexOf("chrome")){p=h.match(/chrome\/[\d.]+/gi)[0];e.browserName="chrome",e.browserVersion=p.split("/")[1],0<h.indexOf("360se")&&(e.browserName="360")}}return e.webView=(r||o)&&t.match(/.*AppleWebKit(?!.*Safari)/i),e},this.loadJs=function(e,t,n){var o=document.createElement("script");o.async=1,o.src=e,o.onload=t,"function"==typeof n&&(o.onerror=n);var r=document.getElementsByTagName("script")[0];return r.parentNode.insertBefore(o,r),r},this.b64Code=function(e){var t=encodeURIComponent(e);try{return btoa(encodeURIComponent(t).replace(/%([0-9A-F]{2})/g,function(e,t){return String.fromCharCode("0x"+t)}))}catch(e){return t}}},D=(new Date).getTime()+6048e5,v=L.getDevice(),m=I.wmUserInfo?JSON.parse(I.wmUserInfo):{},A=[p,l,_,h,T,u,N,d,g],j={ACTIVE_TIME:{}},W=[];function y(){this.handleLogInfo=function(e,t){if(t){var n=I[e]?I[e]:"";switch(e){case p:I[p]=n+JSON.stringify(t)+"$$$";break;case l:I[l]=n+JSON.stringify(t)+"$$$";break;case _:I[_]=n+JSON.stringify(t)+"$$$";break;case h:I[h]=n+JSON.stringify(t)+"$$$";break;case T:I[T]=n+JSON.stringify(t)+"$$$";break;case u:I[u]=n+JSON.stringify(t)+"$$$";break;case N:I[N]=n+JSON.stringify(t)+"$$$";break;case d:I[d]=n+JSON.stringify(t)+"$$$";break;case g:I[g]=n+JSON.stringify(t)+"$$$"}}}}function w(){this.wmVersion=e,this.h=(new Date).getTime(),this.a=L.getWebMonitorId(),this.g=E.location.href.split("?")[0],this.f=L.b64Code(E.location.href),this.b=L.getCustomerKey(),this.c=m.userId,this.j=L.b64Code(m.projectVersion||""),this.d=L.b64Code(m.userTag||""),this.e=L.b64Code(m.secondUserParam||"")}function V(e,t,n,o,r){w.apply(this),this.i=e,this.k=L.getPageKey(),this.l=v.deviceName,this.deviceSize=v.deviceSize,this.m=v.os+(v.osVersion?" "+v.osVersion:""),this.n=v.browserName,this.o=v.browserVersion,this.p=L.getWfCookie("wf_ip"),this.q="",this.r=L.getWfCookie("wf_prov"),this.s="",this.t=t,this.u=n,this.newStatus=o,this.referrer=(r||"").split("?")[0]}function P(e){this.i=r,this.a=L.getWebMonitorId(),this.leaveType=e,this.h=(new Date).getTime(),this.g=E.location.href.split("?")[0],this.b=L.getCustomerKey()}function U(e,t){w.apply(this),this.i=o,this.h=(new Date).getTime(),this.a=L.getWebMonitorId(),this.g=E.location.href.split("?")[0],this.b=L.getCustomerKey(),this.stayTime=e,this.activeTime=t}function J(e,t,n,o,r,i,a,s,c,f,u,l){w.apply(this),this.i=e,this.t=t,this.v=n,this.w=o,this.x=r,this.y=i,this.z=a,this.A=s,this.B=c,this.C=f,this.D=u,this.E=l}function F(e,t,n,o,r,i,a){w.apply(this),this.i=e,this.da=t,this.G=L.b64Code(n),this.H=L.b64Code(o),this.I=L.b64Code(r),this.L=i,this.M=L.b64Code(a)}function R(e,t,n,o,r){w.apply(this),this.i=e,this.O=t,this.k=L.getPageKey(),this.l=v.deviceName,this.m=v.os+(v.osVersion?" "+v.osVersion:""),this.n=v.browserName,this.o=v.browserVersion,this.p=L.getWfCookie("wf_ip"),this.q="",this.r=L.getWfCookie("wf_prov"),this.s="",this.simpleErrorMessage=L.b64Code(n),this.P=L.b64Code(o),this.Q=L.b64Code(r),this.R=L.b64Code(navigator.userAgent)}function K(e,t,n,o,r,i,a,s,c,f,u){w.apply(this),this.i=e,this.method=t,this.g=n,this.S=L.b64Code(o),this.T=r,this.U=i,this.V=a,this.headerText="next version",this.W=L.b64Code(s),this.X=L.b64Code(c),this.h=f,this.u=u}function B(e,t,n,o){w.apply(this),this.i=e,this.Y=L.b64Code(t),this.Z=n,this.aa=o||"jpeg"}function Y(e,t,n,o){w.apply(this),this.i=e,this.ba=n,this.ca=L.b64Code(t),this.T=o}function q(e,t,n,o,r){this.c=e,this.a=L.getWebMonitorId(),this.da=t,this.ea=n,this.i=o,this.Y=r,this.h=(new Date).getTime()}function H(){var e=parseInt(I[$],10),t=(new Date).getTime()-e,n=L.getWfCookie("ACTIVE_TIME_INFO")||{},o=n.ACTIVE_TIME?1*n.ACTIVE_TIME[b]:t;0===o&&t<=1e4&&(o=t);var r=JSON.stringify(new U(t,o));navigator&&"function"==typeof navigator.sendBeacon&&navigator.sendBeacon(c,r)}function z(e){var t=S.lc;t&&!0===t.s&&L.getIp(),L.setPageKey();var n=L.isTodayBrowse(M),o=(new Date).getTime();I[$]=o;var r=null,i=L.formatDate(o,"y-M-d"),a=encodeURIComponent(E.location.href.split("?")[0]),s=I[k];if(s){var c=s.split("$$$"),f=c[0],u=c[1],l=parseInt(c[2],10);i==u?a!=f&&1==l&&(I[k]=a+"$$$"+i+"$$$2",r=new P(2)):(I[k]=a+"$$$"+i+"$$$1",r=new P(1))}else I[k]=a+"$$$"+i+"$$$1",r=new P(1);var h="";x&&(h=x[0]&&"navigate"===x[0].type?"load":"reload");var p=L.getWfCookie("monitorCustomerKey");if(p){var d="",g=p?p.match(/\d{14}/g):[];if(g&&0<g.length){var v=g[0].match(/\d{2}/g),m=v[0]+v[1]+"-"+v[2]+"-"+v[3]+" "+v[4]+":"+v[5]+":"+v[6],y=new Date(m).getTime(),w=(new Date).getTime();d=2e3<w-y?0==n?"o_uv":"o":"n_uv"}}else d="n_uv";var b=document.referrer;function C(n){var e=E.location.href;function t(){var e=new V(T,h,0,d,b),t=JSON.stringify(e)+"$$$";r&&(t+=JSON.stringify(r)+"$$$"),n?e.handleLogInfo(T,e):L.upLog(t,!1)}L.checkIgnore("pv",e)&&(L.getCusInfo("userId")?t():setTimeout(function(){t()},3e3))}var O=I.ds;O||!0!==t.s?("connected"===O&&L.initDebugTool(),setTimeout(function(){"connected"===O&&L.uploadLocalInfo()},2e3),C(e)):L.getIp(function(){C()})}function G(e,t,n,o,r,i){var a=t||"",s=i||"",c="",f="";if((0!==a.length||0!==s.length)&&(1e3<=a.length&&(a=a.substring(0,999)),3e3<=s.length&&(s=s.substring(0,2999)),80<=a.length?f=a.substring(0,80):0<a.length&&a.length<80&&(f=a),L.checkIgnore("je",a))){if(a)if("string"==typeof s)c=s.split(": ")[0].replace('"',"");else c=JSON.stringify(s).split(": ")[0].replace('"',"");var u=new R(l,e,c+": "+f,c+": "+a,s);u.handleLogInfo(l,u)}}V.prototype=new y,P.prototype=new y,U.prototype=new y,J.prototype=new y,F.prototype=new y,R.prototype=new y,K.prototype=new y,B.prototype=new y,Y.prototype=new y,q.prototype=new y,new y;for(var X=S.ia,Z=!1,Q=0;Q<X.length;Q++){var ee=X[Q].replace(/ /g,"");if(ee&&-1!=(E.location.href+E.location.hash).indexOf(ee)){Z=!0;break}}var te=L.getWfCookie("webfunnyStart")||S.s;te&&"p"!=te&&!Z&&function(){j.ACTIVE_TIME[b]=0,L.setWfCookie("ACTIVE_TIME_INFO",j,D);var d=new Date,g=d.getFullYear(),v=d.getMonth(),m=d.getDate();try{var e=S.pv,t=S.je,n=S.hl,o=S.rl,r=S.bl;e.s&&(z(),L.addLoadEvent(function(){setTimeout(function(){if(x){var e="load";e=x[0]&&"navigate"===x[0].type?"load":"reload";var t=a,n=new J(u);n.loadType=e,n.lookupDomain=t.domainLookupEnd-t.domainLookupStart,n.connect=t.connectEnd-t.connectStart,n.request=t.responseEnd-t.requestStart,n.ttfb=t.responseStart-t.navigationStart,n.domReady=t.domComplete-t.responseEnd,n.loadPage=t.loadEventEnd-t.navigationStart,n.redirect=t.redirectEnd-t.redirectStart,n.loadEvent=t.loadEventEnd-t.loadEventStart,n.appcache=t.domainLookupStart-t.fetchStart,n.unloadEvent=t.unloadEventEnd-t.unloadEventStart,n.handleLogInfo(u,n)}},1e3)}),function(){function e(e){var t=history[e],n=new Event(e);return function(){var e=t.apply(this,arguments);return n.arguments=arguments,E.dispatchEvent(n),e}}history.pushState=e("pushState"),history.replaceState=e("replaceState"),E.addEventListener("hashchange",function(){z(1)}),E.addEventListener("popstate",function(){var e=E.location.href.split("?")[0].split("#")[0];i!=e&&(z(0),i=e)}),E.addEventListener("pushState",function(e){z(0)}),E.addEventListener("replaceState",function(e){z(0)})}()),t.s&&function(){var o=console.error;console.error=function(e){var t=e&&e.message||e,n=e&&e.stack;if(n)G("on_error",t,s,0,0,n);else{if("object"==typeof t)try{t=JSON.stringify(t)}catch(e){t="错误无法解析"}G("console_error",t,s,0,0,"CustomizeError: "+t)}return o.apply(console,arguments)},E.onerror=function(e,t,n,o,r){G("on_error",e,t,n,o,r?r.stack:null)},E.onunhandledrejection=function(e){var t="",n="";n="object"==typeof e.reason?(t=e.reason.message,e.reason.stack):(t=e.reason,""),": "===t&&(t=n),G("on_error",t,s,0,0,"UncaughtInPromiseError: "+n)}}(),n.s&&function(){function t(e){var t=new CustomEvent(e,{detail:this});E.dispatchEvent(t)}var n=E.XMLHttpRequest;function r(e,t){if(p[e]&&!0!==p[e].uploadFlag){var n=S.hl,o=(parseInt(n.rl,10),parseInt(n.sl,10)||500),r="";if(t&&t.length<o)try{r=t}catch(e){r=""}else r="内容太长";var i=p[e].simpleUrl,a=(new Date).getTime(),s=p[e].event.detail.responseURL,c=p[e].event.detail.status,f=p[e].event.detail.statusText,u=a-p[e].timeStamp;if(s&&-1==s.indexOf(C)&&-1==s.indexOf(O)&&L.checkIgnore("hl",s)){var l=new K(_,"",i,s,c,f,"request","","",p[e].timeStamp,0),h=new K(_,"",i,s,c,f,"response","",r,a,u);W.push(l,h),p[e].uploadFlag=!0}}}var p=[];E.XMLHttpRequest=function(){var e=new n;return e.addEventListener("loadstart",function(){t.call(this,"ajaxLoadStart")},!1),e.addEventListener("loadend",function(){t.call(this,"ajaxLoadEnd")},!1),e},E.addEventListener("ajaxLoadStart",function(e){var t={timeStamp:(new Date).getTime(),event:e,simpleUrl:E.location.href.split("?")[0],uploadFlag:!1};p.push(t)}),E.addEventListener("ajaxLoadEnd",function(){for(var o=0;o<p.length;o++){if(!0!==p[o].uploadFlag)if(0<p[o].event.detail.status)if("blob"===(p[o].event.detail.responseType+"").toLowerCase())!function(t){var n=new FileReader;n.onload=function(){var e=n.result;r(t,e)};try{n.readAsText(p[o].event.detail.response,"utf-8")}catch(e){r(t,p[o].event.detail.response+"")}}(o);else try{var e=p[o]&&p[o].event&&p[o].event.detail;if(!e)return;var t=e.responseType,n="";""!==t&&"text"!==t||(n=e.responseText),"json"===t&&(n=JSON.stringify(e.response)),r(o,n)}catch(e){}}})}(),o.s&&E.addEventListener("error",function(e){var t=e.target.localName,n="";if("link"===t?n=e.target.href:"script"===t&&(n=e.target.src),n=n?n.split("?")[0]:"",L.checkIgnore("rl",n)&&-1==n.indexOf("pv.sohu.com/cityjson")){var o=new Y(N,n,t,"0");o.handleLogInfo(N,o)}},!0),r.s&&L.addOnclickForDocument(function(e){if(e){var t="",n="",o="",r=e.target.tagName,i="";"svg"!=e.target.tagName&&"use"!=e.target.tagName&&(t=e.target.className,n=e.target.placeholder||"",o=e.target.value||"",100<(i=e.target.innerText?e.target.innerText.replace(/\s*/g,""):"").length&&(i=i.substring(0,50)+" ... "+i.substring(i.length-49,i.length-1)),i=i.replace(/\s/g,""));var a=new F(p,"click",t,n,o,r,i);a.handleLogInfo(p,a)}}),L.addOnBeforeUnloadEvent(function(){H()});var y=0,w=A;setInterval(function(){var e=parseInt(S.wc||"40",10);if(e="connected"==I.ds?5:e,0<y&&y%5==0){if(10<=W.length){for(var t="",n=0;n<W.length;n++){var o=W[n];o&&(t+=JSON.stringify(o)+"$$$")}L.upLog(t,!1)}else{var r="";for(n=0;n<W.length;n++){var i=W[n];i&&(r+=JSON.stringify(i)+"$$$")}I[_]+=r,3e4<=I[_].length&&(L.upLog(I[_],!1),I[_]="")}W=[]}if(e<=y){var a="";for(n=0;n<w.length;n++)a+=I[w[n]]||"";0<a.length&&L.upLog(a,!0),y=0;var s=L.getWfCookie("ACTIVE_TIME_INFO")||{},c={};s.ACTIVE_TIME?c=s.ACTIVE_TIME:s.ACTIVE_TIME={};var f=c[b]||0;0<a.length&&(s.ACTIVE_TIME[b]=1*f+200*e),L.setWfCookie("ACTIVE_TIME_INFO",s,D);var u=new Date((new Date).getTime()+1e4),l=u.getFullYear(),h=u.getMonth(),p=u.getDate();(g<l||v<h||m<p)&&(H(),I[$]=(new Date).getTime(),d=new Date,g=d.getFullYear(),v=d.getMonth(),m=d.getDate())}y++},200)}catch(e){console.error("监控代码异常，捕获",e)}}(),E.webfunny={getCustomerKey:function(){return L.getCustomerKey()},wm_upload_picture:function(e,t,n){var o=new B(h,t,e,n||"jpeg");o.handleLogInfo(h,o)},wm_upload_extend_log:function(e,t,n,o,r){var i=new q(e,t,n,o,r);i.handleLogInfo(d,i)}},function(){if("function"==typeof E.CustomEvent)return;function e(e,t){t=t||{bubbles:!1,cancelable:!1,detail:void 0};var n=document.createEvent("CustomEvent");return n.initCustomEvent(e,t.bubbles,t.cancelable,t.detail),n}e.prototype=E.Event.prototype,E.CustomEvent=e}()}(window),window.LZString=(m=String.fromCharCode,r="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+-$",i={},a={compressToEncodedURIComponent:function(e){return null==e?"":a._compress(e,6,function(e){return r.charAt(e)})},decompressFromEncodedURIComponent:function(t){return null==t?"":""==t?null:(t=t.replace(/ /g,"+"),a._decompress(t.length,32,function(e){return function(e,t){if(!i[e]){i[e]={};for(var n=0;n<e.length;n++)i[e][e.charAt(n)]=n}return i[e][t]}(r,t.charAt(e))}))},_compress:function(e,t,n){if(null==e)return"";var o,r,i,a={},s={},c="",f="",u="",l=2,h=3,p=2,d=[],g=0,v=0;for(i=0;i<e.length;i+=1)if(c=e.charAt(i),Object.prototype.hasOwnProperty.call(a,c)||(a[c]=h++,s[c]=!0),f=u+c,Object.prototype.hasOwnProperty.call(a,f))u=f;else{if(Object.prototype.hasOwnProperty.call(s,u)){if(u.charCodeAt(0)<256){for(o=0;o<p;o++)g<<=1,v==t-1?(v=0,d.push(n(g)),g=0):v++;for(r=u.charCodeAt(0),o=0;o<8;o++)g=g<<1|1&r,v==t-1?(v=0,d.push(n(g)),g=0):v++,r>>=1}else{for(r=1,o=0;o<p;o++)g=g<<1|r,v==t-1?(v=0,d.push(n(g)),g=0):v++,r=0;for(r=u.charCodeAt(0),o=0;o<16;o++)g=g<<1|1&r,v==t-1?(v=0,d.push(n(g)),g=0):v++,r>>=1}0==--l&&(l=Math.pow(2,p),p++),delete s[u]}else for(r=a[u],o=0;o<p;o++)g=g<<1|1&r,v==t-1?(v=0,d.push(n(g)),g=0):v++,r>>=1;0==--l&&(l=Math.pow(2,p),p++),a[f]=h++,u=String(c)}if(""!==u){if(Object.prototype.hasOwnProperty.call(s,u)){if(u.charCodeAt(0)<256){for(o=0;o<p;o++)g<<=1,v==t-1?(v=0,d.push(n(g)),g=0):v++;for(r=u.charCodeAt(0),o=0;o<8;o++)g=g<<1|1&r,v==t-1?(v=0,d.push(n(g)),g=0):v++,r>>=1}else{for(r=1,o=0;o<p;o++)g=g<<1|r,v==t-1?(v=0,d.push(n(g)),g=0):v++,r=0;for(r=u.charCodeAt(0),o=0;o<16;o++)g=g<<1|1&r,v==t-1?(v=0,d.push(n(g)),g=0):v++,r>>=1}0==--l&&(l=Math.pow(2,p),p++),delete s[u]}else for(r=a[u],o=0;o<p;o++)g=g<<1|1&r,v==t-1?(v=0,d.push(n(g)),g=0):v++,r>>=1;0==--l&&(l=Math.pow(2,p),p++)}for(r=2,o=0;o<p;o++)g=g<<1|1&r,v==t-1?(v=0,d.push(n(g)),g=0):v++,r>>=1;for(;;){if(g<<=1,v==t-1){d.push(n(g));break}v++}return d.join("")},_decompress:function(e,t,n){var o,r,i,a,s,c,f,u=[],l=4,h=4,p=3,d="",g=[],v={val:n(0),position:t,index:1};for(o=0;o<3;o+=1)u[o]=o;for(i=0,s=Math.pow(2,2),c=1;c!=s;)a=v.val&v.position,v.position>>=1,0==v.position&&(v.position=t,v.val=n(v.index++)),i|=(0<a?1:0)*c,c<<=1;switch(i){case 0:for(i=0,s=Math.pow(2,8),c=1;c!=s;)a=v.val&v.position,v.position>>=1,0==v.position&&(v.position=t,v.val=n(v.index++)),i|=(0<a?1:0)*c,c<<=1;f=m(i);break;case 1:for(i=0,s=Math.pow(2,16),c=1;c!=s;)a=v.val&v.position,v.position>>=1,0==v.position&&(v.position=t,v.val=n(v.index++)),i|=(0<a?1:0)*c,c<<=1;f=m(i);break;case 2:return""}for(r=u[3]=f,g.push(f);;){if(v.index>e)return"";for(i=0,s=Math.pow(2,p),c=1;c!=s;)a=v.val&v.position,v.position>>=1,0==v.position&&(v.position=t,v.val=n(v.index++)),i|=(0<a?1:0)*c,c<<=1;switch(f=i){case 0:for(i=0,s=Math.pow(2,8),c=1;c!=s;)a=v.val&v.position,v.position>>=1,0==v.position&&(v.position=t,v.val=n(v.index++)),i|=(0<a?1:0)*c,c<<=1;u[h++]=m(i),f=h-1,l--;break;case 1:for(i=0,s=Math.pow(2,16),c=1;c!=s;)a=v.val&v.position,v.position>>=1,0==v.position&&(v.position=t,v.val=n(v.index++)),i|=(0<a?1:0)*c,c<<=1;u[h++]=m(i),f=h-1,l--;break;case 2:return g.join("")}if(0==l&&(l=Math.pow(2,p),p++),u[f])d=u[f];else{if(f!==h)return null;d=r+r.charAt(0)}g.push(d),u[h++]=r+d.charAt(0),r=d,0==--l&&(l=Math.pow(2,p),p++)}}}),void 0===(o=function(){return window.LZString}.call(t,n,t,e))||(e.exports=o)}]);
    </script>
    <meta charset="UTF-8">
    <meta http-equiv="Content-Type" content="text/html;charset=UTF-8" />
    <meta name="viewport" content="width=480,user-scalable=no" />
    <!--CSS-->
    <link rel="stylesheet" href="../../frame3/css/bingotouch.css" />
    <link rel="stylesheet" href="../../frame3/css/app.css" />
    <link rel="stylesheet" href="css/skin_1.css" type="text/css" id="skin_style" />
    <!--JS-->
    <script src="../../js/vconsole.min.js"></script>
    <script src="../../frame3/js/cordova.js"></script>
    <script src="../../frame3/js/zepto.js"></script>
    <!--<script src="../../frame3/js/iscroll.js"></script>-->
    <script src="../../frame3/js/baiduTemplate.js"></script>
    <script src="../../frame3/js/bingotouch.js"></script>
    <script src="../../frame3/js/plugin/linkplugins.js"></script>
    <script src="../../frame3/js/app/app.js"></script>
    <script src="js/index.js"></script>
    <script src="../../js/xh_public.js"></script>
    <script src="../../js/SzgaPlugin.js"></script>
    <title>纪委2.0首页</title>
    <!--<link rel="stylesheet" type="text/css" href="css/component.css"/>-->
    <!--<script src="js/modernizr-custom.js"></script>-->
</head>

<body style=" margin:0;overflow-x: hidden;">
    <div style="background: #f5f5f5;overflow-x: hidden;">
        <div class="onlineerro" style="display:none;position: relative;height:70px;line-height: 70px;color:#333;background-color:#F6EBE6;padding-left:70px; ">
            <span class="icon1" style="background: url('./img/gth1.png') no-repeat center center;background-size: 30px 30px;width: 35px;height: 35px;display: inline-block;position: absolute;top: 17px;left: 30px"></span>
            <span class="text"> 当前网络不可用，请检查网络设置</span>
        </div>
        <!-- <div style="position: absolute;left:55px;top:27px;width:40px;height:40px;" id="dblclickaotu"></div> -->

        <!--<div class="desktop-body" style="margin-top: 74px;width: 515px;">-->
        <div class="desktop-body" style="width: 101%;">

            <div style="text-align: center; margin-top: -60px;">
                <img style="width: 80%;" src='img/banner_title.png'/>
            </div>

            <!--头像名字 电话刷新-->
            <div class="row-box" style="width: 100%;display: flex;margin-bottom: 15px;position: relative;margin-top: -10px;">
                <div class="bottomLogo" style="width: 100%;height:40px;position: absolute;top:12px;text-align: center;">
                    <!-- <div
                        style="display:inline-block;background: url('./image/logo.png') no-repeat center center;background-size: 250px 30px;width:100%;height:40px;margin:  0 auto;opacity: 0.5;">
                    </div> -->
                </div>

                <div class="user-img" style="margin-left: 8px; z-index:2;">
                    <img id="imgAvatar" style="width: 47px;height: 47px;border-radius: 47px;margin-top: 8px;"
                        onerror="onerror=null;src='img/ic_avatar.png'" src="img/ic_avatar.png" />
                </div>

                <div class="user-show"
                    style="z-index:1;margin-top:13px;background-color: rgba(85, 85, 85, 0.4);display: flex;height: 30px; border-radius: 0px 20px 20px 0px;padding-left:25px;padding-right: 10px;align-items: center;position: relative;left: -23px;top:4px;">
                    <div id="user_name" style="padding: 0; margin: 0;font-size: 20px;color: #fff;font-weight: normal;display: inline-block;">
                        
                    </div>
                </div>

                <div onclick="iphone()"
                    style="position:absolute;right:13%;top:-18px;font-size:20px;text-align: center;color:#ff0000;margin-top:30px;display: inline-block;z-index:89;">
                    <img src="img/iphone1.png" style="width:27px;height:27px;" alt="" />
                </div>
                <div onclick="window.location.reload()"
                    style="position:absolute;right:5%;top:-18px;font-size:20px;text-align: center;color:#ff0000;margin-top:30px;display: inline-block;z-index:89;">
                    <img src="img/shuaxin.png" style="width:27px;height:27px;" alt="" />
                </div>

                <!--<div style="flex: 1;justify-content: flex-end;display: flex;align-items: center;margin-right: 30px;border: 1px solid #000;">-->
                <!--<div data-role="BTButton" data-type="image" onclick="app.refresh();">-->
                <!--<img src="img/shuaxin.png" style="width:28px;height:28px;" alt=""/>-->
                <!--</div>-->
                <!--</div>-->
            </div>

            <div style="text-align: center;margin-top: -20px;">
                <img style="width: 96%;" src='img/banner_tips.png'/>
            </div>

            <!-- <div style="background-color: rgba(85, 85, 85, 0.4);display: flex;height: 30px;width: 97%;padding-left: 5px;border-radius: 20px 20px 20px 20px;align-items: center;position: relative;left: 5px;right: 5px;bottom: 5px;">
                <div style="font-size: 21px;color: #fff;font-weight: normal;display: inline-block;">
                    本平台为政务外网非涉密平台，严禁处理、传输国家秘密。
                </div>
            </div> -->

            <div
                style="flex-direction:column;margin:0px 10px 5px 10px;padding-bottom: 10px;padding-top: 5px;overflow: hidden;border-radius: 15px;display: flex;background-color: white;">
                <div style="padding-left:15px;">
                    <div class="row-box">
                        <div class="box-left" onclick="tapAnnouncement(1);">
                            <img style="width:97px;height: 22px; " src="img/tongzhigonggao.png" alt="" />
                            <span id="announceUnreadCount"
                                style="margin-left: 10px;color: white;background-color: red;padding: 0 10px 0 10px;border-radius: 15px;display: none">
                            </span>
                        </div>
                        <div class="span1">
                        </div>
                        <div class="box-right" style="font-size: 18px;color: #90909a;margin-top: 3px;">
                            <span style="margin-right:13px;" onclick="tapAnnouncement(0);">
                                全部
                                <img style="width: 18px;height: 15px;margin-left: 0px;margin-top: -3px;"
                                    src="img/14.png" alt="" />
                            </span>
                        </div>
                    </div>
                </div>
                <div id="announcement" style="display: flex;flex-direction: column;width: 100%;">

                </div>
            </div>
            <!--通知公告-->
            <script type="text/html" id="announcement_entry">
            <div style="display: flex;border-top: 1px solid #EEE;line-height: 50px;height: 50px;"
                 onclick="loadAnnounceDetail('<%=id%>')">
                    <div class="shenglvhao" style="color: #8F9193;padding-left: 10px;">
                        <%=updateDate%>
                    </div>
                    <div class="shenglvhao"
                         style="flex:1;text-align: left;padding-left: 10px;font-size: 24px;width: 100%;">
                        <%=title%>
                    </div>
            </div>
        </script>

            <div class="desktop-content" style="margin-top:25px;">
                <!--待办事项与我的应用模板-->
                <script type="text/html" id="info_column_empty">
                    <div class="info-column">
                        <div class="row-box info-column-header">
                            <div class="info-column-header-center box-left" value="<%=programCode%>" name="<%=programName%>">
                                <span class="font-size-22 font-color-333333">
                                    <%if (programName=="待办事项"){%>
                                        <img style="width:97px;height: 22px; " src="img/daibanshixiang.png">
                                        <span id="couledaiban"
                                            style="margin-left: 10px;color: white;background-color: red;padding: 0 10px 0 10px;border-radius: 15px;display: none">
                                        </span>
                                        <img id="imgRefreshTodo" class="loading1 loading2" style="width:22px;height:22px;margin-left:10px;display: none;" src="img/jiazai.png">

                                    <!-- <div>
                                        <img style="width:97px;height: 22px; " src="img/daibanshixiang.png">
                                        <img id="imgRefreshTodo" class="loading1 loading2" style="width:22px;height:22px;margin-left:10px" src="img/shuaxin.png">
                                    </div>
                                    <div>
                                        <img style="width:97px;height: 22px; " src="img/daibanshixiang.png">
                                        <span style="width:50px;height:28px;margin-left: 10px;color: white;background-color: red;border-radius: 15px;">
                                            &nbsp;
                                            <img id="imgRefreshTodo" class="loading1 loading2" style="width:20px;height:20px;margin-left:1px;margin-bottom:2px" src="image/refresh.jpg">
                                            &nbsp;
                                        </span>
                                    </div>
                                    <div>
                                        <img style="width:97px;height: 22px; " src="img/daibanshixiang.png">
                                        <img id="imgRefreshTodo" class="loading1 loading2" style="width:22px;height:22px;margin-left:10px" src="img/jiazai.png">
                                    </div> -->
                                <%}else if(programName=="我的应用"){%>
                                    <img style="width:97px;height: 22px;" src="img/myApp.png">
                                <%}else{%>
                                <%=programName%>
                                <%}%>
							</span>
                        </div>
                        <div class="span1"></div>
                        <div class="info-column-header-right" value="<%=programCode%>">
                            <div class="box-right" style="font-size: 18px;color: #90909a;margin-top: 3px;">
                                <%if (programName=="待办事项"){%>
                                <%}%>
                                <%if(programName=="我的应用"){%>
                                <div style="height: 100%;width: 40%;margin-left: 60%;padding-right: 10px;display:none"
                                     onclick="showOrHideAllIcon('<%=programCode%>');">
                                    <img src="image/icon_down.jpg"/>
                                </div>
                                <%}%>
                            </div>
                        </div>
                    </div>
                    <div class="info-column-content" value="<%=programCode%>">
                        <!--显示待办或我的应用内容-->
                    </div>
                </div>
            </script>

                <script type="text/html" id="user_info">
                <div class="row-box" style="padding-left: 20px;padding-bottom: 20px;">
                    <div style="width: 90px;">
                        <% if (userPhotoPath == "") { %>
                        <img src="image/default_user_photo.png" width="90" height="90" style="border-radius: 45px;"/>
                        <% } else { %>
                        <img src="<%=userPhotoPath%>" width="90" height="90" style="border-radius: 45px;"/>
                        <% } %>
                    </div>
                    <div style="width: 70%;padding-left: 20px;">
                        <div class="row-box">
                            <div style="padding-top: 8px;">
                                <span class="font-color-333333 font-size-25"><%=userName%></span>
                            </div>
                            <div style="padding-top: 10px;padding-left: 15px;">
                                <span class="font-size-16 font-color-1a5df9"
                                      style="background: #c6d5f9;border-radius: 20px;padding: 5px 15px 5px 15px;"><%=userPosition%></span>
                            </div>
                        </div>
                        <div>
                            <span class="font-size-16 font-color-8c8c8c"><%=deptName%></span>
                        </div>
                    </div>
                </div>
            </script>

                <script type="text/html" id="info_column_content_todo_icon_table">
                <!-- <table class="info-column-content-icon-table" value="<%=programCode%>">
                    <% for (var i = 0; i * lineNum < allList.length; i++) { %>
                    <tr value="hidden">
                        <% for (var j = 0; j < lineNum; j++) { %>
                        <td>
                            <% if (i * lineNum + j < allList.length) { %>
                                <div style="position: relative;">
                                    <% if (allList[i * lineNum + j].pendingCount > 0) { %>
                                                <%if(allList[i * lineNum + j].moduleName=="待阅公文"){%>
                                                <div class="icon-badge dyhongdian" style="border-radius:6px;min-width:12px;height:12px;">
                                                </div>
                                                <%}else{%>
                                                    <div class="icon-badge <%=allList[i * lineNum + j].moduleName%>">
                                                        <%=allList[i * lineNum + j].pendingCount%>
                                                    </div>
                                                <% } %>
                                      <% } %>
                                <img src="<%=allList[i * lineNum + j].appIcon%>" style="width: 40px;height: 40px;margin-bottom: 5px;"
                                     onclick="openAppById('<%=allList[i * lineNum + j].appCode%>','<%=allList[i * lineNum + j].appEntry%>','<%=allList[i * lineNum + j].moduleName%>','<%=allList[i * lineNum + j].appVersion%>');"/>
                            </div>
                          <div class="font-size-16 font-color-333333" style="text-align: center">
                                <div style="overflow: hidden;text-overflow:ellipsis;white-space: nowrap;">
                                    <%=allList[i * lineNum + j].moduleName%>
                                </div>
                            </div>
                            <% } %>
                        </td>
                        <% } %>
                    </tr>
                    <% } %>
                </table>
                <table class="info-column-content-icon-table" value="<%=programCode%>">
                    <% for (var i = 0; i * lineNum < allList.length; i++) { %>
                    <tr value="hidden">
                        <% for (var j = 0; j < lineNum; j++) { %>
                        <td>
                            <% if (i * lineNum + j < allList.length) { %>
                                <div style="position: relative;">
                                    <% if (allList[i * lineNum + j].pendingCount > 0) { %>
                                                <%if(allList[i * lineNum + j].moduleName=="待阅公文"){%>
                                                <div class="icon-badge dyhongdian" style="border-radius:6px;min-width:12px;height:12px;">
                                                </div>
                                                <%}else{%>
                                                    <div class="icon-badge <%=allList[i * lineNum + j].moduleName%>" style="text-align:center">
                                                        &nbsp;&nbsp;?
                                                    </div>
                                                <% } %>
                                      <% } %>
                                <img src="<%=allList[i * lineNum + j].appIcon%>" style="width: 40px;height: 40px;margin-bottom: 5px;"
                                     onclick="openAppById('<%=allList[i * lineNum + j].appCode%>','<%=allList[i * lineNum + j].appEntry%>','<%=allList[i * lineNum + j].moduleName%>','<%=allList[i * lineNum + j].appVersion%>');"/>
                            </div>
                          <div class="font-size-16 font-color-333333" style="text-align: center">
                                <div style="overflow: hidden;text-overflow:ellipsis;white-space: nowrap;">
                                    <%=allList[i * lineNum + j].moduleName%>
                                </div>
                            </div>
                            <% } %>
                        </td>
                        <% } %>
                    </tr>
                    <% } %>
                </table> -->
                <!-- <table class="info-column-content-icon-table" value="<%=programCode%>">
                    <% for (var i = 0; i * lineNum < allList.length; i++) { %>
                    <tr value="hidden">
                        <% for (var j = 0; j < lineNum; j++) { %>
                        <td>
                            <% if (i * lineNum + j < allList.length) { %>
                                <div style="position: relative;">
                                    <% if (allList[i * lineNum + j].pendingCount > 0) { %>
                                                <%if(allList[i * lineNum + j].moduleName=="待阅公文"){%>
                                                <div class="icon-badge dyhongdian" style="border-radius:6px;min-width:12px;height:12px;">
                                                </div>
                                                <%}else{%>
                                                <div class="icon-badge"  style="border-radius:18px;min-width:18px;height:18px;">
                                                    <img id="imgRefreshTodo" class="loading1 loading2" style="width:15px;height:15px;margin-left:1px;margin-bottom:0;margin-top:1px" src="image/refresh.jpg">
                                                </div>
                                                <% } %>
                                      <% } %>
                                <img src="<%=allList[i * lineNum + j].appIcon%>" style="width: 40px;height: 40px;margin-bottom: 5px;"
                                     onclick="openAppById('<%=allList[i * lineNum + j].appCode%>','<%=allList[i * lineNum + j].appEntry%>','<%=allList[i * lineNum + j].moduleName%>','<%=allList[i * lineNum + j].appVersion%>');"/>
                            </div>
                          <div class="font-size-16 font-color-333333" style="text-align: center">
                                <div style="overflow: hidden;text-overflow:ellipsis;white-space: nowrap;">
                                    <%=allList[i * lineNum + j].moduleName%>
                                </div>
                            </div>
                            <% } %>
                        </td>
                        <% } %>
                    </tr>
                    <% } %>
                </table> -->
                <table class="info-column-content-icon-table" value="<%=programCode%>">
                    <% for (var i = 0; i * lineNum < allList.length; i++) { %>
                    <tr value="hidden">
                        <% for (var j = 0; j < lineNum; j++) { %>
                        <td>
                            <% if (i * lineNum + j < allList.length) { %>
                                <div style="position: relative;">
                                    <% if (allList[i * lineNum + j].pendingCount > 0) { %>
                                                <%if(allList[i * lineNum + j].moduleName=="待阅公文"){%>
                                                <div class="icon-badge dyhongdian" style="border-radius:6px;min-width:12px;height:12px;">
                                                </div>
                                                <%}else{%>
                                                <!-- <div class="icon-badge"  style="border-radius:6px;min-width:12px;height:12px;"> -->
                                                    <!-- &nbsp; -->
                                                    <!-- <img id="imgRefreshTodo" class="loading1 loading2" style="width:10px;height:10px;margin-left:1px;margin-bottom:2px" src="image/refresh.jpg"> -->
                                                    <!-- &nbsp; -->
                                                <!-- </div> -->
                                                <!-- <div class="icon-badge" style="display:none">
                                                    <img id="img_refresh_todo_item" class="loading1 loading2" style="width:12px;height:12px;margin-left:10px" src="img/shuaxin.png">
                                                </div> -->
                                                
                                                <%if(isShowItemButNoNum){%>
                                                    <div class="icon-badge divTodoItem <%=allList[i * lineNum + j].moduleName%>" style="display:none;">
                                                        <%=allList[i * lineNum + j].pendingCount%>
                                                    </div>
                                                <%}else{%>
                                                    <div class="icon-badge divTodoItem <%=allList[i * lineNum + j].moduleName%>" style="display:flex;">
                                                        <%=allList[i * lineNum + j].pendingCount%>
                                                    </div>
                                                <%}%>

                                                <div class="icon-badge2 imgRefreshTodoItem"  style="border-radius:18px;min-width:18px;height:18px;background-color:white;display:none;">
                                                    <img class="loading1 loading2" style="width:15px;height:15px;margin-left:1px;margin-bottom:0;margin-top:1px" src="img/jiazai.png">
                                                </div>
                                                <% } %>
                                      <% } %>
                                <img src="<%=allList[i * lineNum + j].appIcon%>" style="width: 40px;height: 40px;margin-bottom: 5px;"
                                     onclick="openAppById('<%=allList[i * lineNum + j].appCode%>','<%=allList[i * lineNum + j].appEntry%>','<%=allList[i * lineNum + j].moduleName%>','<%=allList[i * lineNum + j].appVersion%>');"/>
                            </div>
                          <div class="font-size-16 font-color-333333" style="text-align: center">
                                <div style="overflow: hidden;text-overflow:ellipsis;white-space: nowrap;">
                                    <%=allList[i * lineNum + j].moduleName%>
                                </div>
                            </div>
                            <% } %>
                        </td>
                        <% } %>
                    </tr>
                    <% } %>
                </table>
            </script>

                <script type="text/html" id="icon_table_row">
                <tr value="<%=value%>" style="<% if(value=='hidden'){ %>display:none;<%} %>">
                    <td class="<%=columnName%>" value="<%=lineIndex*lineNum%>">

                    </td>
                    <td class="<%=columnName%>" value="<%=lineIndex*lineNum+1%>">

                    </td>
                    <td class="<%=columnName%>" value="<%=lineIndex*lineNum+2%>">

                    </td>
                    <td class="<%=columnName%>" value="<%=lineIndex*lineNum+3%>">

                    </td>
                </tr>
            </script>

                <script type="text/html" id="icon_table_grid">
                <div>
                    <img class="appiconimg" src="<%=jmtAppIcon%>" style="width: 40px;height: 40px;margin-bottom: 5px;"
                         onerror="onerror=null;src='./img1/Cv1wGlz9sy6AGlvpAAANx3GGeiQ513.png';"
                         onclick="openAppById('<%=appCode%>','<%=appEntry%>','<%=appName%>','<%=version%>');"/>
                    <!--<img src="<%=jmtAppIcon%>" style="width: 40px;height: 40px;margin-bottom: 5px;" onclick="openApp('<%=appCode%>','<%=appEntry%>','<%=appName%>','<%=version%>');"/>-->
                </div>
                 <div class="font-size-16 font-color-333333">
                    <div style="overflow: hidden;text-overflow:ellipsis;white-space: nowrap;">
                        <%=appName%>
                    </div>
                </div>
            </script>

                <div id="desktopContent">
                    &nbsp;
                </div>

                <!--工作动态-->
                <div
                    style="display:flex;flex-direction:column;margin:25px 10px 25px 10px;border-radius: 15px;background:white;padding-bottom: 10px;padding-top: 5px">
                    <div style="padding-left:15px;">
                        <div class="row-box">
                            <div class="box-left" onclick="tapWorkMoment(1);">
                                <img style="width:97px;height: 22px; " src="img/workstatus.png" alt="" />
                                <span id="workUnreadCount"
                                    style="margin-left: 10px;color: white;background-color: red;padding: 0 10px 0 10px;border-radius: 15px;display: none;">
                                </span>
                            </div>
                            <div class="span1">
                            </div>
                            <div class="box-right" style="font-size: 18px;color: #90909a;margin-top: 3px;">
                                <span style="margin-right:13px;" onclick="tapWorkMoment(0);">
                                    全部 <img style="width: 18px;height: 15px;margin-left: 0px;margin-top: -3px;"
                                        src="img/14.png" alt="" />
                                </span>

                            </div>
                        </div>
                    </div>
                    <div>
                        <ul id="workMomentList" class="con_end" style="width: 100%;margin: 0;padding: 0">

                        </ul>
                    </div>
                    <script type="text/html" id="work_moment_item">
                    <li onclick="loadWorkMomentDetail('<%=id%>')"
                        style="overflow: hidden;margin: 0;border-top: 1px solid #EEEEEE;padding: 5px 15px;">
                        <div style="display:flex;line-height: 35px;height: 35px;">
                            <div class="shenglvhao" style="color: #8F9193;">
                                <%=updateDate%>
                            </div>
                            <div class="overflowTextOneline"
                                 style="font-size:24px;line-height: 30px;flex: 1;align-self: center;margin-left: 10px;">
                                <%=title%>
                            </div>
                            <!--<div style="text-align: center;align-self: center">-->
                            <!--<img style="width: 9px;height: 18px;margin-left: 5px"-->
                            <!--src="img/ic_arrow_right.png"/>-->
                            <!--</div>-->
                        </div>

                    </li>
                </script>
                </div>

                <div id="desktopContent1">
                    &nbsp;
                </div>
            </div>
            <!-- <div class="onlineerro"
                style="display:none;position: relative;height:70px;line-height: 70px;color:#333;background-color:#F6EBE6;padding-left:70px; ">
                <span class="icon1" style="background: url('./img/gth1.png') no-repeat center center;background-size: 30px 30px;
            width: 35px;height: 35px;display: inline-block;position: absolute;top: 17px;left: 30px"></span>
                <span class="text"> 当前网络不可用，请检查网络设置</span>
            </div> -->

            <div id="test_app" style="display: none;">
                <script type="text/html" id="info_column_test_app">
                <div class="info-column">
                    <div class="row-box info-column-header">
                        <!--<div class="info-column-header-left">-->
                        <!--<img src="image/icon_use_most.png"/>-->
                        <!--</div>-->
                        <div class="info-column-header-center">
                            <span class="font-size-22 font-color-333333"><%=appClassifyName%>（测试应用）</span>
                        </div>
                        <div class="info-column-header-right">

                        </div>
                    </div>
                    <div class="info-column-content">
                        <table class="info-column-content-icon-table" value="<%=programCode%>">
                            <% for (var i = 0; i * lineNum < appList.length; i++) { %>
                            <tr>
                                <% for (var j = 0; j < lineNum; j++) { %>
                                <td>
                                    <% if (i * lineNum + j < appList.length) { %>
                                    <div style="position: relative;">
                                        <img src="<%=appList[i * lineNum + j].jmtAppIcon%>"
                                             style="width: 40px;height: 40px;margin-bottom: 5px;"
                                             onclick="openAppById('<%=appList[i * lineNum + j].appCode%>','<%=appList[i * lineNum + j].appEntry%>','<%=appList[i * lineNum + j].appName%>','<%=appList[i * lineNum + j].version%>');"/>
                                    </div>
                                    <div class="font-size-16 font-color-333333">
                                        <div style="width: 100%;overflow: hidden;text-overflow:ellipsis;white-space: nowrap;">
                                            <%=appList[i * lineNum + j].appName%>
                                        </div>
                                    </div>
                                    <% } %>
                                </td>
                                <% } %>
                            </tr>
                            <% } %>
                        </table>
                    </div>
                </div>
            </script>

                <div id="testAppContent">

                </div>
            </div>
        </div>
    </div>
</body>

</html>
<!--<script src="js/classie.js"></script>-->
<!--<script src="js/dynamics.min.js"></script>-->
<!--<script src="js/main.js"></script>-->
<!--<script src="hightcharts/highcharts.js"></script>-->
<!--引用url定义文件-->

<!--dialog控件所需脚本和样式 -->
<!--<script src="../../js/ui-expand.js"></script>-->
<script src="url.js" type="text/javascript"></script>
<!--<link rel="stylesheet" href="../../css/ui-expand.css"/>-->
<script type="text/javascript">
    // var vConsole = new VConsole();
    var loginId;
    var userName;
    var deptName;
    var userPosition;
    var userPhotoPath;
    var mUserInfo = null;
    var userId;
    var column_line_icon_number = 4;
    var daibancount = 0
    var programConfigList = {
        "20190325141821591": {
            // 待办事项
            loadData: function (programCode) {
                console.log(getTime() + 'window.onload-重新加载待办')

                showCacheTodoList(programCode, true, false);
                showRefreshIcon();
                setTimeout(function () {
                    // getToDoListOnReload(programCode, false, false);
                    getToDoListOnHomePageResume(programCode, false, false);
                }, 600)    
            },
            apps: null,
            show: false,
            todoList: []
        },
        "20190325141832479": {
            // 我的应用
            loadData: function (programCode) {
                getAllApp(programCode);
            },
            apps: null,
            show: false,
            lines: 5,
            showAllIcon: false
        }
    };
    showOrHideAllIcon("20190325141832479")
    var programDisplayList = [];
    var requestCount = 0;
    var firstLoadTodo = true;
    var userPhotoList = {};
    var hasDialogOpen = false;
    var dialogId = "";
    var exitFlag = false;
    var deviceType = "";
    var enableOpenApp = true;
    var userAppData = [];
    var apptrue = true;
    var isonline = true;
    var shifushuxing = true
    try {
        setTimeout(function () {
            if (shifushuxing) {
                window.location.reload();
            }
        }, 3000)

        window.onload = function () {
            var ISReload = false;//重加载标识
            var appobj = {
                initialize: function () {
                    shifushuxing = false
                    this.bindEvents();
                },
                bindEvents: function () {
                    document.addEventListener('deviceready', this.onDeviceReady, false);
                    //延时100毫秒后进行重加载
                    setTimeout(function () {
                        if (!ISReload) {
                            window.location.reload();//重加载
                        }
                    }, 100)
                },
                onDeviceReady: function () {
                    ISReload = true;//更新重加载标识
                    appobj.receivedEvent('deviceready');
                },
                receivedEvent: function (id) {
                    //获取流程相关的数据
                    try {
                        document.addEventListener("backbutton", backFunc, false);
                    } catch (e) {
                        // console.log(getTime() + "erro")
                        window.location.reload();//重加载
                    }
                    //声明页面事件
                    document.addEventListener("resume", function () {
                        // console.debug("resume");
                        getAnnouncementToken();
                    }, false);
                    var getLoginInfo1 = function (callback) {
                        var successCallback = function (result) {
                            callback(app.utils.toJSON(result));
                        };
                        Cordova.exec(successCallback, null, "LinkPlugin", "getLoginInfo", []);
                    }
                    getLoginInfo1(function (res) {
                        loginId = res.loginId;
                        userName = res.userName;
                        userId = res.userId;
                        getAnnouncementToken();
                        //            1.0

                        var getUserInfo1 = function (callback, failCallback, userId) {
                            var successCallback = function (result) {
                                callback(app.utils.toJSON(result));
                            };
                            Cordova.exec(successCallback, failCallback, "LinkPlugin", "getUserInfo", [userId]);
                        }
                        getUserInfo1(function (res) {
                            $("#user_name").html(res.userName);
                            if (res.userPhotoPath) {
                                if (res.userPhotoPath.search('http://10.253.112.8:80') != -1) {
                                    $("#imgAvatar").attr("src", res.userPhotoPath.replace("http://10.253.112.8:80", 'http://10.248.97.236:8008'));
                                }
                                if (res.userPhotoPath.search('http://172.28.0.56:9000') != -1) {
                                    $("#imgAvatar").attr("src", res.userPhotoPath.replace("http://172.28.0.56:9000", 'http://10.248.97.236:8008'));
                                }
                                if (res.userPhotoPath.search('http://10.224.182.141') != -1) {
                                    $("#imgAvatar").attr("src", res.userPhotoPath.replace("http://10.224.182.141", 'http://10.248.97.236'));
                                }
                                if (res.userPhotoPath.search('https://10.224.182.141:8890') != -1) {
                                    $("#imgAvatar").attr("src", res.userPhotoPath.replace("https://10.224.182.141:8890", 'https://10.248.97.236:8008'));
                                }
                                if (res.userPhotoPath.search('https://10.224.182.141:8008') != -1) {
                                    $("#imgAvatar").attr("src", res.userPhotoPath.replace("https://10.224.182.141:8008", 'https://10.248.97.236:8008'));
                                }
                                if (res.userPhotoPath.search('http://10.248.97.236:12001') != -1) {
                                    $("#imgAvatar").attr("src", res.userPhotoPath.replace("http://10.248.97.236:12001", 'http://10.248.97.237:12001'));
                                }
                            }
                        }, function (res) {
                            console.error(res);
                        }, userId);


                        getProgramConfig();

                        getUserTestApps();

                        // $("#dblclickaotu").dblclick(function () {
                        //     app.loadWithUrl("about.html", {});
                        // })
                    });
                    shuiyin();

                    xh.userTag('com.page.index', function (tag) {
                        // console.log("-----------------tag： " + tag);
                        window.localStorage.wmUserInfo = JSON.stringify({ userId: loginId, userTag: tag, projectVersion: '1.0.1', env: 'pro' })
                    });
                }

            }
            testqu(function (type) {
                //    ORG_SHENZHEN = 0,
                //            ORG_FUTIAN = 1,
                //            ORG_LUOHU = 2,
                //            ORG_YANTIAN = 3,
                //            ORG_NANSHAN = 4,
                //            ORG_LONGGANG = 5,
                //            ORG_LONGHUA = 6,
                //            ORG_PINGSHAN = 7,
                //            ORG_GUANGMING = 8,
                //            ORG_DAPENG = 9,
                //            ORG_SHENSHAN = 10,
                //            ORG_OTHERS = 11;
                if (type == 8) {
                    $(".desktop-body").addClass("guangming")
                    $('.bottomLogo').hide()
                    announcement_url = announcement_url_gm;
                } else if (type == 5) {
                    $(".desktop-body").addClass("longgang")
                    $('.bottomLogo').hide()
                    announcement_url = announcement_url_lg;                
                } else if (type == 10) {
                    $(".desktop-body").addClass("shenshan")
                    $('.bottomLogo').hide()
                } else {// 市纪委监委
                    $('.bottomLogo').show()
                }
            })
            // 初始化
            appobj.initialize()
        };
    } catch (e) {
        // console.log(getTime() + "onloaderro")
        setTimeout(function () {
            window.location.reload();
        }, 1000)
    }

    //安卓切换首页时调用！！！来更新代办的红点数量。
    function onHomePageResume() {
        console.log(getTime() + 'onResume-触发更新')

        showRefreshIcon();

        setTimeout(function () {
            getToDoListOnHomePageResume("20190325141821591", false, false);
        }, 600)
    }

    // 定时调用刷新待办
    // setInterval(function () {
    //     if(isonline){
    //         console.log(getTime() + '定时5S-触发更新')
    //         getToDoList("20190325141821591", true, false);
    //     }
    //     refreshAdrredNums()
    // }, 5 * 1000)

    // FXP
    function showRefreshIcon(){
        console.log(getTime() + '---------------------showRefreshIcon\n')

        $("#couledaiban").hide();
        $(".divTodoItem").hide();
        $("#imgRefreshTodo").show();
        $(".imgRefreshTodoItem").show();
    }

    function hideRefreshIcon(){
        console.log(getTime() + '---------------------hideRefreshIcon\n')

        $("#imgRefreshTodo").hide();
        $(".imgRefreshTodoItem").hide();
        $("#couledaiban").show();
        $(".divTodoItem").show();
    }

   
// 发送给安卓总数
    function refreshAdrredNums() {
        let count = daibancount + announceUnreadCount + workUnreadCount
        // console.log(getTime() + 'count==========', count)
        try {
            Cordova.exec(function(e){console.log(e)}, function(){}, "Page", "updateHomePageUnreadCount", [count+""]);
        } catch (e) {
            
        }
    }


    //安卓调用！！！来更新代办的红点数量。
    function onDesktopMsgReceived(symbolName, params) {
        console.log(getTime() + 'onDesktopMsgReceived-触发更新')

        // console.log(getTime() + symbolName, params)
        switch (symbolName) {
            case "pendingInfo":
                // console.log(getTime() + "onDesktopMsgReceived pendingInfo")
                saveparams(params)
                break;
        //     case "notice":
        //         getAnnouncementToken("notice");
        //         break;
        //     case "workDynamic":
        //         getAnnouncementToken("workDynamic");
        //         break;
        //     case "returnCar":
        //         break;
        }
    }

    var dbParams
    var swParams
    var clParams

    var timeout
    function saveparams(params) {
        try {
            var sxList = eval(params)
            if (sxList[0].moduleName == '待办公文') {
                dbParams = params;
            } else if (sxList[0].moduleName == '工作事务') {
                swParams = params;
            } else if (sxList[0].moduleName == '公务车辆') {
                clParams = params;
            }

            timeout && clearInterval(timeout)
            timeout = setTimeout(() => {
                // console.log(getTime() + '收到推送-触发更新')
                // refreshToDoList("20190325141821591", dbParams)
                // refreshToDoList("20190325141821591", swParams)
                // refreshToDoList("20190325141821591", clParams)
                getToDoList("20190325141821591", false, false);
            }, 500);

        } catch (e) {
            // console.log(getTime() + e)
        }
    }

    //安卓调用！
    function onNetDisconnected() {
        console.log('网络变化-onNetDisconnected')

        isonline = false
        app.hint("网络断开了")

        // showRefreshIcon()

        $("#couledaiban").hide();
        $(".divTodoItem").hide();
        $("#imgRefreshTodo").show();
        // $(".imgRefreshTodoItem").show();

        $('.onlineerro').show()
    }

    function onNetConnected() {
        console.log('网络变化-onNetConnected')

        isonline = true
        app.hint('网络已连接')
        $('.onlineerro').hide()
        window.location.reload();
    }

    //1.0
    function getProgramConfig() {
        $("#desktopContent").html("&nbsp;");
        //      获取本地配置信息 然后设置
        getProgramConfigLocal();
        //  加载线上配置
        //  getProgramConfigRemote();
    }
    //1.5  获取本地配置信息 然后设置
    var bendiconfig = ""
    function getProgramConfigLocal() {
        var configuredProgram = [
            {
                "id": "7947ffc6-5d27-45fe-a7e2-a667f4d2bde7", "programCode": "20190325141821591",
                "programName": "待办事项",
                "iconUrl": "/filestore/2019/4/3/program/2019431039438220.png",
                "createTime": "2019-03-25 14:18:22", "createUserCode": "wangcan", "programDes": "待办事项",
                "lastUpdateTime": "2019-04-03 10:39:43", "lastUpdateUserCode": "zhangxl",
                "orderNo": 20190325141821590, "showLimit": 1, "apps": null
            },
            {
                "id": "119ac393-c92e-4e8e-811b-891120c147a6", "programCode": "20190325141832479",
                "programName": "我的应用", "iconUrl": "/filestore/2019/5/29/program/20195291625507850.png",
                "createTime": "2019-03-25 14:18:33", "createUserCode": "wangcan",
                "programDes": "我的应用",
                "lastUpdateTime": "2019-05-29 16:22:48",
                "lastUpdateUserCode": "taojunru",
                "orderNo": 20190325141832480, "showLimit": 3, "apps": null
            }]
        setProgramConfig(configuredProgram);
        // Cordova.exec(function (result) {
        //         if (result && result != "") {
        //         bendiconfig =result
        //         var configuredProgram = eval(result);

        //         setProgramConfig(configuredProgram);
        //     }
        // }, null, "Setting", "getProgramConfig", [userId]);
    }
    //2.0   加载线上配置
    function getProgramConfigRemote() {
        var param = {
            URL_TYPE: "JMT",
            userCode: loginId
        };
        xh.jw_post(zf_url + "/msa_xhsy_program/programConfig/queryProgram", param, function (res) {
            var result = eval('(' + res.returnValue + ')');

            // console.log(getTime() + "/msa_xhsy_program/programConfig/queryProgram ", result)
            if (result.code != "00000") {
                return;
            }
            var config = JSON.stringify(result.data.configuredProgram);
            Cordova.exec(function () {
            }, null, "Setting", "saveProgramConfig", [userId, config]);
            //本地的配置不等于线上配置时执行线上的
            if (config != bendiconfig) {
                setProgramConfig(result.data.configuredProgram);
            }
        }, function (res) {
        });
    }
    //3.0 设置配置待办事项和我的应用
    function setProgramConfig(configuredProgram) {
        for (var key in programConfigList) {
            programConfigList[key].show = false;
        }
        programDisplayList = [];
        for (var i = 0; i < configuredProgram.length; i++) {
            if (programConfigList[configuredProgram[i].programCode] == undefined) {
                continue;
            }

            programConfigList[configuredProgram[i].programCode].show = true;//配置里有的设置show是true。没有的是默认false
            programConfigList[configuredProgram[i].programCode].showLimit = configuredProgram[i].showLimit;

            programDisplayList[programDisplayList.length] = {
                programCode: configuredProgram[i].programCode,
                programName: configuredProgram[i].programName,
                iconUrl: configuredProgram[i].iconUrl
            };
        }
        var bt = baidu.template;
        var html = "";
        var bt1 = baidu.template;
        var html1 = "";
        //循环将待办事项我的应用头部 和容器渲染出来。
        for (var i = 0; i < programDisplayList.length; i++) {
            if (i == 0) {
                html += bt("info_column_empty", programDisplayList[i]);
                $("#desktopContent").html(html);
            } else {
                html1 += bt1("info_column_empty", programDisplayList[i]);
                $("#desktopContent1").html(html1);
            }
        }
        //根据配置项循环调用接口 的获取数据。
        for (var codeNumber in programConfigList) {
            if (programConfigList[codeNumber].show) {
                programConfigList[codeNumber].loadData(codeNumber);
            }
        }
    }

    
    function getToDoListOnReload(programCode, isTimedRefresh, isRequestRetry) {
        var param = {
            userId: userId,
            access_token: AnnouncementToken
        };
        var reqUrl = "http://10.248.97.236/supp/httpClient/xinghuo-admin/act/task/toDoTaskCountForPush?userId=" + userId + "&access_token=" + AnnouncementToken;

        // console.log(getTime() + reqUrl)
        app.ajax({
            url: reqUrl,
            data: param,
            timeout: 30000,
            method: "POST",
            contentType: "application/json",
            headers: {
                contentType: "application/json"
            },
            async: true,
            success: function (res) {
                console.log(getTime() + '---------------------request tododata success\n')
                var result = eval('(' + res.returnValue + ')');
                result = result.obj;
                result = JSON.parse(result);
                // console.log(getTime() + '---------------------result：', JSON.stringify(result))
                if (result != null && result.code == 200 && result.data.length > 0) {
                    showRequestTodoList(result, programCode, isonline, isTimedRefresh)
                    settodoData(result)
                    let pendingCount = 0
                    result.data.forEach(function (item) {
                        if (item.moduleName != '待阅公文') {
                            pendingCount += item.count
                        }
                    })
                    daibancount = pendingCount
                }
            },
            fail: function (res) {
                console.log(getTime() + '---------------------fail\n')

                if (requestCount >= 2) {
                    console.log(getTime() + '---------------------fail - requestCount >= 2\n')
                    return;
                }
                requestCount++;

                getToDoList(programCode, false, true);
            }
        });
    }

    function getToDoListOnHomePageResume(programCode, isTimedRefresh, isRequestRetry) {
        var param = {
            userId: userId,
            access_token: AnnouncementToken
        };
        var reqUrl = "http://10.248.97.236/supp/httpClient/xinghuo-admin/act/task/toDoTaskCountForPush?userId=" + userId + "&access_token=" + AnnouncementToken;

        showCacheTodoList(programCode, true, isTimedRefresh);

        // console.log(getTime() + reqUrl)
        app.ajax({
            url: reqUrl,
            data: param,
            timeout: 30000,
            method: "POST",
            contentType: "application/json",
            headers: {
                contentType: "application/json"
            },
            async: true,
            success: function (res) {
                console.log(getTime() + '---------------------request tododata success\n')
                var result = eval('(' + res.returnValue + ')');
                result = result.obj;
                result = JSON.parse(result);
                // console.log(getTime() + '---------------------result：', JSON.stringify(result))
                if (result != null && result.code == 200 && result.data.length > 0) {
                    showRequestTodoList(result, programCode, isonline, isTimedRefresh)
                    settodoData(result)
                    let pendingCount = 0
                    result.data.forEach(function (item) {
                        if (item.moduleName != '待阅公文') {
                            pendingCount += item.count
                        }
                    })
                    daibancount = pendingCount
                }
            },
            fail: function (res) {
                console.log(getTime() + '---------------------fail\n')

                if (requestCount >= 2) {
                    console.log(getTime() + '---------------------fail - requestCount >= 2\n')
                    return;
                }
                requestCount++;

                getToDoList(programCode, false, true);
            }
        });
    }

    function getToDoList(programCode, isTimedRefresh, isRequestRetry) {
        console.log(getTime() + '---------------------getToDoList\n')
        console.log(getTime() + '---------------------isTimedRefresh: ' + isTimedRefresh + '\n')
        console.log(getTime() + '---------------------isRequestRetry: ' + isRequestRetry + '\n')

        if(!isTimedRefresh){
            if(!isRequestRetry){
                showRefreshIcon();
            }
        } else {
            if(isonline){
                hideRefreshIcon();
            }
        }

        var param = {
            userId: userId,
            access_token: AnnouncementToken
        };
        var reqUrl = "http://10.248.97.236/supp/httpClient/xinghuo-admin/act/task/toDoTaskCountForPush?userId=" + userId + "&access_token=" + AnnouncementToken;

        showCacheTodoList(programCode, true, isTimedRefresh);

        // console.log(getTime() + reqUrl)
        app.ajax({
            url: reqUrl,
            data: param,
            timeout: 30000,
            method: "POST",
            contentType: "application/json",
            headers: {
                contentType: "application/json"
            },
            async: true,
            success: function (res) {
                console.log(getTime() + '---------------------request tododata success\n')
                var result = eval('(' + res.returnValue + ')');
                result = result.obj;
                result = JSON.parse(result);
                // console.log(getTime() + '---------------------result：', JSON.stringify(result))
                if (result != null && result.code == 200 && result.data.length > 0) {
                    showRequestTodoList(result, programCode, isonline, isTimedRefresh)
                    settodoData(result)
                    let pendingCount = 0
                    result.data.forEach(function (item) {
                        if (item.moduleName != '待阅公文') {
                            pendingCount += item.count
                        }
                    })
                    daibancount = pendingCount
                }
            },
            fail: function (res) {
                console.log(getTime() + '---------------------fail\n')

                if (requestCount >= 2) {
                    console.log(getTime() + '---------------------fail - requestCount >= 2\n')
                    return;
                }
                requestCount++;

                getToDoList(programCode, false, true);
            }
        });
    }

    function isTodoNumChanged(result){
        // console.log(getTime() + '---------------------isTodoNumChanged\n')

        var isChanged = false;
        var localData = gettodoData();
        if(localData != null){
            if(result != null){
                // console.log(getTime() + '---------------------old：' + JSON.stringify(localData) + '\n')
                // console.log(getTime() + '---------------------new：' + JSON.stringify(result) + '\n')
                for (var i = 0; i < result.data.length; i++) {
                    // console.log(getTime() + '---------------------old ' + localData.data[i].moduleName + ' - ' + localData.data[i].count + '\n')
                    // console.log(getTime() + '---------------------new ' + result.data[i].moduleName + ' - ' + result.data[i].count + '\n')

                    if(localData.data[i].count != result.data[i].count){
                        isChanged = true;
                        break;
                    }
                }
            }
        } else {
            if(result != null){
                isChanged = true;
            }
        }
        if(isChanged){
            console.log(getTime() + '---------------------TodoNum is Changed\n')
        } else {
            console.log(getTime() + '---------------------TodoNum Not Changed\n')
        }
        return isChanged;
    }

    function showCacheTodoList(programCode, isShowItemButNoNum, isTimedRefresh) {
        console.log(getTime() + '---------------------showCacheTodoList\n')
        console.log(getTime() + '---------------------isShowItemButNoNum: ' + isShowItemButNoNum + '\n')

        let result = gettodoData();
        if (result != null) {
            console.log(getTime() + '---------------------Cache Todo List Not Null\n')
            try {
                programConfigList[programCode].todoList = [];
                for (var i = 0; i < result.data.length; i++) {
                    result.data[i].appIcon = result.data[i].iconUrl;
                    result.data[i].appEntry = result.data[i].url;
                    result.data[i].pendingCount = result.data[i].count;
                    programConfigList[programCode].todoList[i] = result.data[i];
                }
                var list = programConfigList[programCode].todoList
                list.forEach(function (val) {
                    var a = val.appIcon.split('/')
                    var imgName = a[a.length - 1];
                    val.appIcon = './img1/' + imgName
                    if (val.appCode == 'GWCL') {
                        val.appIcon = './img1/Cv3Le13boxOACk6cAAAI8UTAuHA998.png'
                    }
                })
                setToDoView(programCode, isShowItemButNoNum, isTimedRefresh);
            } catch (e) {
                console.log(getTime() + '---------------------catch：' + e)
            }
        } else {
            console.log(getTime() + '---------------------Cache Todo List is null\n')
        }
    }

    function showRequestTodoList(result, programCode, isShowItemButNoNum, isTimedRefresh) {
        console.log(getTime() + '---------------------showRequestTodoList\n')
        console.log(getTime() + '---------------------isShowItemButNoNum: ' + isShowItemButNoNum + '\n')

        if(!isTodoNumChanged(result)){
            console.log(getTime() + '---------------------return，不再重新渲染待办\n')
            return;
         }

         try {
            programConfigList[programCode].todoList = [];
            for (var i = 0; i < result.data.length; i++) {
                result.data[i].appIcon = result.data[i].iconUrl;
                result.data[i].appEntry = result.data[i].url;
                result.data[i].pendingCount = result.data[i].count;
                programConfigList[programCode].todoList[i] = result.data[i];
                console.log(getTime() + '---------------------' + result.data[i].moduleName + '：' + result.data[i].count)
            }
            var list = programConfigList[programCode].todoList
            list.forEach(function (val) {
                var a = val.appIcon.split('/')
                var imgName = a[a.length - 1];
                val.appIcon = './img1/' + imgName
                if (val.appCode == 'GWCL') {
                    val.appIcon = './img1/Cv3Le13boxOACk6cAAAI8UTAuHA998.png'
                }
            })
            setToDoView(programCode, isShowItemButNoNum, isTimedRefresh);
            firstLoadTodo = false;
        } catch (e) {
            console.log(getTime() + '---------------------catch：' + e)
        }
    }

    //    渲染待办视图
    function setToDoView(programCode, isShowItemButNoNum, isTimedRefresh) {
        console.log(getTime() + '---------------------setToDoView\n')

        //        var appList = [];
        var allList = [];
        for (var i = 0; i < programConfigList[programCode].todoList.length; i++) {
            if (programConfigList[programCode].todoList[i].pendingCount > 0) {//待办数>0
                //                appList.push(programConfigList[programCode].todoList[i]);
                allList.push(programConfigList[programCode].todoList[i]);
            }
        }
        for (var i = 0; i < programConfigList[programCode].todoList.length; i++) {
            if (programConfigList[programCode].todoList[i].pendingCount <= 0) {
                allList.push(programConfigList[programCode].todoList[i]);
            }
        }
        var bt = baidu.template;
        var html = bt("info_column_content_todo_icon_table", {
            programCode: programCode,
            //            appList: appList,
            allList: allList,
            isShowItemButNoNum: isShowItemButNoNum,
            lineNum: column_line_icon_number
        });

        // console.log(getTime() + '---------------------html：' + html + '\n')

        $(".info-column-content[value='" + programCode + "']").html(html);
        $(".info-column-content[value='" + programCode + "']").uiwidget();

        var coulca = 0;
        $(".info-column-content[value='" + programCode + "'] .icon-badge").each(function () {
            if ($(this).html() != "" && $(this).html().trim() != "") {
                coulca += parseInt($(this).html());
                // console.log(getTime() + '---------------------todo：' + parseInt($(this).html()) + '\n')
            }
        })

        $("#couledaiban").html(coulca)
        console.log(getTime() + '---------------------total：' + coulca + '\n')

        if(isonline){
            hideRefreshIcon();
            if (coulca < 1) {
                $("#couledaiban").hide();
            }
        }
    }

    function getTime(){
        // var date = new Date();
        // return date.getHours() + ':' + date.getMinutes() + ':' + date.getSeconds() + ':' + date.getMilliseconds()
        return "";
    }

    var allApp;
    //  5.0  通过接口获取应用列表
    function getAllApp(programCode) {
        $(".info-column-content[value='" + programCode + "']").html('<table class="info-column-content-icon-table" id="icon_table_myapp" value="' + programCode + '"></table>');
        getProgramContentLocal(programCode, function (res) {
            if (res && res != "") {
                //                修改缓存的图片
                var result = eval('(' + res + ')');
                var list = result.data[0].appList
                //                var val = list[0]
                list.forEach(function (val) {
                    var a = val.jmtAppIcon.split('/')
                    var imgName = a[a.length - 1];
                    val.jmtAppIcon = './img1/' + imgName
                })
                result.data[0].appList = list
                setAllAppList(programCode, JSON.stringify(result));
            }
        });
        var param = {
            APP_URL: applist_url + "list",
            userId: userId
        };
        xh.jw_post(applist_url + "list", param, function (res) {
            // console.log(getTime() + 'list', res)
            setAllAppList(programCode, res.returnValue);
            saveProgramContentLocal(programCode, res.returnValue);
        }, function (res) {
        });
    }



    // 6  渲染我的应用列表
    function setAllAppList(programCode, resValue) {
        var result = eval('(' + resValue + ')');
        userAppData = result.data;
        for (var Q = 0; Q < result.data.length; Q++) {
            allApp = result.data[Q].appList;
        }
        var showCount = allApp.length;
        var setHide = false;//是否隐藏，应用数量超过显示的数量showLimit行数 * column_line_icon_number个数就隐藏其余的
        if (showCount > programConfigList[programCode].showLimit * column_line_icon_number) {
            showCount = programConfigList[programCode].showLimit * column_line_icon_number;
            setHide = true;
        }
        var iconCount = showCount + 1;

        var bt = baidu.template;
        var html = "";
        for (var i = 0; i < programConfigList[programCode].showLimit; i++) {
            if (setHide) {
                html += bt("icon_table_row", {
                    value: "shown",
                    columnName: "my-app",
                    lineIndex: i,
                    lineNum: column_line_icon_number
                });
            } else {
                html += bt("icon_table_row", {
                    value: "",
                    columnName: "my-app",
                    lineIndex: i,
                    lineNum: column_line_icon_number
                });
            }

            if ((i + 1) * column_line_icon_number >= iconCount) {
                break;
            }
        }
        $("#icon_table_myapp").html(html);

        for (var i = 0; i < showCount; i++) {
            if (allApp[i].appName != "常用电话") {
                html = bt("icon_table_grid", allApp[i]);
                $(".my-app[value='" + i + "']").html(html);
            }
        }



        if (setHide) {
            var allIconCount = allApp.length + 1;
            var allHtml = "";
            for (var i = 0; ; i++) {
                allHtml += bt("icon_table_row", {
                    value: "hidden",
                    columnName: "my-app",
                    lineIndex: i,
                    lineNum: column_line_icon_number
                });

                if ((i + 1) * column_line_icon_number >= allIconCount) {
                    break;
                }
            }
            $("#icon_table_myapp").append(allHtml);
            for (var i = 0; i < allApp.length; i++) {
                allHtml = bt("icon_table_grid", allApp[i]);
                $("tr[value='hidden'] .my-app[value='" + i + "']").html(allHtml);
            }
        }
        //            $("#icon_table_myapp").uiwidget();
        $(".info-column-content[value='" + programCode + "']").uiwidget();
        if (programConfigList[programCode].showAllIcon) {
            $(".info-column-content-icon-table[value='" + programCode + "'] tr[value='shown']").hide();
            $(".info-column-content-icon-table[value='" + programCode + "'] tr[value='hidden']").show();
        }
    }

    //7 获取测试app
    function getUserTestApps() {
        var param = {
            APP_URL: applist_url + "tsetList",
            userCode: loginId
        };
        // console.log(getTime() + "getUserTestApps - applist_url：" + applist_url);
        xh.jw_post(applist_url + "tsetList", param, function (res) {
            var result = eval('(' + res.returnValue + ')');
            if (result.success != true) {
                return;
            }
            var bt = baidu.template;
            var html = "";
            try {
                for (var i = 0; i < result.data.length; i++) {
                    if (result.data[i].appList.length != 0) {
                        result.data[i].lineNum = column_line_icon_number;
                        html += bt("info_column_test_app", result.data[i]);
                    }
                }
            } catch (error) {
                
            }
            if (html == "") {
                return;
            }
            $("#testAppContent").html(html);
            $("#test_app").show();
        }, function (res) {
            // console.log(getTime() + "getUserTestApps - applist_url：" + JSON.stringify(res));
        });
    }

    //显示全部还是
    function showOrHideAllIcon(programCode) {
        if (programConfigList[programCode].showAllIcon) {
            $(".info-column-content-icon-table[value='" + programCode + "'] tr[value='hidden']").hide();
            $(".info-column-content-icon-table[value='" + programCode + "'] tr[value='shown']").show();
            $(".info-column-header-right[value='" + programCode + "'] img").attr("src", "image/icon_down.jpg");
        } else {
            $(".info-column-content-icon-table[value='" + programCode + "'] tr[value='shown']").hide();
            $(".info-column-content-icon-table[value='" + programCode + "'] tr[value='hidden']").show();
            $(".info-column-header-right[value='" + programCode + "'] img").attr("src", "image/icon_up.jpg");
        }
        programConfigList[programCode].showAllIcon = !programConfigList[programCode].showAllIcon;
        $(".info-column-content-icon-table[value='" + programCode + "']").uiwidget();
    }


    //设置我的应用本地缓存
    function saveProgramContentLocal(programCode, programContent, callback) {
        Cordova.exec(function () {
            if (callback) {
                callback();
            }
        }, null, "Setting", "saveProgramContent", [userId, programCode, programContent]);
    }
    //获取我的应用本地缓存
    function getProgramContentLocal(programCode, callback) {
        Cordova.exec(function (result) {
            var ret = eval('(' + result + ')');
            if (ret.data[0].appList != "") {
                apptrue = false;
            } else {
                apptrue = true;
            }
            if (callback) {
                callback(result);
            }
        }, null, "Setting", "getProgramContent", [userId, programCode]);
    }

    function refreshToDoList(programCode, params) {
        // console.log(getTime() + params)
        if (params != null && params != "") {
            var refreshTodoList = eval(params);
            for (var i = 0; i < refreshTodoList.length; i++) {
                for (var j = 0; j < programConfigList[programCode].todoList.length; j++) {
                    if (programConfigList[programCode].todoList[j].appId == refreshTodoList[i].appId && programConfigList[programCode].todoList[j].moduleName == refreshTodoList[i].moduleName) {
                        programConfigList[programCode].todoList[j].pendingCount = refreshTodoList[i].count;
                    }
                }
            }
            // console.log(getTime() + "programConfigList",programConfigList)
            // console.log(getTime() + refreshTodoList)
            setToDoView(programCode, false, false);
        }
    }


    function showPreLoadIcon(flag) {
        switch (flag) {
            case 0:
                $("#openDownloadList").hide();
                break;
            case 1:
                $("#openDownloadList").show();
                break;
        }
    }

    //打开我的应用，待办应用
    function openAppById(appId, appEntry, appName, appVersion) {
        if (!enableOpenApp) {
            return;
        }
        enableOpenApp = false;
        setTimeout("enableOpenApp = true;", 1000);
        if (!isonline) {
            app.hint('当前网络不可用')
            return;
        }
        var params = {
            appCode: appId,
            appUrl: appEntry,
            appName: appName,
            appVersion: appVersion,
            data: {}
        };
        if (params.appCode == "XINGHUO_test_ADMINa") {
            params.appCode = "XINGHUO_ADMIN";
        }
        Cordova.exec(onSuccess, onError, "Page", "loadApp2", [params.appCode, params.appUrl, params.data, "", params.appVersion]);
        function onSuccess(res) {
            // console.log(getTime() + res)
        }

        function onError(err) {
            // console.log(getTime() + err)
        }
    }

    // ———————————————— 通知公告工作动态——————————————————————————————————
    var AnnouncementToken;
    var announceUnreadCount = 0;
    var workUnreadCount = 0;
    function getAnnouncementToken(updatePoint) {
        if (getnoticeData()) { showAnnouncement(getnoticeData()) }
        if (getworkData()) { showWorkMomentData(getworkData()) }
        var tokenUrl = announcement_url + "sys/token";
        var params = {
            loginId: loginId
        };

        // console.log('getAnnouncementToken-tokenUrl：' + tokenUrl)
        xh.jw_get(tokenUrl, params, function (res) {
            var ret = eval('(' + res.returnValue + ')');
            if (ret.code == 0) {
                AnnouncementToken = ret.access_token;
                if (updatePoint == "notice") {
                    getAnnouncementData(AnnouncementToken);
                } else if (updatePoint == "workDynamic") {
                    getWorkMomentData(AnnouncementToken);
                } else {
                    getAnnouncementData(AnnouncementToken);
                    getWorkMomentData(AnnouncementToken);
                }
            }
            // console.log('getAnnouncementToken-success：' + JSON.stringify(ret))

            $(".onlineerro").hide()
            isonline = true
        }, function (err) {
            // console.log('getAnnouncementToken-error：' + JSON.stringify(err))

            $(".onlineerro").show()
            isonline = false
            ui.hideMask();
        }, function (e) {

        })

    }
    //通知公告工作动态本地存储
    function setLocalData(noticeData, workData) {
        if (noticeData && noticeData.code == 0) {
            localStorage.setItem("noticeData", JSON.stringify(noticeData))
        }
        if (workData && workData.code == 0) {
            localStorage.setItem("workData", JSON.stringify(workData))
        }
    }
    function getnoticeData() {
        return JSON.parse(localStorage.getItem("noticeData"))
    }
    function getworkData() {
        return JSON.parse(localStorage.getItem("workData"))
    }
    function settodoData(data) {
        console.log(getTime() + '---------------------settodoData\n')

        localStorage.setItem("todoData", JSON.stringify(data))
    }
    function gettodoData() {
        console.log(getTime() + '---------------------gettodoData\n')

        return JSON.parse(localStorage.getItem("todoData"))
    }
    function settodoCount(data) {
        localStorage.setItem("todoCount", JSON.stringify(data))
    }
    function gettodoCount() {
        return JSON.parse(localStorage.getItem("todoCount"))
    }
    //通知公告
    function getAnnouncementData(token) {
        var requestUrl = announcement_url + "oa/info/notifyList";
        var params = {
            type: 1,
            num: 2,
            access_token: token
        };
        xh.jw_get(requestUrl, params, function (res) {
            ui.hideMask();
            var ret = eval('(' + res.returnValue + ')');
            showAnnouncement(ret)
            setLocalData(ret)
        }, function (err) {
            ui.hideMask();
            console.error(err);
        }, token)
    }
    function showAnnouncement(ret) {
        announceUnreadCount = ret.notReadNum;
        if (announceUnreadCount && announceUnreadCount > 0) {
            $("#announceUnreadCount").html(announceUnreadCount);
            $("#announceUnreadCount").show();
        } else {
            $("#announceUnreadCount").hide();
        }
        var oaInfoList = ret.oaInfoList;
        renderAnnouncement(oaInfoList);
    }
    function renderAnnouncement(result) {
        //            console.debug(result);
        if (result.length > 0) {
            var html = "";
            try {
                var length;
                if (result.length > 2) {
                    length = 2
                } else {
                    length = result.length
                }
                for (var i = 0; i < length; i++) {
                    var announcement = result[i];
                    var bt = baidu.template;
                    announcement.updateDate = announcement.updateDate.substring(0, 10);
                    html += bt("announcement_entry", announcement);
                }
            } catch (e) {
                // console.log(getTime() + "数据有误：" + e);
            }
            $("#announcement").html(html);
            $("#announcement").uiwidget();
        }

    }
    //工作动态
    function getWorkMomentData(token) {
        var requestUrl = announcement_url + "/oa/info/notifyList";
        var params = {
            type: 4,
            num: 2,
            access_token: token
        };
        xh.jw_get(requestUrl, params, function (res) {
            ui.hideMask();
            var ret = eval('(' + res.returnValue + ')');
            showWorkMomentData(ret)
            setLocalData("", ret)
        }, function (err) {
            ui.hideMask();
            console.error(err);
        }, function () {

        })

    }
    function showWorkMomentData(ret) {
        workUnreadCount = ret.notReadNum;
        if (workUnreadCount && workUnreadCount > 0) {
            $("#workUnreadCount").html(workUnreadCount);
            $("#workUnreadCount").show();
        } else {
            $("#workUnreadCount").hide();
        }
        var oaInfoList = ret.oaInfoList;
        renderWorkMoment(oaInfoList);
    }
    function renderWorkMoment(result) {
        if (result == null || result === 'undefined') return;
        var bt = baidu.template;
        var html = "";
        var length;
        if (result.length > 2) {
            length = 2
        } else {
            length = result.length
        }
        for (var i = 0; i < length; i++) {
            var moment = result[i];
            moment.updateDate = moment.updateDate.substring(0, 10);
            html += bt("work_moment_item", moment);
        }
        $("#workMomentList").html(html);
        $("#workMomentList").uiwidget();

    }
    function refreshRedPoint(updatePoint) {
        getAnnouncementToken(updatePoint);
    }
    //    拨打电话
    function iphone() {
        ui.confirm('确认拨打市纪委信息数据监督室运维电话？',
            '0755-88133410',
            function () {
                var successCallback = function (result) {

                };
                Cordova.exec(successCallback, null, "PhonePlugin", "dial", ["0755-88133410"]);
            }, function () {
                app.hint("您取消了该操作！")
            })
    }

</script>

<style type="text/css">
    html,
    body {
        height: 100%;
    }

    .shenglvhao {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    .overflowTextOneline {
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
    }

    [data-theme="a"] {
        background-color: #439fd7;
        background-image: -moz-linear-gradient(top, #59b3e8, #2280bd);
        background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#59b3e8), to(#2280bd));
        background-image: -webkit-linear-gradient(top, #59b3e8, #2280bd);
        background-image: -o-linear-gradient(top, #59b3e8, #2280bd);
        background-image: linear-gradient(to bottom, #59b3e8, #2280bd);
        background-repeat: repeat-x;
        color: #ffffff;
        border: 1px solid #156598;
        border-color: #81c5ee;
        text-shadow: 0 0 1px #999;
    }

    .title .navbar li [data-role="BTButton"].btn-active {
        background-color: transparent !important;
        border: 0;
    }

    /*.title .navbar {*/
    /*width: 80px;*/
    /*padding-top: 0;*/
    /*text-align: right;*/
    /*}*/

    .navbar ul[data-menupos="bottom"] .angle {
        bottom: -11px;
        left: 42%;
    }

    .angle {
        background: #434343;
    }

    .navbar [data-role="BTButton"] .btn-text {
        line-height: 30px;
        padding-left: 0;
        font-weight: normal;
        text-shadow: none;
    }

    .title .navbar li [data-role="BTButton"].btn-active {
        color: #FFFFff;
    }

    .triangle-right-black {
        width: 0;
        height: 0;
        border: 10px solid transparent;
        border-bottom: 10px solid #434343;
    }

    .font-size-25 {
        font-size: 25px;
    }

    .font-size-22 {
        font-size: 22px;
    }

    .font-size-20 {
        font-size: 20px;
    }

    .font-size-18 {
        font-size: 18px;
    }

    .font-size-16 {
        font-size: 16px;
    }

    .font-color-333333 {
        color: #333333;
    }

    .font-color-8c8c8c {
        color: #8c8c8c;
    }

    .font-color-c8c8c8 {
        color: #c8c8c8;
    }

    .font-color-1a5df9 {
        color: #1a5df9;
    }

    .font-color-ffffff {
        color: #ffffff;
    }

    .font-color-a9a9a9 {
        color: #a9a9a9;
    }

    .font-color-b3b3b3 {
        color: #b3b3b3;
    }

    .font-color-b1b1b1 {
        color: #b1b1b1;
    }

    .font-color-4783e2 {
        color: #4783e2;
    }

    .font-color-a1a1a1 {
        color: #a1a1a1;
    }

    .info-column {
        margin-left: 2%;
        width: 96%;
        border-radius: 10px;
        background: white;
        margin-bottom: 20px;
    }

    .info-column-header {
        padding-top: 7px;
        padding-bottom: 7px;
    }

    .info-column-header-left {
        width: 8%;
        text-align: left;
        padding-left: 15px;
    }

    .info-column-header-left img {
        width: 25px;
        height: 25px;
    }

    .info-column-header-center {
        width: 70%;
        padding-left: 10px;
    }

    .info-column-header-right {
        width: 22%;
        text-align: right;
        padding-right: 5px;
    }

    .info-column-header-right img {
        width: 16px;
        height: 16px;
    }

    .info-column-content {
        border-top: 1px solid #eeeeee;
        padding-top: 5px;
        padding-bottom: 5px;
    }

    .info-column-content-icon-table {
        margin-top: 5px;
        margin-bottom: 5px;
    }

    .info-column-content-icon-table tr td {
        width: 25%;
        text-align: center;
        padding-bottom: 5px;
        /*border: 1px solid green;*/
    }

    .info-column-content-icon-table tr td div img {
        width: 50px;
        height: 50px;
        margin-bottom: 5px;
    }

    .statistics-chart-navbar {
        padding-left: 15px;
        border-bottom: 1px solid #eeeeee;
    }

    .statistics-chart-navbar div[data-role='BTButton'] {
        width: 50%;
        margin-left: 20px;
        background: white;
        /*color: white;*/
        color: #333333;
        border: none;
        /*border-radius: 10px;*/
        /*padding: 3px 1px 15px 10px;*/
        padding: 3px 0px 15px 7px;
    }

    .statistics-chart-navbar div[data-role='BTButton'][class='btn-active'] {
        width: 50%;
        margin-left: 20px;
        background: white;
        color: #1b80ff;
        /*border: none;*/
        /*border-radius: 10px;*/
        /*padding: 3px 1px 15px 10px;*/
        padding: 3px 0px 15px 7px;
        border-bottom: 3px solid #1b80ff;
    }

    .loading1{
        width: 15px;
        height: 15px;
        background-position: 0 -90px;
        -webkit-animation: loading 2s linear infinite;
    }

    .loading2 {
        from {
            -webkit-transform: rotate(0deg) translateZ(0);
        } to {
            -webkit-transform: rotate(360deg) translateZ(0);
        }
    }

</style>