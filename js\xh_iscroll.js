/**
 * Created by admin on 2019/3/12.
 */
/**
 isroll组件
 @class ui.IScroll
 */
/**
 isroll组件初始化
 @method  ui.IScroll.init(selector, options)
 @param selector {selector|zepto} (必选) iscroll对象CSS选择器
 @param options {JSONObject} (可选) 参数，具体参数如下
 <ul>
 <li>scrollBar (Boolean):   是否出现滚动条 | 默认 false</li>
 <li>enablePullDown (Boolean):  是否允许下拉刷新 | 默认 false </li>
 <li>enablePullUp (Boolean):  是否允许上拉刷新  | 默认 false</li>
 <li>pullDownAction (Function):  下拉刷新调用方法 </li>
 <li>pullUpAction (Function):  上拉刷新调用方法</li>
 </ul>
 @example
 ui.IScroll.init(".pullrefresh-wrapper",{
		    scrollBar : true,                //是否出现滚动条
		    enablePullDown: true,            //是否允许下拉刷新
		    pullDownAction: pullDownActionDemo //下拉刷新调用方法
		});

 //refreshCallback必须执行，这是执行完业务逻辑之后IScroll状态刷新的回调。
 //例如是ajax请求，那这条语句就放在ajax的callback的最后一条。
 //PS:之所以要提供回调处理，是因为IScroll不知道业务逻辑什么时候结束
 function pullDownActionDemo(refreshCallback){
		    //setTimeout是为了模拟ajax请求数据的时间
		    setTimeout(function () {
		        refreshCallback && refreshCallback();
		    }, 3500);
		}
 */
;ui.xh_IScroll = (function(){

    var defaults = {
        scrollBar : false,
        enablePullDown: false,
        enablePullUp:false,
        loadingLabel:"加载中...",
        flipLabel:"释放刷新",
        pullDownlabel:"下拉刷新",
        pullUplabel:"上拉显示更多",
        pullDownAction:function(){},
        pullUpAction:function(){}
    };

    var init = function(selector, options){
        var pullSettings = $.extend({}, defaults, options);
        var $iscrollWrapper = $(selector);
        if($iscrollWrapper.length){

            _uiRender($iscrollWrapper);

            var pullContainer = $iscrollWrapper.children();

            var pullDownEl, pullUpEl,
                pullDownOffset = pullUpOffset = 0,
                _pervScroll = 0, isLoading = false;
            if(pullSettings.enablePullDown){
                var paddingTop = pullContainer.css("padding-top") || "";
                paddingTop = paddingTop.replace("px", "");
                pullDownEl = $('<div class="pullDown"><span class="pullDownIcon"></span><span class="pullDownLabel">下拉刷新</span></div>').prependTo(pullContainer);
                pullDownOffset = ( pullDownEl.height() || 51 )  - paddingTop; //提供一个默认值
                pullDownEl.hide();
            }

            if(pullSettings.enablePullUp){
                pullUpEl = $('<div class="pullUp"><span class="pullUpIcon"></span><span class="pullUpLabel">上拉显示更多</span></div>').appendTo(pullContainer) ;
                pullUpOffset =  ( pullUpEl.height()|| 51 );
                pullUpEl.hide();
            }

            var myScroll = new iScroll($iscrollWrapper[0], {
                hScrollbar: pullSettings.scrollBar, //是否显示水平滚动条
                vScrollbar: pullSettings.scrollBar,
                checkDOMChanges: !(pullSettings.enablePullDown || pullSettings.enablePullUp) ,
                useTransition: false,
                topOffset: pullDownOffset,
                bounce : pullSettings.enablePullDown || pullSettings.enablePullUp ,
                //topOffset : pullDownOffset ,
                onBeforeScrollStart: function (e) {
                    var target = e.target;
                    while (target.nodeType != 1) target = target.parentNode;

                    if (target.tagName != 'SELECT' && target.tagName != 'INPUT' && target.tagName != 'TEXTAREA')
                        e.preventDefault();
                },
                onRefresh: function () {
                    if( pullSettings.enablePullDown && pullDownEl.hasClass("loading")   ){
                        pullDownEl.removeClass("loading") ;
                        pullDownEl.find(".pullDownLabel").html( pullSettings.pullDownlabel ) ;
                        //pullDownEl.hide();
                    }else if(  pullSettings.enablePullUp && pullUpEl.hasClass("loading")   ){
                        pullUpEl.removeClass("loading") ;
                        pullUpEl.find(".pullUpLabel").html( pullSettings.pullUplabel ) ;
                        //pullUpEl.hide();
                    }
                },
                onScrollMove: function () {
                    if (pullSettings.onScrollMoveCallback) {
                        pullSettings.onScrollMoveCallback();
                    }

                    if(isLoading) { return; }


                    if(window._keyboardIsShow){ //fix android键盘弹出时，滚不到最上或最下
                        this.wrapper.scrollTop = 0;
                        $(this.scroller).height(window._currentIscrollHeight + 1);
                    }


                    if( pullSettings.enablePullDown ){
                        pullDownEl.show();
                    }

                    if( pullSettings.enablePullUp ){
                        pullUpEl.show();
                    }

                    if (pullSettings.enablePullDown && this.y > 5 && !pullDownEl.hasClass('flip') ) {

                        pullDownEl.addClass("flip") ;
                        pullDownEl.find(".pullDownLabel").html( pullSettings.flipLabel ) ;
                        this.minScrollY = 0;
                    } else if (pullSettings.enablePullDown && this.y < 5 && pullDownEl.hasClass('flip') ) {

                        pullDownEl.show().removeClass("flip") ;
                        pullDownEl.find(".pullDownLabel").html( pullSettings.pullDownlabel) ;
                        this.minScrollY = -pullDownOffset;
                    } else if (pullSettings.enablePullUp &&this.y < (this.maxScrollY - 5) && !pullUpEl.hasClass('flip')) {
                        pullUpEl.addClass("flip") ;
                        pullUpEl.find(".pullUpLabel").html( pullSettings.flipLabel ) ;
                        this.maxScrollY = this.maxScrollY;
                    } else if (pullSettings.enablePullUp &&this.y > (this.maxScrollY + 5) && pullUpEl.hasClass('flip')) {
                        pullUpEl.show().removeClass("flip") ;
                        pullUpEl.find(".pullUpLabel").html( pullSettings.pullUplabel  ) ;

                        //this.maxScrollY = pullUpOffset;
                    }

                },
                onScrollEnd: function () {
                    if (pullSettings.onScrollEndCallback) {
                        pullSettings.onScrollEndCallback();
                    }

                    //阻止重复调用
                    if(isLoading){ return; }

                    if (pullSettings.enablePullDown&&pullDownEl.hasClass('flip')) {
                        pullDownEl.addClass("loading") ;
                        pullDownEl.find(".pullDownLabel").html( pullSettings.loadingLabel ) ;
                        isLoading = true;

                        pullSettings.pullDownAction(refreshCallback) ;
                    } else if (pullSettings.enablePullUp&&pullUpEl.hasClass('flip')  ) {
                        pullUpEl.addClass("loading") ;
                        pullUpEl.find(".pullUpLabel").html( pullSettings.loadingLabel ) ;
                        isLoading = true;

                        pullSettings.pullUpAction(refreshCallback) ;
                    }
                }
            });

            //结束调用action之后的事情
            function refreshCallback(){
                pullDownEl && pullDownEl.removeClass("flip");
                pullUpEl && pullUpEl.removeClass("flip");
                myScroll.refresh();
                isLoading = false;
            }

            if( pullSettings.enablePullDown ){
                pullDownEl.show();
            }

            if( pullSettings.enablePullUp ){
                pullUpEl.show();
            }


            myScroll.destroyMe = function() {
                pullSettings = null;
                pullDownEl = null;
                pullUpEl= null;
                pullDownOffset = null;
                pullUpOffset= null;
                isLoading = null;
                myScroll.destroy();
            };

            GC&&GC.push([myScroll], "iScroll", $iscrollWrapper.closest("section"));
            return myScroll;
        }
        return null;
    }

    function _uiRender($iscrollWrapper){
        $iscrollWrapper.addClass("iscroll-wrapper-clz") ;

        var $footer1 = $iscrollWrapper.next(".footer") ,
            $footer2 = $iscrollWrapper.closest("section").find(".footer"),
            $header1 = $iscrollWrapper.prev(".header"),
            $header2= $iscrollWrapper.closest("section").find(".header");

        var $footer = $footer1.length ? $footer1 : $footer2,
            $header = $header1.length ? $header1 : $header2;

        if($footer.length){
            $iscrollWrapper.css("bottom", $footer.height()+"px" ) ;
        }
        //if($header.length){
        var top  = $iscrollWrapper.attr("top") ||  $header.height() || $header2.height();;
        $iscrollWrapper.css("top", parseFloat(top) + "px" ) ;
        //}

    }

    /**
     isroll页面放大缩小
     @method  ui.IScroll.zoom(selector)
     @param selector {selector|zepto} (必选) iscroll对象CSS选择器
     @example
     ui.IScroll.zoom(".pullrefresh-wrapper");

     */
    var zoom = function(selector){
        return new iScroll($(selector)[0], {
            zoom:true,
            checkDOMChanges:true,
            useTransition: false,
            hScrollbar: true, //是否显示水平滚动条
            vScrollbar: true
        });
    }

    return {
        init : init,
        zoom : zoom
    };

})();