/**
 * Created by luoxieting on 2018/7/9.
 */
var xinghuoArea = "xh_gansu";
window.xh_gansu = window.xh_gansu || {};
/**
 * post请求
 * @param url
 * @param data
 * @param success
 * @param fail
 */
xh_gansu.get = function(url, data, success, fail, isCloud) {
    var timeout = data.timeout;
    if (typeof (timeout) == "undefined" || timeout == "") {
        timeout = 30000;
    }
    var successCallback = function (result) {
        success({"returnValue": result})
    };
    var failCallback = function (result) {
        checkFailCallback(result);
        fail({"returnValue": result});
    };
    $.ajax({
        url: url,
        data: data,
        async: app.utils.toJSON(data).async, // || true,
        timeout: timeout,
        type: "GET",
        success: successCallback,
        error: failCallback
    });
}
/**
 * post请求
 * @param url
 * @param data
 * @param success
 * @param fail
 * @param before 设置了请求头
 * @param complete
 */
xh_gansu.post = function(url, data, success, fail, isCloud,before, complete) {
    var timeout = data.timeout || 30 * 1000;
    var content_type = data.contentType || "application/json";
    var successCallback = function (result) {
        success({"returnValue": result});
    };
    var failCallback = function (result) {
        fail({"returnValue": result});
    };
    $.ajax({
        url: url,
        data: JSON.stringify(data),
        async: app.utils.toJSON(data).async, // || true,
        timeout: timeout,
        type: "POST",
        success: successCallback,
        error: failCallback,
        beforeSend: function (xhr) {
            before(xhr);
        },
        complete: complete,
        contentType: content_type
    });
}

//文件上传，带上手机硬件参数
xh_gansu.uploadFile= function(filePath, url, success, fail, options) {
    var fileTransfer = new FileTransfer();
    var successCallback = function (result) {
        success(result);
    };
    var failCallback = function (result) {
        //检测错误返回，进行统一提示。
        checkFailCallback(result);
        fail(result);
    };
    fileTransfer.upload(
        filePath,
        url,
        successCallback,
        failCallback,
        options
    );
}


//文件下载，带上手机硬件参数
xh_gansu.downloadFile = function(filePath, url, success, fail, _boolean, option) {
    var fileTransfer = new FileTransfer();
    var uri = encodeURI(url);
    var successCallback = function (result) {
        success(result);
    };
    var failCallback = function (result) {
        //检测错误返回，进行统一提示。
        fail(result);
    };
    fileTransfer.download(
        uri,
        filePath,
        successCallback,
        failCallback,
        _boolean,
        option
    );
}
xh_gansu.getToken = function (loginId, callback) {
    setTimeout(function () {
        Cordova.exec(successToken, null, "LinkPlugin", "getToken", []);
    }, 1000)
    var successToken = function (res) {
        callback({"returnValue": res});

    };
}

/*
 * 错误反馈，统一对用户进行提示
 * */
function checkFailCallback(result) {
//      alert(JSON.stringify(result));
    var errorCode = result.code;
    if (errorCode == 404){
        app.hint("你所访问的接口不存在！");
    } else if (errorCode == 0) {
        ret = "" + result.returnValue;
        if (ret.indexOf("SocketTimeoutException") >= 0) {
            app.hint("网络连接超时！");
        } else if (ret.indexOf("ConnectException") >= 0) {
            app.hint("网络连接失败！");
        } else {
            //其他连接失败错误
            app.hint("其他网络错误！");
        }
    } else {
        var ret = eval( "(" + result.returnValue + ")" );
        if (ret.success == false) {
            // app.hint(ret.msg);
        } else {
            //未知错误
            app.hint("其他连接错误！");
        }
    }
}