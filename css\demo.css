@CHARSET "utf-8";
body {
	padding:0px;
	margin:0px;
	margin-top:10px;
	text-align: left;
	font-family:'微软雅黑',Trebuchet MS,Verdana,Helvetica,Arial,sans-serif;
}
.ichartjs_btn{
	padding:2px 5px;
	line-height:25px;
	color:#0b2946;
	cursor: pointer;
	text-align:center;
	font-size:12px;
	border:1px solid #98adc1;
	-webkit-box-shadow:0px 0px 2px #375073;
	-moz-box-shadow:0px 0px 2px #375073;
	box-shadow:0px 0px 2px #375073;
	-moz-border-radius:5px;
	-webkit-border-radius:5px;
	-khtml-border-radius:5px;
	border-radius:5px
}
.ichartjs_author{
	position: absolute;
	font-size:12px;
	right: 20px;
	top: 0px;
}
.ichartjs_author a{
	color:#113659;
}

.ichartjs_info{
	position:relative;
	margin:10px;
	padding:5px;
	color:#1b4267;
}
.ichartjs_sm{
	margin:10px 0px;
	font-size: 13px;
	font-weight: 600;
}
.ichartjs_details{
	padding:0px;
	text-indent:2em;
	font-size: 12px;
	line-height:20px;
}
#ichartjs_code{
	display:none;
}
#ichartjs_result{
	position: absolute;
	left: 20px;
	bottom: 20px;
	padding:8px;
	color:#fefefe;
	font-size:20px;
	font-weight:600;
	background-color:#6d869f;
	cursor: pointer;
	text-align:center;
	border:1px solid #6a869d;
	-webkit-box-shadow:0px 0px 2px #375073;
	-moz-box-shadow:0px 0px 2px #375073;
	box-shadow:0px 0px 2px #375073;
	-moz-border-radius:10px;
	-webkit-border-radius:10px;
	-khtml-border-radius:10px;
	border-radius:10px
}