<!doctype html>
<html lang="zh" class="no-js">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/>
    <meta name="viewport" content="width=480,user-scalable=no"/>
    <!--CSS-->
    <link rel="stylesheet" href="../../frame3/css/bingotouch.css"/>
    <link rel="stylesheet" href="../../frame3/css/app.css"/>
    <link rel="stylesheet" href="css/skin_1.css" type="text/css" id="skin_style"/>
    <!--JS-->
    <!--<script src="../../js/vconsole.min.js"></script>-->
    <script src="../../frame3/js/cordova.js"></script>
    <script src="../../frame3/js/zepto.js"></script>
    <!--<script src="../../frame3/js/iscroll.js"></script>-->
    <script src="../../frame3/js/baiduTemplate.js"></script>
    <script src="../../frame3/js/bingotouch.js"></script>
    <script src="../../frame3/js/plugin/linkplugins.js"></script>
    <script src="../../frame3/js/app/app.js"></script>
    <script src="js/index.js"></script>
    <script src="../../js/xh_public.js"></script>
    <script src="../../js/SzgaPlugin.js"></script>
    <title>纪委2.0首页</title>
    <!--<link rel="stylesheet" type="text/css" href="css/component.css"/>-->
    <!--<script src="js/modernizr-custom.js"></script>-->
</head>
<body style=" margin:0;overflow-x: hidden;">
<div style="background: #f5f5f5;overflow-x: hidden;">
    <div style="position: absolute;left:55px;top:27px;width:40px;height:40px;" id="dblclickaotu"></div>
    <!--<div class="desktop-body" style="margin-top: 74px;width: 515px;">-->
    <div class="desktop-body" style="width: 101%;">
        <!--头像名字 电话刷新-->
        <div class="row-box" style="width: 100%;display: flex;margin-bottom: 15px;position: relative">
            <div class="bottomLogo" style="width: 100%;height:40px;position: absolute;top:12px;text-align: center;">
               <div style="display:inline-block;background: url('./image/logo.png') no-repeat center center;background-size: 250px 30px;width:100%;height:40px;margin:  0 auto;opacity: 0.5;"></div>
            </div>

            <div class="user-img" style="margin-left: 8px; z-index:2;">
                <img id="imgAvatar" style="width: 47px;height: 47px;border-radius: 47px;margin-top: 8px;"
                     onerror="onerror=null;src='img/ic_avatar.png'" src="img/ic_avatar.png"/>
            </div>
            
            <div class="user-show"  style="z-index:1;margin-top:13px;background-color: rgba(85, 85, 85, 0.4);display: flex;height: 30px; border-radius: 0px 20px 20px 0px;padding-left:25px;padding-right: 10px;align-items: center;position: relative;left: -23px;top:4px;">
                <div id="user_name" style="padding: 0; margin: 0;font-size: 20px;color: #fff;font-weight: normal;display: inline-block;">
                </div>
            </div>

            <div onclick="iphone()" style="position:absolute;right:13%;top:-18px;font-size:20px;text-align: center;color:#ff0000;margin-top:30px;display: inline-block;z-index:89;">
                <img src="img/iphone1.png" style="width:27px;height:27px;" alt=""/>
            </div>
            <div onclick="app.refresh();" style="position:absolute;right:5%;top:-18px;font-size:20px;text-align: center;color:#ff0000;margin-top:30px;display: inline-block;z-index:89;">
                <img src="img/shuaxin.png" style="width:27px;height:27px;" alt=""/>
            </div>

            <!--<div style="flex: 1;justify-content: flex-end;display: flex;align-items: center;margin-right: 30px;border: 1px solid #000;">-->
                <!--<div data-role="BTButton" data-type="image" onclick="app.refresh();">-->
                    <!--<img src="img/shuaxin.png" style="width:28px;height:28px;" alt=""/>-->
                <!--</div>-->
            <!--</div>-->
        </div>
        <div style="flex-direction:column;margin:0px 10px 5px 10px;padding-bottom: 10px;padding-top: 5px;overflow: hidden;border-radius: 15px;display: flex;background-color: white;">
            <div style="padding-left:15px;">
                <div class="row-box">
                    <div class="box-left" onclick="tapAnnouncement(1);">
                        <img style="width:97px;height: 22px; " src="img/tongzhigonggao.png" alt=""/>
                                <span id="announceUnreadCount"  style="margin-left: 10px;color: white;background-color: red;padding: 0 10px 0 10px;border-radius: 15px;display: none">
                                </span>
                    </div>
                    <div class="span1">
                    </div>
                    <div class="box-right" style="font-size: 18px;color: #90909a;margin-top: 3px;">
                                <span style="margin-right:13px;" onclick="tapAnnouncement(0);">
                                    全部
                                    <img style="width: 18px;height: 15px;margin-left: 0px;margin-top: -3px;"
                                         src="img/14.png" alt=""/>
                                </span>
                    </div>
                </div>
            </div>
            <div id="announcement" style="display: flex;flex-direction: column;width: 100%;">

            </div>
        </div>
        <!--通知公告-->
        <script type="text/html" id="announcement_entry">
            <div style="display: flex;border-top: 1px solid #EEE;line-height: 50px;height: 50px;"
                 onclick="loadAnnounceDetail('<%=id%>')">
                    <div class="shenglvhao" style="color: #8F9193;padding-left: 10px;">
                        <%=updateDate%>
                    </div>
                    <div class="shenglvhao"
                         style="flex:1;text-align: left;padding-left: 10px;font-size: 24px;width: 100%;">
                        <%=title%>
                    </div>
            </div>
        </script>

        <div class="desktop-content" style="margin-top:25px;">
            <!--待办事项与我的应用模板-->
            <script type="text/html" id="info_column_empty">
                <div class="info-column">
                    <div class="row-box info-column-header">
                        <div class="info-column-header-center box-left" value="<%=programCode%>" name="<%=programName%>">
							<span class="font-size-22 font-color-333333">
                                <%if (programName=="待办事项"){%>
                                    <img style="width:97px;height: 22px; " src="img/daibanshixiang.png">
                                    <span id="couledaiban"
                                          style="margin-left: 10px;color: white;background-color: red;padding: 0 10px 0 10px;border-radius: 15px;display: none">
                                    </span>
                                <%}else if(programName=="我的应用"){%>
                                    <img style="width:97px;height: 22px;" src="img/myApp.png">
                                <%}else{%>
                                <%=programName%>
                                <%}%>
							</span>
                        </div>
                        <div class="span1"></div>
                        <div class="info-column-header-right" value="<%=programCode%>">
                            <div class="box-right" style="font-size: 18px;color: #90909a;margin-top: 3px;">
                                <%if (programName=="待办事项"){%>
                                <%}%>
                                <%if(programName=="我的应用"){%>
                                <div style="height: 100%;width: 40%;margin-left: 60%;padding-right: 10px;"
                                     onclick="showOrHideAllIcon('<%=programCode%>');">
                                    <img src="image/icon_down.jpg"/>
                                </div>
                                <%}%>
                            </div>
                        </div>
                    </div>
                    <div class="info-column-content" value="<%=programCode%>">
                        <!--显示待办或我的应用内容-->
                    </div>
                </div>
            </script>

            <script type="text/html" id="user_info">
                <div class="row-box" style="padding-left: 20px;padding-bottom: 20px;">
                    <div style="width: 90px;">
                        <% if (userPhotoPath == "") { %>
                        <img src="image/default_user_photo.png" width="90" height="90" style="border-radius: 45px;"/>
                        <% } else { %>
                        <img src="<%=userPhotoPath%>" width="90" height="90" style="border-radius: 45px;"/>
                        <% } %>
                    </div>
                    <div style="width: 70%;padding-left: 20px;">
                        <div class="row-box">
                            <div style="padding-top: 8px;">
                                <span class="font-color-333333 font-size-25"><%=userName%></span>
                            </div>
                            <div style="padding-top: 10px;padding-left: 15px;">
                                <span class="font-size-16 font-color-1a5df9"
                                      style="background: #c6d5f9;border-radius: 20px;padding: 5px 15px 5px 15px;"><%=userPosition%></span>
                            </div>
                        </div>
                        <div>
                            <span class="font-size-16 font-color-8c8c8c"><%=deptName%></span>
                        </div>
                    </div>
                </div>
            </script>

            <script type="text/html" id="info_column_content_todo_icon_table">
                <table class="info-column-content-icon-table" value="<%=programCode%>">
                    <% for (var i = 0; i * lineNum < allList.length; i++) { %>
                    <tr value="hidden">
                        <% for (var j = 0; j < lineNum; j++) { %>
                        <td>
                            <% if (i * lineNum + j < allList.length) { %>
                                <div style="position: relative;">
                                    <% if (allList[i * lineNum + j].pendingCount > 0) { %>
                                                <%if(allList[i * lineNum + j].moduleName=="待阅公文"){%>
                                                <div class="icon-badge" style="border-radius:6px;min-width:12px;height:12px;"></div>
                                                <%}else{%>
                                                <div class="icon-badge">
                                                    <%=allList[i * lineNum + j].pendingCount%>
                                                </div>
                                                <% } %>
                                      <% } %>
                                <img src="<%=allList[i * lineNum + j].appIcon%>" style="width: 40px;height: 40px;margin-bottom: 5px;"
                                     onclick="openAppById('<%=allList[i * lineNum + j].appCode%>','<%=allList[i * lineNum + j].appEntry%>','<%=allList[i * lineNum + j].moduleName%>','<%=allList[i * lineNum + j].appVersion%>');"/>
                                <!--<%if(pendingCount>=1){%>-->
                                <!--<span-->
                                <!--style="margin-left: 10px;color: white;background-color: red;padding: 0 10px 0 10px;border-radius: 15px;">-->
                                <!--<%=pendingCount%>-->
                                <!--</span>-->
                                <!--<%}%>-->
                            </div>
                          <div class="font-size-16 font-color-333333" style="text-align: center">
                                <div style="overflow: hidden;text-overflow:ellipsis;white-space: nowrap;">
                                    <%=allList[i * lineNum + j].moduleName%>
                                </div>
                            </div>
                            <% } %>
                        </td>
                        <% } %>
                    </tr>
                    <% } %>
                </table>
            </script>

            <script type="text/html" id="icon_table_row">
                <tr value="<%=value%>" style="<% if(value=='hidden'){ %>display:none;<%} %>">
                    <td class="<%=columnName%>" value="<%=lineIndex*lineNum%>">

                    </td>
                    <td class="<%=columnName%>" value="<%=lineIndex*lineNum+1%>">

                    </td>
                    <td class="<%=columnName%>" value="<%=lineIndex*lineNum+2%>">

                    </td>
                    <td class="<%=columnName%>" value="<%=lineIndex*lineNum+3%>">

                    </td>
                </tr>
            </script>

            <script type="text/html" id="icon_table_grid">
                <div>
                    <img class="appiconimg" src="<%=jmtAppIcon%>" style="width: 40px;height: 40px;margin-bottom: 5px;"
                         onerror="onerror=null;src='./img1/Cv1wGlz9sy6AGlvpAAANx3GGeiQ513.png';"
                         onclick="openAppById('<%=appCode%>','<%=appEntry%>','<%=appName%>','<%=version%>');"/>
                    <!--<img src="<%=jmtAppIcon%>" style="width: 40px;height: 40px;margin-bottom: 5px;" onclick="openApp('<%=appCode%>','<%=appEntry%>','<%=appName%>','<%=version%>');"/>-->
                </div>
                 <div class="font-size-16 font-color-333333">
                    <div style="overflow: hidden;text-overflow:ellipsis;white-space: nowrap;">
                        <%=appName%>
                    </div>
                </div>
            </script>

            <div id="desktopContent">
                &nbsp;
            </div>

            <!--工作动态-->
            <div style="display:flex;flex-direction:column;margin:25px 10px 25px 10px;border-radius: 15px;background:white;padding-bottom: 10px;padding-top: 5px">
                <div style="padding-left:15px;">
                    <div class="row-box">
                        <div class="box-left" onclick="tapWorkMoment(1);">
                            <img style="width:97px;height: 22px; " src="img/workstatus.png" alt=""/>
                                <span id="workUnreadCount"
                                      style="margin-left: 10px;color: white;background-color: red;padding: 0 10px 0 10px;border-radius: 15px;display: none;">
                                </span>
                        </div>
                        <div class="span1">
                        </div>
                        <div class="box-right" style="font-size: 18px;color: #90909a;margin-top: 3px;">
                                <span style="margin-right:13px;" onclick="tapWorkMoment(0);">
                                    全部 <img style="width: 18px;height: 15px;margin-left: 0px;margin-top: -3px;"
                                            src="img/14.png" alt=""/>
                                </span>

                        </div>
                    </div>
                </div>
                <div>
                    <ul id="workMomentList" class="con_end" style="width: 100%;margin: 0;padding: 0">

                    </ul>
                </div>
                <script type="text/html" id="work_moment_item">
                    <li onclick="loadWorkMomentDetail('<%=id%>')"
                        style="overflow: hidden;margin: 0;border-top: 1px solid #EEEEEE;padding: 5px 15px;">
                        <div style="display:flex;line-height: 35px;height: 35px;">
                            <div class="shenglvhao" style="color: #8F9193;">
                                <%=updateDate%>
                            </div>
                            <div class="overflowTextOneline"
                                 style="font-size:24px;line-height: 30px;flex: 1;align-self: center;margin-left: 10px;">
                                <%=title%>
                            </div>
                            <!--<div style="text-align: center;align-self: center">-->
                            <!--<img style="width: 9px;height: 18px;margin-left: 5px"-->
                            <!--src="img/ic_arrow_right.png"/>-->
                            <!--</div>-->
                        </div>

                    </li>
                </script>
            </div>

            <div id="desktopContent1">
                &nbsp;
            </div>
        </div>
        <div class="onlineerro" style="display:none;position: relative;height:70px;line-height: 70px;color:#333;background-color:#F6EBE6;padding-left:70px; ">
            <span class="icon1" style="background: url('./img/gth1.png') no-repeat center center;background-size: 30px 30px;
            width: 35px;height: 35px;display: inline-block;position: absolute;top: 17px;left: 30px"></span>
            <span class="text">  当前网络不可用，请检查网络设置</span>
        </div>

        <div id="test_app" style="display: none;">
            <script type="text/html" id="info_column_test_app">
                <div class="info-column">
                    <div class="row-box info-column-header">
                        <!--<div class="info-column-header-left">-->
                        <!--<img src="image/icon_use_most.png"/>-->
                        <!--</div>-->
                        <div class="info-column-header-center">
                            <span class="font-size-22 font-color-333333"><%=appClassifyName%>（测试应用）</span>
                        </div>
                        <div class="info-column-header-right">

                        </div>
                    </div>
                    <div class="info-column-content">
                        <table class="info-column-content-icon-table" value="<%=programCode%>">
                            <% for (var i = 0; i * lineNum < appList.length; i++) { %>
                            <tr>
                                <% for (var j = 0; j < lineNum; j++) { %>
                                <td>
                                    <% if (i * lineNum + j < appList.length) { %>
                                    <div style="position: relative;">
                                        <img src="<%=appList[i * lineNum + j].jmtAppIcon%>"
                                             style="width: 40px;height: 40px;margin-bottom: 5px;"
                                             onclick="openAppById('<%=appList[i * lineNum + j].appCode%>','<%=appList[i * lineNum + j].appEntry%>','<%=appList[i * lineNum + j].appName%>','<%=appList[i * lineNum + j].version%>');"/>
                                    </div>
                                    <div class="font-size-16 font-color-333333">
                                        <div style="width: 100%;overflow: hidden;text-overflow:ellipsis;white-space: nowrap;">
                                            <%=appList[i * lineNum + j].appName%>
                                        </div>
                                    </div>
                                    <% } %>
                                </td>
                                <% } %>
                            </tr>
                            <% } %>
                        </table>
                    </div>
                </div>
            </script>

            <div id="testAppContent">

            </div>
        </div>
    </div>
</div>
</body>
</html>
<!--<script src="js/classie.js"></script>-->
<!--<script src="js/dynamics.min.js"></script>-->
<!--<script src="js/main.js"></script>-->
<!--<script src="hightcharts/highcharts.js"></script>-->
<!--引用url定义文件-->

<!--dialog控件所需脚本和样式 -->
<!--<script src="../../js/ui-expand.js"></script>-->
<script src="url.js" type="text/javascript"></script>
<!--<link rel="stylesheet" href="../../css/ui-expand.css"/>-->
<script type="text/javascript">
//    var vConsole = new VConsole();
    var loginId;
    var userName;
    var deptName;
    var userPosition;
    var userPhotoPath;
    var mUserInfo = null;
    var userId;
    var column_line_icon_number = 4;
    var programConfigList = {
        "20190325141821591": {
            // 待办事项
            loadData: function (programCode) {
                getToDoList(programCode);
            },
            apps: null,
            show: false,
            todoList: []
        },
        "20190325141832479": {
            // 我的应用
            loadData: function (programCode) {
                getAllApp(programCode);
            },
            apps: null,
            show: false,
            lines: 5,
            showAllIcon: false
        }
    };
    var programDisplayList = [];
    var requestCount = 0;
    var firstLoadTodo = true;
    var userPhotoList = {};
    var hasDialogOpen = false;
    var dialogId = "";
    var exitFlag = false;
    var deviceType = "";
    var enableOpenApp = true;
    var userAppData = [];
    var apptrue = true;
    var isonline = true;

try{
    window.onload = function(){
        var ISReload = false;//重加载标识
        var appobj = {
            initialize: function() {
                console.log(2)
                this.bindEvents();
            },
            bindEvents: function() {
                document.addEventListener('deviceready', this.onDeviceReady, false);
                console.log(3)
                //延时100毫秒后进行重加载
                setTimeout(function() {
                    if (!ISReload) {
                        window.location.reload();//重加载
                        console.log('生效')
                        setTimeout(function(){
                            console.log('生效')
                        },2000)
                    }
                },100)
            },
            onDeviceReady: function() {
                ISReload = true;//更新重加载标识
                console.log(4)
                appobj.receivedEvent('deviceready');
            },
            receivedEvent: function(id) {
                console.log(5)
                //获取流程相关的数据
                try{
                document.addEventListener("backbutton", backFunc, false);
                }catch (e){
                    console.log("erro")
                    window.location.reload();//重加载
                }
                    //声明页面事件
                    document.addEventListener("resume", function () {
                        console.debug("resume");
                    getAnnouncementToken();
                }, false);
                var getLoginInfo1 = function (callback) {
                    var successCallback = function (result) {
                        callback(app.utils.toJSON(result));
                    };
                    Cordova.exec(successCallback, null, "LinkPlugin", "getLoginInfo", []);
                }
                getLoginInfo1(function (res) {
                    loginId = res.loginId;
                    userName = res.userName;
                    userId = res.userId;
                    console.log(6)
                    getAnnouncementToken();
//            1.0
                    getProgramConfig();
                    var  getUserInfo1 = function (callback, failCallback, userId) {
                        var successCallback = function (result) {
                            callback(app.utils.toJSON(result));
                        };
                        Cordova.exec(successCallback, failCallback, "LinkPlugin", "getUserInfo", [userId]);
                    }
                    getUserInfo1(function (res) {
                        $("#user_name").html(res.userName);
                        if (res.userPhotoPath) {
                            $("#imgAvatar").attr("src", "http://10.248.97.236:8008" + res.userPhotoPath);
                        }
                    }, function (res) {
                        console.error(res);
                    }, userId);
                    getUserTestApps();
                });

                $(document).click(function () {
                    $("#my_sonmenu").hide();
                });
                $("#dblclickaotu").dblclick(function () {
                    app.loadWithUrl("about.html", {});
                })
                shuiyin();
            }

        }
        appobj.initialize()
    };
}catch (e){
    console.log("onloaderro")
    setTimeout(function(){
        localreload()
    },1000)
}
//    app.page.onLoad = function () {
//        document.addEventListener("deviceready", function () {
//            document.addEventListener("backbutton", backFunc, false);
//        }, false);
//        document.addEventListener("resume", function () {
//            console.debug("resume");
//            getAnnouncementToken();
//        }, false);
//        xh.getLoginInfo(function (res) {
//            loginId = res.loginId;
//            userName = res.userName;
//            userId = res.userId;
//            getAnnouncementToken();
////            1.0
//            getProgramConfig();
//            app.link.getUserInfo(function (res) {
//                $("#user_name").html(res.userName);
//                if (res.userPhotoPath) {
//                    $("#imgAvatar").attr("src", "http://10.248.97.236:8008" + res.userPhotoPath);
//                }
//            }, function (res) {
//                console.error(res);
//            }, userId);
//            getUserTestApps();
//        });
//
//        $(document).click(function () {
//            $("#my_sonmenu").hide();
//        });
//        $("#dblclickaotu").dblclick(function () {
//            app.loadWithUrl("about.html", {});
//        })
//        shuiyin();
//    }

//安卓调用！！！来更新代办的红点数量。
    function onDesktopMsgReceived(symbolName, params) {
        switch (symbolName) {
            case "pendingInfo":
                refreshToDoList("20190325141821591", params);
                break;
            case "notice":
                getAnnouncementToken("notice");
                break;
            case "workDynamic":
                getAnnouncementToken("workDynamic");
                break;
            case "returnCar":
                break;
        }
    }

    //安卓调用！
    function onNetDisconnected(){
        isonline =false
        app.hint("网络断开了")
        $('.onlineerro').show()
    }
    function onNetConnected(){
        isonline =true
        app.hint( '网络已连接')
        $('.onlineerro').hide()
        window.location.reload();
    }

    //1.0
    function getProgramConfig() {
        $("#desktopContent").html("&nbsp;");
//      获取本地配置信息 然后设置
          getProgramConfigLocal();
        //  加载线上配置
         getProgramConfigRemote();
    }
    //1.5  获取本地配置信息 然后设置
    var bendiconfig =""
    function getProgramConfigLocal() {
        Cordova.exec(function (result) {
                if (result && result != "") {
                bendiconfig =result
                var configuredProgram = eval(result);
                setProgramConfig(configuredProgram);
            }
        }, null, "Setting", "getProgramConfig", [userId]);
    }
    //2.0   加载线上配置
    function getProgramConfigRemote() {
        var param = {
            URL_TYPE: "JMT",
            userCode: loginId
        };
        xh.jw_post(zf_url + "/msa_xhsy_program/programConfig/queryProgram", param, function (res) {
            var result = eval('(' + res.returnValue + ')');
            if (result.code != "00000") {
                return;
            }
            var config = JSON.stringify(result.data.configuredProgram);
            Cordova.exec(function () {
            }, null, "Setting", "saveProgramConfig", [userId, config]);
//            本地的配置不等于线上配置时执行线上的
                if(config !=bendiconfig){
                    setProgramConfig(result.data.configuredProgram);
                }


        }, function (res) {
        });
    }
    //3.0 设置配置待办事项和我的应用
    function setProgramConfig(configuredProgram) {
        for (var key in programConfigList) {
            programConfigList[key].show = false;
        }
        programDisplayList = [];
        for (var i = 0; i < configuredProgram.length; i++) {
            if (programConfigList[configuredProgram[i].programCode] == undefined) {
                continue;
            }

            programConfigList[configuredProgram[i].programCode].show = true;//配置里有的设置show是true。没有的是默认false
            programConfigList[configuredProgram[i].programCode].showLimit = configuredProgram[i].showLimit;

            programDisplayList[programDisplayList.length] = {
                programCode: configuredProgram[i].programCode,
                programName: configuredProgram[i].programName,
                iconUrl: configuredProgram[i].iconUrl
            };
        }
        var bt = baidu.template;
        var html = "";
        var bt1 = baidu.template;
        var html1 = "";
        //循环将待办事项我的应用头部 和容器渲染出来。
        for (var i = 0; i < programDisplayList.length; i++) {
            if (i == 0) {
                html += bt("info_column_empty", programDisplayList[i]);
                $("#desktopContent").html(html);
            } else {
                html1 += bt1("info_column_empty", programDisplayList[i]);
                $("#desktopContent1").html(html1);
            }
        }
        //根据配置项循环调用接口 的获取数据。
        for (var codeNumber in programConfigList) {
            if (programConfigList[codeNumber].show) {
                programConfigList[codeNumber].loadData(codeNumber);
            }
        }
    }

//4.0
    function getToDoList(programCode) {
        var param = {
            userId: userId,
            appTerminalType: 3
        };
        var reqUrl;

        if (firstLoadTodo) {
            reqUrl = daiban_url + "queryPendingRecords";
        } else {
            param.userCode = loginId;
            reqUrl = daiban_url + "queryAppModuleFreshCount";
        }
       if(gettodoData()){
           showtodolist(gettodoData(),programCode)
       }
        app.ajax({
            url: reqUrl,
            data: param,
            timeout: 30000,
            method: "POST",
            contentType: "application/json",
            headers: {
                contentType: "application/json"
            },
            async: true,
            success: function (res) {
                var result = eval('(' + res.returnValue + ')');
                showtodolist(result,programCode)
                settodoData(result)
            },
            fail: function (res) {
                if (requestCount >= 2) {
                    return;
                }
                requestCount++;
                getToDoList(programCode);
            }
        });
    }
    function showtodolist(result,programCode){
        try{
        programConfigList[programCode].todoList = [];
        for (var i = 0; i < result.data.length; i++) {
            result.data[i].appIcon = result.data[i].iconUrl;
            result.data[i].appEntry = result.data[i].url;
            result.data[i].pendingCount = result.data[i].count;
            programConfigList[programCode].todoList[i] = result.data[i];
        }
        var list =programConfigList[programCode].todoList
        list.forEach(function(val){
            var a =  val.appIcon.split('/')
            var imgName =a[a.length-1];
            val.appIcon = './img1/'+imgName
            if(val.appCode=='GWCL'){
                val.appIcon = './img1/Cv3Le13boxOACk6cAAAI8UTAuHA998.png'
            }
        })
        setToDoView(programCode);
        firstLoadTodo = false;
        }catch (e){
            console.log(e)
        }
    }
//    渲染待办视图
    function setToDoView(programCode) {
//        var appList = [];
        var allList = [];
        for (var i = 0; i < programConfigList[programCode].todoList.length; i++) {
            if (programConfigList[programCode].todoList[i].pendingCount > 0) {//待办数>0
//                appList.push(programConfigList[programCode].todoList[i]);
                allList.push(programConfigList[programCode].todoList[i]);
            }
        }
        for (var i = 0; i < programConfigList[programCode].todoList.length; i++) {
            if (programConfigList[programCode].todoList[i].pendingCount <= 0) {
                allList.push(programConfigList[programCode].todoList[i]);
            }
        }
        var bt = baidu.template;
        var html = bt("info_column_content_todo_icon_table", {
            programCode: programCode,
//            appList: appList,
            allList: allList,
            lineNum: column_line_icon_number
        });
        $(".info-column-content[value='" + programCode + "']").html(html);
        $(".info-column-content[value='" + programCode + "']").uiwidget();

        var coulca = 0;
        $(".info-column-content[value='" + programCode + "'] .icon-badge").each(function () {
            if ($(this).html() != "") {
                coulca += parseInt($(this).html());
            }
        })
        if (coulca > 0) {
            $("#couledaiban").show();
            $("#couledaiban").html(coulca)
        } else {
            $("#couledaiban").hide();
        }
    }

    var allApp;
    //  5.0  通过接口获取应用列表
    function getAllApp(programCode) {
        $(".info-column-content[value='" + programCode + "']").html('<table class="info-column-content-icon-table" id="icon_table_myapp" value="' + programCode + '"></table>');
        getProgramContentLocal(programCode, function (res) {
            if (res && res != "") {
//                修改缓存的图片
                var result = eval('(' + res + ')');
                var list = result.data[0].appList
//                var val = list[0]
                list.forEach(function(val){
                    var a =  val.jmtAppIcon.split('/')
                    var imgName =a[a.length-1];
                    val.jmtAppIcon = './img1/'+imgName
                })
                result.data[0].appList =list
                setAllAppList(programCode, JSON.stringify(result));
            }
        });
        var param = {
            URL_TYPE: "GAW",
            APP_URL: applist_url + "list"
//                userId: userId
        };
        xh.jw_post(applist_url + "list", param, function (res) {
            setAllAppList(programCode, res.returnValue);
            saveProgramContentLocal(programCode, res.returnValue);
        }, function (res) {
        });
    }




// 6  渲染我的应用列表
    function setAllAppList(programCode, resValue) {
        var result = eval('(' + resValue + ')');
        userAppData = result.data;
        for (var Q = 0; Q < result.data.length; Q++) {
            allApp = result.data[Q].appList;
        }
        var showCount = allApp.length;
        var setHide = false;//是否隐藏，应用数量超过显示的数量showLimit行数 * column_line_icon_number个数就隐藏其余的
        if (showCount > programConfigList[programCode].showLimit * column_line_icon_number) {
            showCount = programConfigList[programCode].showLimit * column_line_icon_number;
            setHide = true;
        }
        var iconCount = showCount + 1;

        var bt = baidu.template;
        var html = "";
        for (var i = 0; i < programConfigList[programCode].showLimit; i++) {
            if (setHide) {
                html += bt("icon_table_row", {
                    value: "shown",
                    columnName: "my-app",
                    lineIndex: i,
                    lineNum: column_line_icon_number
                });
            } else {
                html += bt("icon_table_row", {
                    value: "",
                    columnName: "my-app",
                    lineIndex: i,
                    lineNum: column_line_icon_number
                });
            }

            if ((i + 1) * column_line_icon_number >= iconCount) {
                break;
            }
        }
        $("#icon_table_myapp").html(html);

        for (var i = 0; i < showCount; i++) {
            if (allApp[i].appName != "常用电话") {
                html = bt("icon_table_grid", allApp[i]);
                $(".my-app[value='" + i + "']").html(html);
            }
        }



        if (setHide) {
            var allIconCount = allApp.length + 1;
            var allHtml = "";
            for (var i = 0; ; i++) {
                allHtml += bt("icon_table_row", {
                    value: "hidden",
                    columnName: "my-app",
                    lineIndex: i,
                    lineNum: column_line_icon_number
                });

                if ((i + 1) * column_line_icon_number >= allIconCount) {
                    break;
                }
            }
            $("#icon_table_myapp").append(allHtml);
            for (var i = 0; i < allApp.length; i++) {
                allHtml = bt("icon_table_grid", allApp[i]);
                $("tr[value='hidden'] .my-app[value='" + i + "']").html(allHtml);
            }
        }
//            $("#icon_table_myapp").uiwidget();
        $(".info-column-content[value='" + programCode + "']").uiwidget();
        if (programConfigList[programCode].showAllIcon) {
            $(".info-column-content-icon-table[value='" + programCode + "'] tr[value='shown']").hide();
            $(".info-column-content-icon-table[value='" + programCode + "'] tr[value='hidden']").show();
        }
    }

//7 获取测试app
    function getUserTestApps() {
        var param = {
            APP_URL: applist_url + "tsetList",
            userCode: loginId
        };
        xh.jw_post(applist_url + "tsetList", param, function (res) {
            var result = eval('(' + res.returnValue + ')');
            if (result.success != true) {
                return;
            }
            var bt = baidu.template;
            var html = "";
            for (var i = 0; i < result.data.length; i++) {
                if (result.data[i].appList.length != 0) {
                    result.data[i].lineNum = column_line_icon_number;
                    html += bt("info_column_test_app", result.data[i]);
                }
            }
            if (html == "") {
                return;
            }
            $("#testAppContent").html(html);
            $("#test_app").show();
        }, function (res) {
        });
    }

//显示全部还是
    function showOrHideAllIcon(programCode) {
        if (programConfigList[programCode].showAllIcon) {
            $(".info-column-content-icon-table[value='" + programCode + "'] tr[value='hidden']").hide();
            $(".info-column-content-icon-table[value='" + programCode + "'] tr[value='shown']").show();
            $(".info-column-header-right[value='" + programCode + "'] img").attr("src", "image/icon_down.jpg");
        } else {
            $(".info-column-content-icon-table[value='" + programCode + "'] tr[value='shown']").hide();
            $(".info-column-content-icon-table[value='" + programCode + "'] tr[value='hidden']").show();
            $(".info-column-header-right[value='" + programCode + "'] img").attr("src", "image/icon_up.jpg");
        }
        programConfigList[programCode].showAllIcon = !programConfigList[programCode].showAllIcon;
        $(".info-column-content-icon-table[value='" + programCode + "']").uiwidget();
    }


//设置我的应用本地缓存
    function saveProgramContentLocal(programCode, programContent, callback) {
        Cordova.exec(function () {
            if (callback) {
                callback();
            }
        }, null, "Setting", "saveProgramContent", [userId, programCode, programContent]);
    }
    //获取我的应用本地缓存
    function getProgramContentLocal(programCode, callback) {
        Cordova.exec(function (result) {
            var ret = eval('(' + result + ')');
            if (ret.data[0].appList != "") {
                apptrue = false;
            } else {
                apptrue = true;
            }
            if (callback) {
                callback(result);
            }
        }, null, "Setting", "getProgramContent", [userId, programCode]);
    }




    function refreshToDoList(programCode, params) {
        if (params != null && params != "") {
            var refreshTodoList = eval(params);
            for (var i = 0; i < refreshTodoList.length; i++) {
                for (var j = 0; j < programConfigList[programCode].todoList.length; j++) {
                    if (programConfigList[programCode].todoList[j].appId == refreshTodoList[i].appId && programConfigList[programCode].todoList[j].moduleName == refreshTodoList[i].moduleName) {
                        programConfigList[programCode].todoList[j].pendingCount = refreshTodoList[i].count;
                    }
                }
            }

            setToDoView(programCode);
        }
    }


    function showPreLoadIcon(flag) {
        switch (flag) {
            case 0:
                $("#openDownloadList").hide();
                break;
            case 1:
                $("#openDownloadList").show();
                break;
        }
    }

//打开我的应用，待办应用
    function openAppById(appId, appEntry, appName, appVersion) {
        if (!enableOpenApp) {
            return;
        }
        enableOpenApp = false;
        setTimeout("enableOpenApp = true;", 1000);
            if(!isonline){
                app.hint('当前网络不可用')
                return;
            }
        var params = {
            appCode: appId,
            appUrl: appEntry,
            appName: appName,
            appVersion: appVersion,
            data: {}
        };
        if (params.appCode == "XINGHUO_test_ADMINa") {
            params.appCode = "XINGHUO_ADMIN";
        }
        Cordova.exec(onSuccess, onError, "Page", "loadApp2", [params.appCode, params.appUrl, params.data]);
        function onSuccess(res) {
            console.log(res)
        }

        function onError(err) {
            console.log(err)
        }
    }

// ———————————————— 通知公告工作动态——————————————————————————————————
    var AnnouncementToken;
    var announceUnreadCount = 0;
    var workUnreadCount = 0;
    function getAnnouncementToken(updatePoint) {
      if(getnoticeData()) { showAnnouncement(getnoticeData())}
        if(getworkData()) { showWorkMomentData(getworkData())}
        var tokenUrl = announcement_url + "sys/token";
        var params = {
            loginId: loginId
        };
        xh.jw_get(tokenUrl, params, function (res) {
            var ret = eval('(' + res.returnValue + ')');
            if(ret.code ==0){
                AnnouncementToken = ret.access_token;
                if (updatePoint == "notice") {
                    getAnnouncementData(AnnouncementToken);
                } else if (updatePoint == "workDynamic") {
                    getWorkMomentData(AnnouncementToken);
                } else {
                    getAnnouncementData(AnnouncementToken);
                    getWorkMomentData(AnnouncementToken);
                }
            }
            $(".onlineerro").hide()
            isonline =true
        }, function (err) {
            $(".onlineerro").show()
            isonline =false
            ui.hideMask();
        }, function (e) {

        })

    }
//通知公告工作动态本地存储
    function setLocalData(noticeData,workData){
        if(noticeData && noticeData.code ==0){
            localStorage.setItem("noticeData",JSON.stringify(noticeData))
        }
        if(workData && workData.code ==0){
            localStorage.setItem("workData",JSON.stringify(workData))
        }
    }
    function getnoticeData(){
        return JSON.parse(localStorage.getItem("noticeData"))
    }
    function getworkData(){
        return JSON.parse(localStorage.getItem("workData"))
    }
    function settodoData(data){
        localStorage.setItem("todoData",JSON.stringify(data))
    }
    function gettodoData(){
        return JSON.parse(localStorage.getItem("todoData"))
    }
//通知公告
    function getAnnouncementData(token) {
        var requestUrl = announcement_url + "oa/info/notifyList";
        var params = {
            type: 1,
            num: 2,
            access_token: token
        };
        xh.jw_get(requestUrl, params, function (res) {
            ui.hideMask();
            var ret = eval('(' + res.returnValue + ')');
            showAnnouncement(ret)
            setLocalData(ret)
        }, function (err) {
            ui.hideMask();
            console.error(err);
        }, token)
    }
    function showAnnouncement(ret){
        announceUnreadCount = ret.notReadNum;
        if (announceUnreadCount && announceUnreadCount > 0) {
            $("#announceUnreadCount").html(announceUnreadCount);
            $("#announceUnreadCount").show();
        } else {
            $("#announceUnreadCount").hide();
        }
        var oaInfoList = ret.oaInfoList;
        renderAnnouncement(oaInfoList);
    }
    function renderAnnouncement(result) {
//            console.debug(result);
        if (result.length > 0) {
            var html = "";
            try {
                var length;
                if (result.length > 2) {
                    length = 2
                } else {
                    length = result.length
                }
                for (var i = 0; i < length; i++) {
                    var announcement = result[i];
                    var bt = baidu.template;
                    announcement.updateDate = announcement.updateDate.substring(0, 10);
                    html += bt("announcement_entry", announcement);
                }
            } catch (e) {
                console.log("数据有误：" + e);
            }
            $("#announcement").html(html);
            $("#announcement").uiwidget();
        }

    }
//工作动态
    function getWorkMomentData(token) {
        var requestUrl = announcement_url + "/oa/info/notifyList";
        var params = {
            type: 4,
            num: 2,
            access_token: token
        };
        xh.jw_get(requestUrl, params, function (res) {
            ui.hideMask();
            var ret = eval('(' + res.returnValue + ')');
            showWorkMomentData(ret)
            setLocalData("",ret)
        }, function (err) {
            ui.hideMask();
            console.error(err);
        }, function () {

        })

    }
    function showWorkMomentData(ret){
        workUnreadCount = ret.notReadNum;
        if (workUnreadCount && workUnreadCount > 0) {
            $("#workUnreadCount").html(workUnreadCount);
            $("#workUnreadCount").show();
        } else {
            $("#workUnreadCount").hide();
        }
        var oaInfoList = ret.oaInfoList;
        renderWorkMoment(oaInfoList);
    }
    function renderWorkMoment(result) {
        if (result == null || result === 'undefined') return;
        var bt = baidu.template;
        var html = "";
        var length;
        if (result.length > 2) {
            length = 2
        } else {
            length = result.length
        }
        for (var i = 0; i < length; i++) {
            var moment = result[i];
            moment.updateDate = moment.updateDate.substring(0, 10);
            html += bt("work_moment_item", moment);
        }
        $("#workMomentList").html(html);
        $("#workMomentList").uiwidget();

    }
    function refreshRedPoint(updatePoint){
        getAnnouncementToken(updatePoint);
    }
//    拨打电话
    function iphone() {
        ui.confirm('确认拨打信息数据监督室运维电话？',
                '0755-88133855',
                function () {
                    var successCallback = function (result) {

                    };
                    Cordova.exec(successCallback, null, "PhonePlugin", "dial", ["0755-88133855"]);
                }, function () {
                    app.hint("您取消了该操作！")
                })
    }

</script>

<style type="text/css">
    html,body {
        height:100%;
    }
    .shenglvhao {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    .overflowTextOneline {
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
    }

    [data-theme="a"] {
        background-color: #439fd7;
        background-image: -moz-linear-gradient(top, #59b3e8, #2280bd);
        background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#59b3e8), to(#2280bd));
        background-image: -webkit-linear-gradient(top, #59b3e8, #2280bd);
        background-image: -o-linear-gradient(top, #59b3e8, #2280bd);
        background-image: linear-gradient(to bottom, #59b3e8, #2280bd);
        background-repeat: repeat-x;
        color: #ffffff;
        border: 1px solid #156598;
        border-color: #81c5ee;
        text-shadow: 0 0 1px #999;
    }

    .title .navbar li [data-role="BTButton"].btn-active {
        background-color: transparent !important;
        border: 0;
    }

    /*.title .navbar {*/
    /*width: 80px;*/
    /*padding-top: 0;*/
    /*text-align: right;*/
    /*}*/

    .navbar ul[data-menupos="bottom"] .angle {
        bottom: -11px;
        left: 42%;
    }

    .angle {
        background: #434343;
    }

    .navbar [data-role="BTButton"] .btn-text {
        line-height: 30px;
        padding-left: 0;
        font-weight: normal;
        text-shadow: none;
    }

    .title .navbar li [data-role="BTButton"].btn-active {
        color: #FFFFff;
    }

    .triangle-right-black {
        width: 0;
        height: 0;
        border: 10px solid transparent;
        border-bottom: 10px solid #434343;
    }

    .font-size-25 {
        font-size: 25px;
    }

    .font-size-22 {
        font-size: 22px;
    }

    .font-size-20 {
        font-size: 20px;
    }

    .font-size-18 {
        font-size: 18px;
    }

    .font-size-16 {
        font-size: 16px;
    }

    .font-color-333333 {
        color: #333333;
    }

    .font-color-8c8c8c {
        color: #8c8c8c;
    }

    .font-color-c8c8c8 {
        color: #c8c8c8;
    }

    .font-color-1a5df9 {
        color: #1a5df9;
    }

    .font-color-ffffff {
        color: #ffffff;
    }

    .font-color-a9a9a9 {
        color: #a9a9a9;
    }

    .font-color-b3b3b3 {
        color: #b3b3b3;
    }

    .font-color-b1b1b1 {
        color: #b1b1b1;
    }

    .font-color-4783e2 {
        color: #4783e2;
    }

    .font-color-a1a1a1 {
        color: #a1a1a1;
    }

    .info-column {
        margin-left: 2%;
        width: 96%;
        border-radius: 10px;
        background: white;
        margin-bottom: 20px;
    }

    .info-column-header {
        padding-top: 7px;
        padding-bottom: 7px;
    }

    .info-column-header-left {
        width: 8%;
        text-align: left;
        padding-left: 15px;
    }

    .info-column-header-left img {
        width: 25px;
        height: 25px;
    }

    .info-column-header-center {
        width: 70%;
        padding-left: 10px;
    }

    .info-column-header-right {
        width: 22%;
        text-align: right;
        padding-right: 5px;
    }

    .info-column-header-right img {
        width: 16px;
        height: 16px;
    }

    .info-column-content {
        border-top: 1px solid #eeeeee;
        padding-top: 5px;
        padding-bottom: 5px;
    }

    .info-column-content-icon-table {
        margin-top: 5px;
        margin-bottom: 5px;
    }

    .info-column-content-icon-table tr td {
        width: 25%;
        text-align: center;
        padding-bottom: 5px;
        /*border: 1px solid green;*/
    }

    .info-column-content-icon-table tr td div img {
        width: 50px;
        height: 50px;
        margin-bottom: 5px;
    }

    .statistics-chart-navbar {
        padding-left: 15px;
        border-bottom: 1px solid #eeeeee;
    }

    .statistics-chart-navbar div[data-role='BTButton'] {
        width: 50%;
        margin-left: 20px;
        background: white;
        /*color: white;*/
        color: #333333;
        border: none;
        /*border-radius: 10px;*/
        /*padding: 3px 1px 15px 10px;*/
        padding: 3px 0px 15px 7px;
    }

    .statistics-chart-navbar div[data-role='BTButton'][class='btn-active'] {
        width: 50%;
        margin-left: 20px;
        background: white;
        color: #1b80ff;
        /*border: none;*/
        /*border-radius: 10px;*/
        /*padding: 3px 1px 15px 10px;*/
        padding: 3px 0px 15px 7px;
        border-bottom: 3px solid #1b80ff;
    }
</style>
