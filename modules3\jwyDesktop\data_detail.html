<script type="text/javascript">
    //下载保存路径
    var savePath;
    var saveDirectory = "GACloudFiles";
    var detail_id;
    var dataDetail;

    app.section.onReady = function () {
    }

    app.section.onLoad = function () {
        var params = app.getSectionParams();
//        alert(JSON.stringify(params));
        detail_id = params.id;

//        var listFile = params.listFile;
//        for (var i = 0; i < listFile.length; i++) {
//            if (params.id == listFile[i].id) {
//                dataDetail = listFile[i];
//            }
//        }
//        $("#d_name").html(dataDetail.ofname);
////        $("#d_contenttype").html("." + dataDetail.contenttype);
//        $("#d_createName").html(dataDetail.createName);
//        $("#d_createTime").html(dataDetail.createTime);
//        $("#d_fileSize").html(dataDetail.fileSize + "K");
//        $("#d_desc").html(dataDetail.description);
//        $("#d_download_num").html(dataDetail.downloadNum);
//        $("#d_need_score").html(dataDetail.score);
////        alert(dataDetail.avgScore);
//        if( dataDetail.avgScore !=null ){
//            if((dataDetail.avgScore+"").length>1){
//                $("#data_avg_score").html(dataDetail.avgScore );
//            }else{
//                $("#data_avg_score").html(dataDetail.avgScore + ".0");
//            }
//        }
//
//
//
//        showImgType(dataDetail.contenttype);

//        alert(JSON.stringify(dataDetail));
        $("#d_downLoad").tap(function () {
            downloadDecreaseScore();
//            downloadNewFile();
        });
        $("#d_score").tap(function () {
            ui.load({
                url: "score_detail.html",
                params: {id: params.id}
            });
        });
        $("#data_show_comment").tap(function () {
            ui.load({
                url: "comment_list.html",
                params: {id: params.id}
            });
        });

        getSavePath();
        getDataDetailList();

    }
    function refreshDetail(){
        ui.Page.refresh();
    }
    function getDataDetailList() {
        var params = {
            access_token:access_token,
            jwDataId: detail_id,
            userId: userId,
//            URL_TYPE: "JMT",
//            REQUEST_TYPE: "GET",
            pageNo: "1",
            pageSize: "4"
        };
//        alert(JSON.stringify(params));
        xh.jw_$get(url+ "data/jwData/dataDetail", params, function (res) {
            var obj = eval("(" + res.returnValue + ")");
//            alert(JSON.stringify(obj.jwDataEntity));
            var scoreList = obj.result.records;
            var jwDataEntity = obj.jwDataEntity;
            dataDetail = jwDataEntity;
            $("#d_name").html(jwDataEntity.ofname);
            $("#d_createName").html(jwDataEntity.createName);
            $("#d_createTime").html(jwDataEntity.createTime);
            $("#d_fileSize").html(jwDataEntity.fileSize + "K");
            $("#d_desc").html(jwDataEntity.description);
            $("#d_download_num").html(jwDataEntity.downloadNum);
            $("#d_need_score").html(jwDataEntity.score);
            if( jwDataEntity.avgScore !=null ){
                if((jwDataEntity.avgScore+"").length>1){
                    $("#data_avg_score").html(jwDataEntity.avgScore );
                }else{
                    $("#data_avg_score").html(jwDataEntity.avgScore + ".0");
                }
            }
            showImgType(jwDataEntity.contenttype);

            var bt = baidu.template;
            var html = "";
            for (var i = 0; i < scoreList.length; i++) {
                html += bt("dataScoreListItem", scoreList[i]);
            }
            $("#dataScoreList").append(html);
            $("#dataScoreList").uiwidget();
            showFiveScore(obj);
        }, function (error) {
        });
    }
    function showFiveScore(obj){
        var gradeScoreUser = obj.gradeScoreUser;
        var personNum =  obj.PersonNum;
//        alert(JSON.stringify(gradeScoreUser));
        if( personNum!= null){
            $("#scoreTimes").html(personNum+"个评分");
        }

        for(var i = 0; i < gradeScoreUser.length; i++){
            var percent = gradeScoreUser[i].personNum/personNum*100+"%";
            if(gradeScoreUser[i].gradeScore==5){
                $("#fiveScorePercent").css("width", percent);
            }else if(gradeScoreUser[i].gradeScore==4){
                $("#fourScorePercent").css("width", percent);
            }else if(gradeScoreUser[i].gradeScore==3){
                $("#threeScorePercent").css("width", percent);
            }else if(gradeScoreUser[i].gradeScore==2){
                $("#twoScorePercent").css("width", percent);
            }else if(gradeScoreUser[i].gradeScore==1){
                $("#oneScorePercent").css("width", percent);
            }
        }
    }

    function showImgType(contenttype) {
        if ('doc' == contenttype) {
            $("#d_img_type").attr("src", "images/data/DOCX.png");
        } else if ('zip' == contenttype) {
            $("#d_img_type").attr("src", "images/data/ZIP.png");
        } else if ('jpg' == contenttype) {
            $("#d_img_type").attr("src", "images/data/jpg.png");
        } else if ('pdf' == contenttype) {
            $("#d_img_type").attr("src", "images/data/PDF.png");
        } else if ('ppt' == contenttype) {
            $("#d_img_type").attr("src", "images/data/PPT.png");
        } else if ('rar' == contenttype) {
            $("#d_img_type").attr("src", "images/data/RAR.png");
        } else if ('xls' == contenttype) {
            $("#d_img_type").attr("src", "images/data/XLS.png");
        } else if ('zip' == contenttype) {
            $("#d_img_type").attr("src", "images/data/Zip.png");
        }
    }
    //文件下载并打开
    function getSavePath() {
        app.getAppDirectoryEntry(function (res) {
            //区分平台，并将相应的目录保存到全局,方便下面下载的时候使用
//            alert(JSON.stringify(res));     //wangxi
            if (window.devicePlatform == "android") {
                savePath = res.sdcard + "/" + saveDirectory;
            }
        });
    }
    //拷贝目标地址
    var dstDir;
    //缓存照片的最大数量
    var MAX_PHOTOS = 500;
    //文件复制保存路径
    function copyFile2Library(obj, newName) {
        //检查保存路径
//    alert(newName+"  " +savePath);
        checkFile(savePath);
        if (obj.fullPath.indexOf(saveDirectory) > 0) {
            //历史记录，不重复保存
            //                app.hint("file :" + obj.fullPath + " exist.no copy needed.");
            return obj.fullPath;
        }
        //拍照的相片复制到保存路径下
        window.resolveLocalFileSystemURI(obj.fullPath, onSuccess, onError);

        function onSuccess(fileEntry) {
            var dstName = obj.name;
            if (typeof(newName) != "undefined" && newName != null && newName != "") {
                dstName = newName;
            }
            fileEntry.copyTo(dstDir, dstName, null, null);
        }

        function onError(evt) {
            app.hint("保存路径不存在！");
        }

        return dstDir.fullPath + "/" + newName;
    }

    //检查文件路径，没有则创建
    function checkFile(path) {
        window.requestFileSystem(LocalFileSystem.PERSISTENT, 0, onFileSystemSuccess, fail);

        function onFileSystemSuccess(fileSystem) {
            fileSystem.root.getDirectory(path, {
                create: true,
                exclusive: false
            }, success, fail);

            function success(parent) {
                //$("#result").html(JSON.stringify(parent));
                dstDir = parent;
                // 创建一个目录读取器
                var directoryReader = parent.createReader();
                // 获取目录中的所有条目
                directoryReader.readEntries(function (entries) {
                    if (entries.length > MAX_PHOTOS) {
                        // 删除此目录及其所有内容
                        parent.removeRecursively(function () {
                            //重建文件夹
                            //                                        checkFile(savePath);
                            fileSystem.root.getDirectory(path, {
                                create: true,
                                exclusive: false
                            }, success, fail);
                        }, function (error) {
                            app.hint("清空图片缓存文件夹失败！");
                        });
                    }
                    //                        $("#result").html(JSON.stringify(entries));
                }, function (error) {
                    // alert("Failed to list directory contents:" + error.code);
                });
            }

            function fail(error) {
                // app.hint("Unable to create new directory:" + error.code);
            }
        }

        function fail(evt) {
            // app.hint("requestFileSystem : " + evt.target.error.code);
        }
    }

    function openFileIfLocalExist(obj) {
        var fileId = obj.id;
        var fileName = obj.ofname;
        window.resolveLocalFileSystemURI("file:///" + savePath, onSuccess, onError);

        function onSuccess(fileEntry) {
            fileEntry.getFile(fileId + fileName, {create: false, exclusive: false}, success, fail);
            function success(parent) {
//                app.hint("file : " + fileName + " exist,going to open.");
//                alert(JSON.stringify(parent));
                app.openFile(parent.fullPath, "", function (res) {
//                    app.hint("打开成功!");
                });
            }

            function fail(error) {
//                app.hint(JSON.stringify(error));
//                app.hint("正在下载文件...");
                downloadNewFile(obj);
            }
        }

        function onError(evt) {
            app.hint("保存路径不存在！");
            downloadNewFile(obj);
        }
    }
    function downloadDecreaseScore() {
        var params = {
            access_token:access_token,
            jwDataId: detail_id,
            contentType:"application/x-www-form-urlencoded"
        };

//        alert(JSON.stringify(params) +url+ "data/jwData/downLoadData");
//        var url = xh.getRealUrl(0) + "/data/jwData/downLoadData";
        xh.jw_post(url+ "data/jwData/downLoadData", params, function (res) {
//            alert(JSON.stringify(res));
            var obj = eval("(" + res.returnValue + ")");
//            downloadNewFile();
            if ("9" == obj.msg) {
                downloadNewFile();
            }else if("0" == obj.msg){
                app.hint("资料不存在");
            }else if("-1" == obj.msg){
                app.hint("用户信息为空");
            }else if("-2" == obj.msg){
                app.hint("用户的积分值为0");
            }else if("-3" == obj.msg){
                app.hint("用户的积分值不够");
            }
        }, function (error) {

        });
    }
    //下载文件
    function downloadNewFile() {
//        alert(JSON.stringify(111))
        var filePath = savePath + dataDetail.url;
        var uri = openFileUrl+ dataDetail.url;
//        alert(JSON.stringify(uri))
//        alert(JSON.stringify(filePath))
        app.progress.start("请稍候...");
        xh.downloadFile_gm(filePath, uri, success, fail);
        function success(entry) {
            data_is_downLoad = true;
            app.progress.stop();
            //打开文件
            app.openFile(entry.fullPath, "", function (res) {
//                alert(1111+"打开");

            });
        }

        function fail(error) {
            app.hint("下载文件失败!");
        }
    }

    app.section.onBack = function () {
    }

</script>
<style>

</style>
<section id="data_detail" data-transition="slide">
    <!--Header-->
    <div class="header" data-fixed="top">
        <div class="title row-box" style="background: #d93a49">
            <div class="box-left" style="background: #d93a49">
                <div data-role="BTButton" data-type="image" onClick="ui.Page.back();">
                    <img src="../../../css/images/icons/navicon/icon-back.png" alt=""/>
                </div>
            </div>
            <div class="span1" style="background: #d93a49">
                <h1 id="txt_title">资料详情</h1>
            </div>
            <div class="box-right " style="background: #d93a49">
                <div data-role="BTButton" data-type="image" onclick="ui.Page.refresh();">
                    <!--<span style="padding: 10px;color: #ffffff;font-size: 24px;">详情</span>-->
                    <img src="../../../css/images/icons/navicon/icon-refresh.png" alt=""/>
                </div>
            </div>
        </div>
    </div>

    <!--Content-->
    <div class="content iscroll-wrapper">
        <div>
            <div class="thumbnail-text" style="padding: 10px;margin-top: 10px;margin-bottom: 10px;">
                <div class="row-fluid gray-font" style="line-height: 30px;">
                    <div class="span2" align="center" style="color: #b1b1b1; display: flex;">
                        <img id="d_img_type" src="images/data/DOCX.png" height="80px" width="80px;"
                             style="margin-right: 10px;"/>
                    </div>
                    <div class="span7" style="margin-top: 10px;">
                        <div style="color: #000000;font-size: 1.1em;">
                            <span id="d_name"></span>
                            <span id="d_contenttype"></span>&nbsp;&nbsp;
                            <img src="images/data/i_data_score.png">&nbsp;<span id="d_need_score"
                                                                                style="color: #3585F1"></span>
                        </div>
                        <div style="color: #b1b1b1;font-size: 0.9em;margin-top: 10px;">
                            <span id="d_createName"></span>&nbsp;&nbsp;
                            <span id="d_createTime"></span>&nbsp;&nbsp;
                            <span id="d_fileSize"></span>
                        </div>
                    </div>
                    <div class="span3">
                        <div id="d_downLoad" class="downloadbtn" style="float: right">
                            下载
                        </div>
                        <div id="d_score" class="scorebtn" style="margin-top: 10px;float: right">
                            <span>评分</span></div>
                    </div>
                </div>
            </div>
            <div style="height: 1px;background: #f8f8f8;"></div>
            <div class="thumbnail-text" style="padding: 10px;">
                <span style="font-size: 28px;">资料描述</span>
                <div id="d_desc" style="padding: 10px;font-size: larger"></div>
                <!--<div style="padding: 10px;">-->
                <!--所需：<span id="d_need_score" style="color: red;font-size: 26px;"></span>积分-->
                <!--</div>-->
            </div>
            <div style="height: 1px;background: #f8f8f8;"></div>
            <div class="thumbnail-text" style="padding: 10px;">
                <div class="row-fluid gray-font">
                    <div class="span4">
                        <span style="font-size: 28px;">评分及评论</span>
                        <div id="data_avg_score" style="font-size: 76px;text-align: center">0.0</div>
                        <div style="font-size: 20px;text-align: center">满分5分</div>
                    </div>
                    <div class="span3" style="text-align: right">
                        <div style="visibility: hidden">
                            <span style="color: #0085e9">查看全部</span>
                        </div>
                        <div style="margin-top: 20px;">
                            <img src="images/data/start_score_blank.png">
                            <img src="images/data/start_score_blank.png">
                            <img src="images/data/start_score_blank.png">
                            <img src="images/data/start_score_blank.png">
                            <img src="images/data/start_score_blank.png">
                        </div>
                        <div>
                            <img src="images/data/start_score_blank.png">
                            <img src="images/data/start_score_blank.png">
                            <img src="images/data/start_score_blank.png">
                            <img src="images/data/start_score_blank.png">
                        </div>
                        <div>
                            <img src="images/data/start_score_blank.png">
                            <img src="images/data/start_score_blank.png">
                            <img src="images/data/start_score_blank.png">
                        </div>
                        <div>
                            <img src="images/data/start_score_blank.png">
                            <img src="images/data/start_score_blank.png">
                        </div>
                        <div>
                            <img src="images/data/start_score_blank.png">
                        </div>
                    </div>
                    <div class="span5" style="text-align: right">
                        <div  style="color: #0085e9" id="data_show_comment">
                            <span>查看全部</span>
                        </div>
                        <div  style="margin-top: 25px;">
                            <div class="progress" style="height: 10px;margin-top: 5px; ">
                                <div class="bar data-bar-danger" style="width: 80%;height: 10px; "  id="fiveScorePercent"></div>
                            </div>
                            <div class="progress" style="height: 10px; margin-top: -8px;">
                                <div class="bar data-bar-danger" style="width: 40%;height: 10px; "  id="fourScorePercent"></div>
                            </div>
                            <div class="progress" style="height: 10px; margin-top: -8px;">
                                <div class="bar data-bar-danger" style="width: 20%;height: 10px; "  id="threeScorePercent"></div>
                            </div>
                            <div class="progress" style="height: 10px;margin-top: -8px; ">
                                <div class="bar data-bar-danger" style="width: 10%;height: 10px; "  id="twoScorePercent"></div>
                            </div>
                            <div class="progress" style="height: 10px;margin-top: -8px; ">
                                <div class="bar data-bar-danger" style="width: 5%;height: 10px; "  id="oneScorePercent"></div>
                            </div>
                            <div style="color: #b1b1b1;" id="scoreTimes">
                                0个评分
                            </div>
                        </div>
                    </div>
                </div>

            </div>
            <div style="height: 1px;background: #f8f8f8"></div>
            <div>
            <ul class="list-view " data-corner="none;" id="dataScoreList" style="padding-bottom: 10px;">
            </ul>
        </div>
        </div>
        <script type="text/html" id="dataScoreListItem">
            <div style="height: 80px;padding: 10px;">
                <div class="thumbnail-text" style="margin-left: 10px;">
                    <div class="row-fluid gray-font" style="line-height: 30px;display: flex">

                        <div class="span7">
                            <div style="color: #000000;font-size: 1.1em;">
                                <span><%=gradeScoreName%></span>
                            </div>
                            <div style="color: #b1b1b1;font-size: 1.1em;">
                                <%if('1'==gradeScore){%>
                                <img src="images/data/start_score.png">
                                <%} else if('2'==gradeScore) {%>
                                <img src="images/data/start_score.png">
                                <img src="images/data/start_score.png">
                                <%} else if('3'==gradeScore) {%>
                                <img src="images/data/start_score.png">
                                <img src="images/data/start_score.png">
                                <img src="images/data/start_score.png">
                                <%} else if('4'==gradeScore) {%>
                                <img src="images/data/start_score.png">
                                <img src="images/data/start_score.png">
                                <img src="images/data/start_score.png">
                                <img src="images/data/start_score.png">
                                <%} else if('5'==gradeScore) {%>
                                <img src="images/data/start_score.png">
                                <img src="images/data/start_score.png">
                                <img src="images/data/start_score.png">
                                <img src="images/data/start_score.png">
                                <img src="images/data/start_score.png">
                                <%}%>
                            </div>
                        </div>
                        <div class="span5" style="color: #b1b1b1;text-align: right ">
                            <span><%=gradeScoreTime%></span>
                        </div>
                    </div>
                </div>
            </div>
            <div style="color: #b1b1b1;font-size: 1.1em;">
                <%if(null==userContent){%>
                <%} else {%>
                <span style="margin-left: 20px;"><%=userContent%></span>
                <%}%>
            </div>
            <div style="height: 1px;background: #f8f8f8"></div>
        </script>
    </div>

</section>