2025-06-26 16:01:52.629 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:01:52:629]: fetchMucSpecification:: retCode: 200
2025-06-26 16:01:52.637 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:01:52:637]: callback getNetState
2025-06-26 16:01:52.641 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:01:52:641]: httprequest perform: http://10.248.97.237:12001/easemob-szjw/chat/chatgroups/125533052534785?version=v3&resource=android_ca60c3d1-4ec6-4158-9f57-0b76c1fe3baf
2025-06-26 16:01:52.643 14428-16365/com.xinghuo.jingxin D/TrafficStats: tagSocket(177) with statsTag=0xffffffff, statsUid=-1
2025-06-26 16:01:52.703 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:01:52:702]: fetchMucSpecification:: retCode: 200
2025-06-26 16:01:52.712 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:01:52:712]: callback getNetState
2025-06-26 16:01:52.717 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:01:52:717]: httprequest perform: http://10.248.97.237:12001/easemob-szjw/chat/chatgroups/210515278233601?version=v3&resource=android_ca60c3d1-4ec6-4158-9f57-0b76c1fe3baf
2025-06-26 16:01:52.718 14428-16365/com.xinghuo.jingxin D/TrafficStats: tagSocket(177) with statsTag=0xffffffff, statsUid=-1
2025-06-26 16:01:52.766 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:01:52:765]: fetchMucSpecification:: retCode: 200
2025-06-26 16:01:52.776 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:01:52:776]: callback getNetState
2025-06-26 16:01:52.782 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:01:52:782]: httprequest perform: http://10.248.97.237:12001/easemob-szjw/chat/chatgroups/134410850598913?version=v3&resource=android_ca60c3d1-4ec6-4158-9f57-0b76c1fe3baf
2025-06-26 16:01:52.784 14428-16365/com.xinghuo.jingxin D/TrafficStats: tagSocket(177) with statsTag=0xffffffff, statsUid=-1
2025-06-26 16:01:52.825 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:01:52:825]: fetchMucSpecification:: retCode: 200
2025-06-26 16:01:52.837 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:01:52:836]: callback getNetState
2025-06-26 16:01:52.841 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:01:52:841]: httprequest perform: http://10.248.97.237:12001/easemob-szjw/chat/chatgroups/179871452037121?version=v3&resource=android_ca60c3d1-4ec6-4158-9f57-0b76c1fe3baf
2025-06-26 16:01:52.842 14428-16365/com.xinghuo.jingxin D/TrafficStats: tagSocket(177) with statsTag=0xffffffff, statsUid=-1
2025-06-26 16:01:52.898 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:01:52:897]: fetchMucSpecification:: retCode: 200
2025-06-26 16:01:52.906 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:01:52:906]: callback getNetState
2025-06-26 16:01:52.910 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:01:52:910]: httprequest perform: http://10.248.97.237:12001/easemob-szjw/chat/chatgroups/213057802403841?version=v3&resource=android_ca60c3d1-4ec6-4158-9f57-0b76c1fe3baf
2025-06-26 16:01:52.911 14428-16365/com.xinghuo.jingxin D/TrafficStats: tagSocket(177) with statsTag=0xffffffff, statsUid=-1
2025-06-26 16:01:52.972 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:01:52:972]: fetchMucSpecification:: retCode: 200
2025-06-26 16:01:52.980 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:01:52:980]: callback getNetState
2025-06-26 16:01:52.984 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:01:52:983]: httprequest perform: http://10.248.97.237:12001/easemob-szjw/chat/chatgroups/179810769895425?version=v3&resource=android_ca60c3d1-4ec6-4158-9f57-0b76c1fe3baf
2025-06-26 16:01:52.985 14428-16365/com.xinghuo.jingxin D/TrafficStats: tagSocket(177) with statsTag=0xffffffff, statsUid=-1
2025-06-26 16:01:53.033 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:01:53:032]: fetchMucSpecification:: retCode: 200
2025-06-26 16:01:53.042 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:01:53:042]: callback getNetState
2025-06-26 16:01:53.047 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:01:53:047]: httprequest perform: http://10.248.97.237:12001/easemob-szjw/chat/chatgroups/118544409886721?version=v3&resource=android_ca60c3d1-4ec6-4158-9f57-0b76c1fe3baf
2025-06-26 16:01:53.048 14428-16365/com.xinghuo.jingxin D/TrafficStats: tagSocket(177) with statsTag=0xffffffff, statsUid=-1
2025-06-26 16:01:53.099 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:01:53:099]: fetchMucSpecification:: retCode: 200
2025-06-26 16:01:53.111 14428-16365/com.xinghuo.jingxin E/FXP1: LoadContactsActivity getJoinedGroupsFromServer roomIds 141388761071617,131868201648129,181782410493953,122347780636673,123628310036481,139735717707777,135588678270977,129335474323457,125533052534785,134410850598913,179871452037121,179810769895425,118544409886721,
2025-06-26 16:01:53.129 14428-14428/com.xinghuo.jingxin E/FXP1: LoadContactsActivity getIMGroupsFromServer handler 0
2025-06-26 16:01:53.135 14428-14428/com.xinghuo.jingxin E/RtgSchedManager: endActivityTransaction: margin state not match
2025-06-26 16:01:53.135 14428-14428/com.xinghuo.jingxin V/ActivityThread: Handling launch of ActivityRecord{4489624 token=android.os.BinderProxy@6f1eeb7 {com.xinghuo.jingxin/com.xinghuo.desktoptools.activity.HomeActivityEx}}
2025-06-26 16:01:53.142 14428-14428/com.xinghuo.jingxin V/ActivityThread: callActivityOnCreate
2025-06-26 16:01:53.173 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:01:53:173]: [HomeActivityEx] showExceptionDialogFromIntent
2025-06-26 16:01:53.174 14428-14428/com.xinghuo.jingxin D/HomeActivityEx: bucket:10
2025-06-26 16:01:53.174 14428-14428/com.xinghuo.jingxin E/FXP: HomeActivityEx-loadUserInfo
2025-06-26 16:01:53.176 14428-14428/com.xinghuo.jingxin E/FXP: clearLocalDebugLog
2025-06-26 16:01:53.177 14428-14428/com.xinghuo.jingxin E/FXP: clearLightAppZip
2025-06-26 16:01:53.179 14428-14428/com.xinghuo.jingxin I/ActivityThread: add activity client record, r= ActivityRecord{4489624 token=android.os.BinderProxy@6f1eeb7 {com.xinghuo.jingxin/com.xinghuo.desktoptools.activity.HomeActivityEx}} token= android.os.BinderProxy@6f1eeb7
2025-06-26 16:01:53.180 14428-14484/com.xinghuo.jingxin D/HnContentRecognizerManager: getCloudConfigsBundle: cache
2025-06-26 16:01:53.180 14428-14484/com.xinghuo.jingxin D/HnContentRecognizerManager: getCloudConfigsBundle: cache
2025-06-26 16:01:53.180 14428-14484/com.xinghuo.jingxin D/HnContentRecognizerManager: isContentRecognizerEnable: mIsPackageConfigured false
2025-06-26 16:01:53.180 14428-14484/com.xinghuo.jingxin I/ContentDelivererImpl: isFeatureOpen: true
2025-06-26 16:01:53.180 14428-14484/com.xinghuo.jingxin D/HnContentRecognizerManager: isFeatureEnable: cache
2025-06-26 16:01:53.180 14428-14487/com.xinghuo.jingxin D/HnContentRecognizerManager: handleMessage: mCurrentPackageName com.xinghuo.jingxin
2025-06-26 16:01:53.180 14428-14484/com.xinghuo.jingxin D/HnContentRecognizerManager: isContentRecognizerEnable: mIsPackageConfigured false
2025-06-26 16:01:53.181 14428-14428/com.xinghuo.jingxin E/FXP: RetrofitUtil url - http://*************:80/supp/httpClient/sso/oauth/check_token
2025-06-26 16:01:53.181 14428-14428/com.xinghuo.jingxin E/FXP1: HomeActivityEx-checkUserDbTime
2025-06-26 16:01:53.181 14428-14428/com.xinghuo.jingxin E/FXP1: HomeActivityEx-checkUserDbTime-同步数据（增量）
2025-06-26 16:01:53.181 14428-14428/com.xinghuo.jingxin E/FXP1: upDateDept
2025-06-26 16:01:53.182 14428-14487/com.xinghuo.jingxin D/HnContentRecognizerManager: handleMessage: activityName com.xinghuo.desktoptools.activity.HomeActivityExmAllConfigs PackageAllConfigs{mActivityName='com.xinghuo.desktoptools.activity.HomeActivityEx', mBundle=Bundle[mParcelledData.dataSize=376], mWhiteList=null, mBlackList=null, mIsPackageConfigured=false, mIsAllowAll=false}
2025-06-26 16:01:53.184 14428-14428/com.xinghuo.jingxin E/FXP1: RetrofitUtil GetObservable url - http://*************:80/supp/httpClient/users/v2/front/dept/incrementSimple
2025-06-26 16:01:53.184 14428-14428/com.xinghuo.jingxin E/FXP1: upDateUser
2025-06-26 16:01:53.190 14428-14428/com.xinghuo.jingxin E/FXP1: RetrofitUtil GetObservable url - http://*************:80/supp/httpClient/users/v2/front/user/incrementSimple
2025-06-26 16:01:53.194 14428-16382/com.xinghuo.jingxin D/TrafficStats: tagSocket(212) with statsTag=0xffffffff, statsUid=-1
2025-06-26 16:01:53.199 14428-14428/com.xinghuo.jingxin D/UriCovertUtil: covertUriIfNeed,  new uri authority: com.hihonor.android.launcher.settings
2025-06-26 16:01:53.207 14428-14428/com.xinghuo.jingxin E/BadgeRadioButton: setBadgeNumber: -1
2025-06-26 16:01:53.212 14428-16385/com.xinghuo.jingxin D/TrafficStats: tagSocket(177) with statsTag=0xffffffff, statsUid=-1
2025-06-26 16:01:53.215 14428-14428/com.xinghuo.jingxin E/FXP1: RetrofitUtil GetObservable url - http://*************:80/supp/httpClient/blogservice/message/v1/getNoReadCount
2025-06-26 16:01:53.216 14428-14428/com.xinghuo.jingxin E/UpdateAppUtils: get user info failed
2025-06-26 16:01:53.218 14428-14428/com.xinghuo.jingxin E/UpdateAppUtils: 2239,/storage/emulated/10/Android/data/com.xinghuo.jingxin/files/apk/EasyOA_V2239.apk
2025-06-26 16:01:53.218 14428-16386/com.xinghuo.jingxin D/TrafficStats: tagSocket(243) with statsTag=0xffffffff, statsUid=-1
2025-06-26 16:01:53.221 14428-14428/com.xinghuo.jingxin W/FileDownloader.DownloadServiceNotConnectedHelper: request get the status for the task[0] in the download service, but the download service isn't connected yet.
    You can use FileDownloader#isServiceConnected() to check whether the service has been connected, 
    besides you can use following functions easier to control your code invoke after the service has been connected: 
    1. FileDownloader#bindService(Runnable)
    2. FileDownloader#insureServiceBind()
    3. FileDownloader#insureServiceBindAsync()
2025-06-26 16:01:53.223 14428-14428/com.xinghuo.jingxin E/FXP: RetrofitUtil url - http://*************:80/supp/httpClient/appservice/main/getNewVer
2025-06-26 16:01:53.229 14428-14428/com.xinghuo.jingxin E/UpdateAppUtils: get user info failed
2025-06-26 16:01:53.229 14428-14428/com.xinghuo.jingxin I/DecorView[]: set decor visibility 4
2025-06-26 16:01:53.229 14428-16387/com.xinghuo.jingxin D/TrafficStats: tagSocket(245) with statsTag=0xffffffff, statsUid=-1
2025-06-26 16:01:53.229 14428-16388/com.xinghuo.jingxin D/TrafficStats: tagSocket(246) with statsTag=0xffffffff, statsUid=-1
2025-06-26 16:01:53.237 14428-14428/com.xinghuo.jingxin D/FullScreenUtils: isNeedToHideStatusBar: splitscreenstate=-1
2025-06-26 16:01:53.237 14428-14428/com.xinghuo.jingxin D/InputEventReceiver: dispatchInputInterval 1000000
2025-06-26 16:01:53.238 14428-14428/com.xinghuo.jingxin I/HwForceDarkManager: isSystemInDarkMode isResUiModeYes: false, isDarkMode: false
2025-06-26 16:01:53.238 14428-14428/com.xinghuo.jingxin I/DecorView[]: set decor visibility 0
2025-06-26 16:01:53.238 14428-14428/com.xinghuo.jingxin E/RtgSchedManager: endActivityTransaction: margin state not match
2025-06-26 16:01:53.238 14428-14428/com.xinghuo.jingxin I/VRI[LoadContactsActivity]: send MSG_WINDOW_FOCUS_CHANGED msg
2025-06-26 16:01:53.238 14428-14428/com.xinghuo.jingxin I/InputEventReceiver: consumeEvents focus, 0
2025-06-26 16:01:53.238 14428-14428/com.xinghuo.jingxin E/RtgSchedManager: endActivityTransaction: margin state not match
2025-06-26 16:01:53.240 14428-14428/com.xinghuo.jingxin E/FXP: RetrofitUtil url - http://*************:80/supp/httpClient/sso/oauth/token
2025-06-26 16:01:53.241 14428-14481/com.xinghuo.jingxin D/HWUI: disableOutlineDraw is true
2025-06-26 16:01:53.241 14428-14428/com.xinghuo.jingxin D/PhoneWindow: onViewRootImplSet activityToken android.os.BinderProxy@6f1eeb7,activityClientRecord ActivityRecord{4489624 token=android.os.BinderProxy@6f1eeb7 {com.xinghuo.jingxin/com.xinghuo.desktoptools.activity.HomeActivityEx}},isHnNavigationHide false isHidePrivateFlag = false
2025-06-26 16:01:53.241 14428-14428/com.xinghuo.jingxin D/HwPhoneWindow: onViewRootImplSet activityToken android.os.BinderProxy@6f1eeb7,activityClientRecord ActivityRecord{4489624 token=android.os.BinderProxy@6f1eeb7 {com.xinghuo.jingxin/com.xinghuo.desktoptools.activity.HomeActivityEx}},isHwNavigationHide false
2025-06-26 16:01:53.241 14428-14428/com.xinghuo.jingxin D/HiTouch_PressGestureDetector: onAttached:1
2025-06-26 16:01:53.242 14428-14428/com.xinghuo.jingxin D/FullScreenUtils: isNeedToHideStatusBar: splitscreenstate=-1
2025-06-26 16:01:53.242 14428-14428/com.xinghuo.jingxin I/PhoneWindow: REFRESH_STATUS_BAR_ATTRS OnContentApplyWindowInsetsListener has SYSTEM_UI_LAYOUT_FLAGS
2025-06-26 16:01:53.242 14428-14428/com.xinghuo.jingxin D/FullScreenUtils: isNeedToHideStatusBar: splitscreenstate=-1
2025-06-26 16:01:53.243 14428-14428/com.xinghuo.jingxin I/PhoneWindow: REFRESH_STATUS_BAR_ATTRS OnContentApplyWindowInsetsListener has SYSTEM_UI_LAYOUT_FLAGS
2025-06-26 16:01:53.243 14428-14428/com.xinghuo.jingxin D/FullScreenUtils: isNeedToHideStatusBar: splitscreenstate=-1
2025-06-26 16:01:53.245 14428-14428/com.xinghuo.jingxin E/FXP: HomeFragment setUserVisibleHint
2025-06-26 16:01:53.245 14428-14428/com.xinghuo.jingxin E/FXP: HomeFragment setUserVisibleHint return
2025-06-26 16:01:53.245 14428-14428/com.xinghuo.jingxin E/FXP: HomeFragment setUserVisibleHint
2025-06-26 16:01:53.245 14428-14428/com.xinghuo.jingxin E/FXP: HomeFragment setUserVisibleHint return
2025-06-26 16:01:53.247 14428-14428/com.xinghuo.jingxin E/CordovaFragment: onCreate
2025-06-26 16:01:53.256 14428-14428/com.xinghuo.jingxin I/WebViewFactory: Loading com.google.android.webview version 130.0.6723.58 (code 672305831)
2025-06-26 16:01:53.258 14428-14428/com.xinghuo.jingxin V/ResourcesManager: The following library key has been added: ResourcesKey{ mHash=e70f7750 mResDir=null mSplitDirs=[] mOverlayDirs=[/data/resource-cache/com.android.systemui-neutral-GynO.frro,/data/resource-cache/com.android.systemui-accent-n3iZ.frro,/data/resource-cache/com.android.systemui-dynamic-ClAz.frro] mLibDirs=[/product/app/WebViewGoogle/WebViewGoogle.apk,/product/app/TrichromeLibrary/TrichromeLibrary.apk,/system_ext/framework/androidx.window.extensions.jar] mDisplayId=0 mOverrideConfig=v35 mCompatInfo={480dpi always-compat} mLoaders=[]}
2025-06-26 16:01:53.260 14428-14428/com.xinghuo.jingxin W/ziparchive: Unable to open '/product/app/TrichromeLibrary/TrichromeLibrary.dm': No such file or directory
2025-06-26 16:01:53.260 14428-14428/com.xinghuo.jingxin W/ziparchive: Unable to open '/product/app/TrichromeLibrary/TrichromeLibrary.dm': No such file or directory
2025-06-26 16:01:53.260 14428-14428/com.xinghuo.jingxin W/xinghuo.jingxin: Entry not found
2025-06-26 16:01:53.260 14428-14428/com.xinghuo.jingxin D/nativeloader: InitLlndkLibrariesProduct: libEGL.so:libGLESv1_CM.so:libGLESv2.so:libGLESv3.so:libRS.so:libandroid_net.so:libapexsupport.so:libbinder_ndk.so:libc.so:libcgrouprc.so:libclang_rt.asan-aarch64-android.so:libclang_rt.asan-arm-android.so:libclang_rt.hwasan-aarch64-android.so:libcom.android.tethering.connectivity_native.so:libdl.so:libft2.so:liblog.so:libm.so:libmediandk.so:libnativewindow.so:libneuralnetworks.so:libselinux.so:libsync.so:libvendorsupport.so:libvndksupport.so:libvulkan.so
2025-06-26 16:01:53.260 14428-14428/com.xinghuo.jingxin D/nativeloader: Configuring product-clns-8 for unbundled product apk /product/app/TrichromeLibrary/TrichromeLibrary.apk. target_sdk_version=34, uses_libraries=ALL, library_path=/product/app/WebViewGoogle/lib/arm64:/product/app/WebViewGoogle/WebViewGoogle.apk!/lib/arm64-v8a:/product/app/TrichromeLibrary/TrichromeLibrary.apk!/lib/arm64-v8a:/product/lib64:/system/product/lib64, permitted_path=/data:/mnt/expand:/product/lib64:/system/product/lib64
2025-06-26 16:01:53.260 14428-14428/com.xinghuo.jingxin D/nativeloader: Extending system_exposed_libraries: libautotune.honor.so:libteec.honor.so:libiGraphicsCore.honor.so:libautotune.honor.so:libmediaalgoclient.honor.so:libIsfClient.honor.so:libteec_system.honor.so
2025-06-26 16:01:53.261 14428-14428/com.xinghuo.jingxin D/nativeloader: InitVndkspLibrariesProduct: VNDK is deprecated with product
2025-06-26 16:01:53.261 14428-14428/com.xinghuo.jingxin D/ApplicationLoaders: Returning zygote-cached class loader: /system_ext/framework/androidx.window.extensions.jar
2025-06-26 16:01:53.262 14428-14428/com.xinghuo.jingxin D/nativeloader: Configuring product-clns-9 for unbundled product apk /product/app/WebViewGoogle/WebViewGoogle.apk. target_sdk_version=34, uses_libraries=, library_path=/product/app/WebViewGoogle/lib/arm64:/product/app/WebViewGoogle/WebViewGoogle.apk!/lib/arm64-v8a:/product/app/TrichromeLibrary/TrichromeLibrary.apk!/lib/arm64-v8a:/product/lib64:/system/product/lib64, permitted_path=/data:/mnt/expand:/product/lib64:/system/product/lib64
2025-06-26 16:01:53.271 14428-14428/com.xinghuo.jingxin I/cr_WVCFactoryProvider: version=130.0.6723.58 (672305831) minSdkVersion=29 isBundle=true multiprocess=true packageId=3
2025-06-26 16:01:53.275 14428-14428/com.xinghuo.jingxin D/nativeloader: Load /product/app/WebViewGoogle/WebViewGoogle.apk!/lib/arm64-v8a/libmonochrome.so using ns product-clns-9 from class loader (caller=/product/app/WebViewGoogle/WebViewGoogle.apk): ok
2025-06-26 16:01:53.277 14428-14428/com.xinghuo.jingxin D/nativeloader: Load /system/lib64/libwebviewchromium_plat_support.so using ns product-clns-9 from class loader (caller=/product/app/WebViewGoogle/WebViewGoogle.apk): ok
2025-06-26 16:01:53.278 14428-16396/com.xinghuo.jingxin I/chromium: [0626/160153.278379:INFO:variations_seed_loader.cc(68)] Failed to open file for reading.: No such file or directory (2)
2025-06-26 16:01:53.278 14428-16396/com.xinghuo.jingxin I/chromium: [0626/160153.278590:INFO:variations_seed_loader.cc(68)] Failed to open file for reading.: No such file or directory (2)
2025-06-26 16:01:53.281 14428-14428/com.xinghuo.jingxin I/cr_LibraryLoader: Successfully loaded native library
2025-06-26 16:01:53.281 14428-14428/com.xinghuo.jingxin I/cr_CachingUmaRecorder: Flushed 6 samples from 6 histograms, 0 samples were dropped.
2025-06-26 16:01:53.283 14428-14428/com.xinghuo.jingxin I/cr_CombinedPProvider: #registerProvider() provider:WV.z8@79786e3 isPolicyCacheEnabled:false policyProvidersSize:0
2025-06-26 16:01:53.287 14428-14428/com.xinghuo.jingxin I/cr_PolicyProvider: #setManagerAndSource() 0
2025-06-26 16:01:53.287 14428-14428/com.xinghuo.jingxin W/cr_PlatformSer-Internal: GMS is installed but not enabled
2025-06-26 16:01:53.292 14428-14428/com.xinghuo.jingxin I/cr_CombinedPProvider: #linkNativeInternal() 1
2025-06-26 16:01:53.293 14428-14428/com.xinghuo.jingxin I/cr_AppResProvider: #getApplicationRestrictionsFromUserManager() Bundle[EMPTY_PARCEL]
2025-06-26 16:01:53.293 14428-14428/com.xinghuo.jingxin I/cr_PolicyProvider: #notifySettingsAvailable() 0
2025-06-26 16:01:53.293 14428-14428/com.xinghuo.jingxin I/cr_CombinedPProvider: #onSettingsAvailable() 0
2025-06-26 16:01:53.293 14428-14428/com.xinghuo.jingxin I/cr_CombinedPProvider: #flushPolicies()
2025-06-26 16:01:53.298 14428-16386/com.xinghuo.jingxin D/OKHTTP: : Response >> {"msg":"操作成功","msgType":"NORMAL","obj":"{\"code\":200,\"cmd\":\"/blogservice/message/v1/getNoReadCount\",\"data\":0}","success":true}
2025-06-26 16:01:53.303 14428-16382/com.xinghuo.jingxin D/OKHTTP: : Response >> {"msg":"操作成功","msgType":"NORMAL","obj":"{\"msg\":\"success\",\"code\":200,\"data\":[{\"actualDepartmentId\":\"6987103527239680\",\"childDeptIds\":\"\",\"mobilePhone2\":\"19928816636\",\"available\":1,\"isReceiveMsg\":1,\"userName\":\"黄泽琳\",\"userId\":\"5a47fff7-0467-4ad5-a61d-aaaedfc6b63b\",\"userPhotoPath\":\"http://10.224.182.141:8890/group1/M01/00/AF/CuC2i2csnuuAB5HcAAAJz5TZnDg935.jpg\",\"actualPosition\":\"市纪委常委、市监委委员\",\"userLoginId\":\"huangzelin\",\"mobilePhone\":\"13902989328\",\"listOrder\":4,\"isPublic\":1,\"hxId\":\"5a47fff704674ad5a61daaaedfc6b63b\",\"userType\":1,\"isSendMsgUnlimited\":0,\"lastUpdateTime\":1750920592000}],\"success\":true,\"count\":1}","success":true}
2025-06-26 16:01:53.303 14428-16272/com.xinghuo.jingxin D/OKHTTP: : Response >> {"msg":"操作成功","msgType":"NORMAL","obj":"{\"msg\":\"success\",\"code\":200,\"data\":[],\"success\":true,\"count\":0}","success":true}
2025-06-26 16:01:53.323 14428-16387/com.xinghuo.jingxin D/OKHTTP: : Response >> {"msg":"操作成功","msgType":"NORMAL","obj":"{\t\"cmd\":\"/appservice/main/getNewVer\",\t\"code\":200,\t\"data\":{\t\t\"appId\":\"1044281837108592640\",\t\t\"appName\":\"警信APP端\",\t\t\"appState\":2,\t\t\"appType\":null,\t\t\"appVersion\":\"2239\",\t\t\"appVersionName\":\"V2.23.20250314\",\t\t\"authUserList\":[],\t\t\"createTime\":\"2025-03-13 15:15:51\",\t\t\"createUser\":\"\",\t\t\"createUserId\":\"\",\t\t\"fileName\":\"\",\t\t\"installPackage\":\"http://*************:8008/group1/M02/00/BC/CuC2i2fTiu6AHT67BJg1ZZ2ezUs968.apk\",\t\t\"isOnlineHistory\":null,\t\t\"isStrongRemind\":0,\t\t\"isStrongUpdate\":0,\t\t\"jmtInstallPackage\":\"http://*************:8008/group1/M02/00/BC/CuC2i2fTiu6AHT67BJg1ZZ2ezUs968.apk\",\t\t\"packageMd5\":\"\",\t\t\"updateContent\":\"为解决部分用户在广东省外连接工作网异<span></span>常且返深后网络配置失效等问题，发布移动纪检云APP新版本(V2.2.3.9_250314）。您可点击“立即更新”自动升级安装，也可以关闭本界面稍后升级。若有疑问，请联系信息数据监督室(联系人：贾骅俊，19928875008）。感谢支持！\",\t\t\"updateTime\":\"2025-03-13 15:15:51\",\t\t\"updateUser\":\"\",\t\t\"updateUserId\":\"\",\t\t\"versionId\":\"a54b6407f26742ce9250ae444f6483b8\"\t}}","success":true}
2025-06-26 16:01:53.338 14428-16388/com.xinghuo.jingxin E/UpdateAppUtils: reportInfo onSuccess ：{"msg":"success","code":0,"success":true}
2025-06-26 16:01:53.349 14428-14428/com.xinghuo.jingxin E/webview: setDownloadListener
2025-06-26 16:01:53.350 14428-14428/com.xinghuo.jingxin E/SystemWebView: new SystemWebViewClient
2025-06-26 16:01:53.351 14428-14428/com.xinghuo.jingxin E/SystemWebView: new SystemWebChromeClient
2025-06-26 16:01:53.360 14428-14428/com.xinghuo.jingxin I/Environment: path /storage/emulated/10 state is mounted
2025-06-26 16:01:53.362 14428-14428/com.xinghuo.jingxin I/Cordova: loadUrl - file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html
2025-06-26 16:01:53.363 14428-14428/com.xinghuo.jingxin E/webview: clearCache
2025-06-26 16:01:53.363 14428-14428/com.xinghuo.jingxin E/webview: clearHistory
2025-06-26 16:01:53.363 14428-14428/com.xinghuo.jingxin E/CordovaWebViewImpl: loadUrlIntoView: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html
2025-06-26 16:01:53.363 14428-14428/com.xinghuo.jingxin E/SystemWebViewEngine: this: libcordova.src.org.apache.cordova.engine.SystemWebViewEngine@a5f31ff， webview: libcordova.src.org.apache.cordova.engine.SystemWebView{dfca34 IFEDH.C.. .F....I. 0,0-0,0 #cc78f}loadUrl: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html
2025-06-26 16:01:53.369 14428-14428/com.xinghuo.jingxin E/FXP: HomeFragment onCreate 3 file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html
2025-06-26 16:01:53.376 14428-14428/com.xinghuo.jingxin E/CordovaFragment: onCreateView
2025-06-26 16:01:53.377 14428-14428/com.xinghuo.jingxin I/HwResourcesImpl: handleAddIconBackground resId = 0 return directly 
2025-06-26 16:01:53.382 14428-14428/com.xinghuo.jingxin I/System.out: recordForce value 7
2025-06-26 16:01:53.382 14428-14428/com.xinghuo.jingxin E/CordovaFragment: Started.
2025-06-26 16:01:53.382 14428-14428/com.xinghuo.jingxin E/CordovaFragment: Resumed.
2025-06-26 16:01:53.383 14428-14428/com.xinghuo.jingxin E/SystemWebViewEngine: setPaused: false
2025-06-26 16:01:53.383 14428-14428/com.xinghuo.jingxin I/Cordova: loadUrl - javascript:onHomePageResume()
2025-06-26 16:01:53.383 14428-14428/com.xinghuo.jingxin E/webview: clearCache
2025-06-26 16:01:53.383 14428-14428/com.xinghuo.jingxin E/webview: clearHistory
2025-06-26 16:01:53.383 14428-14428/com.xinghuo.jingxin E/CordovaWebViewImpl: loadUrlIntoView: javascript:onHomePageResume()
2025-06-26 16:01:53.383 14428-14428/com.xinghuo.jingxin E/SystemWebViewEngine: this: libcordova.src.org.apache.cordova.engine.SystemWebViewEngine@a5f31ff， webview: libcordova.src.org.apache.cordova.engine.SystemWebView{dfca34 IFEDH.C.. .F....I. 0,0-0,0 #cc78f}loadUrl: javascript:onHomePageResume()
2025-06-26 16:01:53.383 14428-16438/com.xinghuo.jingxin D/libEGL: [eglDestroySurface] start surface is 0x74348cbf60
2025-06-26 16:01:53.386 14428-16438/com.xinghuo.jingxin W/chromium: [WARNING:viz_main_impl.cc(85)] VizNullHypothesis is disabled (not a warning)
2025-06-26 16:01:53.395 14428-14433/com.xinghuo.jingxin W/xinghuo.jingxin: Missing inline cache for void com.hihonor.fluency.FlingJankDetector.endFling(int)
2025-06-26 16:01:53.407 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:01:53:407]: Java_com_hyphenate_chat_adapter_EMAChatManager_nativeGetConversations 
2025-06-26 16:01:53.409 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:01:53:409]: Java_com_hyphenate_chat_adapter_EMAChatManager_nativeGetConversations 
2025-06-26 16:01:53.412 14428-14428/com.xinghuo.jingxin I/HwResourcesImpl: handleAddIconBackground resId = 0 return directly 
2025-06-26 16:01:53.413 14428-14428/com.xinghuo.jingxin I/HwResourcesImpl: handleAddIconBackground resId = 0 return directly 
2025-06-26 16:01:53.425 14428-14428/com.xinghuo.jingxin E/BadgeRadioButton: setBadgeNumber: -1
2025-06-26 16:01:53.425 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:01:53:425]: Java_com_hyphenate_chat_adapter_EMAChatManager_nativeGetConversations 
2025-06-26 16:01:53.425 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:01:53:425]: Java_com_hyphenate_chat_adapter_EMAChatManager_nativeGetConversations 
2025-06-26 16:01:53.426 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:01:53:425]: httprequest perform: http://10.248.97.237:12001/easemob-szjw/chat/users/22a319c71bd04d0ab6c5389dbe6f03cd/rosters/?version=undefined
2025-06-26 16:01:53.426 14428-16386/com.xinghuo.jingxin D/TrafficStats: tagSocket(314) with statsTag=0xffffffff, statsUid=-1
2025-06-26 16:01:53.429 14428-14428/com.xinghuo.jingxin D/UriCovertUtil: covertUriIfNeed,  new uri authority: com.hihonor.android.launcher.settings
2025-06-26 16:01:53.430 14428-14428/com.xinghuo.jingxin D/MineFragment: onResume
2025-06-26 16:01:53.431 14428-14428/com.xinghuo.jingxin D/MineFragment: MineViewModel.load(), token = f97b011b-0bfb-40b4-a70f-e5b97c35d3e6
2025-06-26 16:01:53.435 14428-16382/com.xinghuo.jingxin D/OkHttp: --> POST http://*************/supp/httpClient/users/user/getUserInfoByUserCodes
2025-06-26 16:01:53.435 14428-16382/com.xinghuo.jingxin D/OkHttp: Content-Type: application/x-www-form-urlencoded
2025-06-26 16:01:53.436 14428-16382/com.xinghuo.jingxin D/OkHttp: Content-Length: 617
2025-06-26 16:01:53.436 14428-16382/com.xinghuo.jingxin D/OkHttp: bodyEntity=%7B%22userids%22%3A%5B%2222a319c7-1bd0-4d0a-b6c5-389dbe6f03cd%22%5D%7D&token=f97b011b-0bfb-40b4-a70f-e5b97c35d3e6&APP_URL=%2Fusers%2Fuser%2FgetUserInfoByUserCodes&contentType=application%2Fjson&headerParameter=%7B%22requestType%22%3A%22zuul%22%7D&APP_NAME=%E7%A7%BB%E5%8A%A8%E7%BA%AA%E6%A3%80%E4%BA%91&APP_VERSION=2.2.3.9&APP_DEVICE=%7B%22deviceUuid%22%3A%22%22%2C%22deviceVersion%22%3A%2215%22%2C%22deviceName%22%3A%22%22%2C%22plateform%22%3A%22Android%22%2C%22model%22%3A%22VER-AN10%22%2C%22bingoDeviceId%22%3A%22%22%2C%22loginId%22%3A%22fxp%22%2C%22meid%22%3A%22%22%2C%22imsi%22%3A%22%22%7D&URL_TYPE=JMT
2025-06-26 16:01:53.436 14428-16382/com.xinghuo.jingxin D/OkHttp: --> END POST (617-byte body)
2025-06-26 16:01:53.439 14428-14428/com.xinghuo.jingxin I/HwResourcesImpl: handleAddIconBackground resId = 0 return directly 
2025-06-26 16:01:53.440 14428-14428/com.xinghuo.jingxin W/xinghuo.jingxin: Accessing hidden field Landroid/view/View;->mAccessibilityDelegate:Landroid/view/View$AccessibilityDelegate; (unsupported, reflection, allowed)
2025-06-26 16:01:53.440 14428-16390/com.xinghuo.jingxin D/OKHTTP: : Response >> {"msg":"操作成功","msgType":"NORMAL","obj":"{\"code\":200,\"cmd\":\"/momentsservice/v1/moments/tagType/queryTagTypeList\",\"data\":[{\"tagTypeId\":\"6c345051256a4fec97e106b4a7e10efd\",\"createTime\":1523179619640,\"isDelete\":0,\"orderBy\":3,\"tagTypeName\":\"工作动态\",\"userId\":\"35fd5aab347547fc842dedcb2ffeb152\",\"tagTypeIndex\":2,\"lastUpdateTime\":1523179619640}]}","success":true}
2025-06-26 16:01:53.454 14428-14428/com.xinghuo.jingxin D/FullScreenUtils: isNeedToHideStatusBar: splitscreenstate=-1
2025-06-26 16:01:53.454 14428-14428/com.xinghuo.jingxin I/PhoneWindow: REFRESH_STATUS_BAR_ATTRS OnContentApplyWindowInsetsListener has SYSTEM_UI_LAYOUT_FLAGS
2025-06-26 16:01:53.454 14428-14428/com.xinghuo.jingxin D/FullScreenUtils: isNeedToHideStatusBar: splitscreenstate=-1
2025-06-26 16:01:53.454 14428-14428/com.xinghuo.jingxin I/PhoneWindow: REFRESH_STATUS_BAR_ATTRS OnContentApplyWindowInsetsListener has SYSTEM_UI_LAYOUT_FLAGS
2025-06-26 16:01:53.454 14428-14428/com.xinghuo.jingxin D/FullScreenUtils: isNeedToHideStatusBar: splitscreenstate=-1
2025-06-26 16:01:53.468 14428-14428/com.xinghuo.jingxin D/VRI[HomeActivityEx]: relayoutWindow: mRelayoutRequested = true
2025-06-26 16:01:53.469 14428-14428/com.xinghuo.jingxin I/BufferQueueConsumer: [](id:385c00000004,api:0,p:-1,c:14428) connect: controlledByApp=false
2025-06-26 16:01:53.469 14428-14428/com.xinghuo.jingxin D/FullScreenUtils: isNeedToHideStatusBar: splitscreenstate=-1
2025-06-26 16:01:53.469 14428-14428/com.xinghuo.jingxin V/VRI[HomeActivityEx]: Visible with new config: {1.0 460mcc11mnc [zh_CN_#Hans] ldltr sw353dp w353dp h719dp 480dpi nrml long hdr widecg port finger -keyb/v/h -nav/h winConfig={ mBounds=Rect(0, 0 - 1060, 2376) mAppBounds=Rect(0, 99 - 1060, 2256) mMaxBounds=Rect(0, 0 - 1060, 2376) mDisplayRotation=ROTATION_0 mWindowingMode=fullscreen mActivityType=standard mAlwaysOnTop=undefined mRotation=ROTATION_0 mPopOverMode=undefined} as.20 hwt:3 suim:1 fontWeightScale:100 changeUserFlag:3 changeUiModeFlag:0 fullScreenVideoFlag:0 splitScreenState:0 landScapeFreeformFlag:1 hoverMode:0 changeIconStyleFlag:1 changeThemeStyleFlag:-1 s.2 fontWeightAdjustment=0}
2025-06-26 16:01:53.470 14428-14428/com.xinghuo.jingxin V/VRI[HomeActivityEx]: Applying new config to window com.xinghuo.jingxin/com.xinghuo.desktoptools.activity.HomeActivityEx, globalConfig: {1.0 460mcc11mnc [zh_CN_#Hans] ldltr sw353dp w353dp h719dp 480dpi nrml long hdr widecg port finger -keyb/v/h -nav/h winConfig={ mBounds=Rect(0, 0 - 1060, 2376) mAppBounds=Rect(0, 99 - 1060, 2256) mMaxBounds=Rect(0, 0 - 1060, 2376) mDisplayRotation=ROTATION_0 mWindowingMode=fullscreen mActivityType=undefined mAlwaysOnTop=undefined mRotation=ROTATION_0 mPopOverMode=undefined} as.20 hwt:3 suim:1 fontWeightScale:100 changeUserFlag:3 changeUiModeFlag:0 fullScreenVideoFlag:0 splitScreenState:0 landScapeFreeformFlag:1 hoverMode:0 changeIconStyleFlag:1 changeThemeStyleFlag:-1 s.1188 fontWeightAdjustment=0}, overrideConfig: {1.0 460mcc11mnc [zh_CN_#Hans] ldltr sw353dp w353dp h719dp 480dpi nrml long hdr widecg port finger -keyb/v/h -nav/h winConfig={ mBounds=Rect(0, 0 - 1060, 2376) mAppBounds=Rect(0, 99 - 1060, 2256) mMaxBounds=Rect(0, 0 - 1060, 2376) mDisplayRotation=ROTATION_0 mWindowingMode=fullscreen mActivityType=standard mAlwaysOnTop=undefined mRotation=ROTATION_0 mPopOverMode=undefined} as.20 hwt:3 suim:1 fontWeightScale:100 changeUserFlag:3 changeUiModeFlag:0 fullScreenVideoFlag:0 splitScreenState:0 landScapeFreeformFlag:1 hoverMode:0 changeIconStyleFlag:1 changeThemeStyleFlag:-1 s.2 fontWeightAdjustment=0}
2025-06-26 16:01:53.470 14428-14481/com.xinghuo.jingxin D/libEGL: [eglCreateWindowSurface] start window is 493225150544
2025-06-26 16:01:53.470 14428-14481/com.xinghuo.jingxin I/BufferQueueProducer: [VRI[HomeActivityEx]#4(BLAST Consumer)4](id:385c00000004,api:0,p:-1,c:14428) connect: api=1 producerControlledByApp=true
2025-06-26 16:01:53.484 14428-14428/com.xinghuo.jingxin I/System.out: -----------start
2025-06-26 16:01:53.489 14428-14428/com.xinghuo.jingxin I/HwForceDarkManager: setAllowedHwForceDark:false package:com.xinghuo.jingxin mCurrProcessState:0 mIsPackageNameChange:false hwForceDarkState:0 isViewAllowedForceDark:true isLastHonorForceDark:false
2025-06-26 16:01:53.503 14428-14428/com.xinghuo.jingxin D/FullScreenUtils: isNeedToHideStatusBar: splitscreenstate=-1
2025-06-26 16:01:53.504 14428-14428/com.xinghuo.jingxin I/PhoneWindow: REFRESH_STATUS_BAR_ATTRS OnContentApplyWindowInsetsListener has SYSTEM_UI_LAYOUT_FLAGS
2025-06-26 16:01:53.504 14428-14428/com.xinghuo.jingxin D/FullScreenUtils: isNeedToHideStatusBar: splitscreenstate=-1
2025-06-26 16:01:53.504 14428-14428/com.xinghuo.jingxin I/PhoneWindow: REFRESH_STATUS_BAR_ATTRS OnContentApplyWindowInsetsListener has SYSTEM_UI_LAYOUT_FLAGS
2025-06-26 16:01:53.504 14428-14428/com.xinghuo.jingxin D/FullScreenUtils: isNeedToHideStatusBar: splitscreenstate=-1
2025-06-26 16:01:53.507 14428-14428/com.xinghuo.jingxin E/FXP: RetrofitUtil url - http://*************:80/supp/httpClient/sso/oauth/token
2025-06-26 16:01:53.510 14428-14428/com.xinghuo.jingxin E/FXP: RequestManager getMessageCount - {"code":200,"cmd":"/blogservice/message/v1/getNoReadCount","data":0}
2025-06-26 16:01:53.512 14428-14428/com.xinghuo.jingxin D/UriCovertUtil: covertUriIfNeed,  new uri authority: com.hihonor.android.launcher.settings
2025-06-26 16:01:53.513 14428-14428/com.xinghuo.jingxin E/BadgeRadioButton: setBadgeNumber: -1
2025-06-26 16:01:53.513 14428-16382/com.xinghuo.jingxin D/OkHttp: <-- 200 http://*************/supp/httpClient/users/user/getUserInfoByUserCodes (77ms)
2025-06-26 16:01:53.513 14428-16382/com.xinghuo.jingxin D/OkHttp: Server: xh_web_server
2025-06-26 16:01:53.513 14428-16382/com.xinghuo.jingxin D/OkHttp: Date: Thu, 26 Jun 2025 07:53:46 GMT
2025-06-26 16:01:53.513 14428-16382/com.xinghuo.jingxin D/OkHttp: Content-Type: text/plain;charset=UTF-8
2025-06-26 16:01:53.513 14428-16382/com.xinghuo.jingxin D/OkHttp: Transfer-Encoding: chunked
2025-06-26 16:01:53.513 14428-16382/com.xinghuo.jingxin D/OkHttp: Connection: keep-alive
2025-06-26 16:01:53.513 14428-16382/com.xinghuo.jingxin D/OkHttp: Vary: Accept-Encoding
2025-06-26 16:01:53.514 14428-14428/com.xinghuo.jingxin I/VRI[HomeActivityEx]: send MSG_WINDOW_FOCUS_CHANGED msg
2025-06-26 16:01:53.514 14428-14428/com.xinghuo.jingxin I/InputEventReceiver: consumeEvents focus, 1
2025-06-26 16:01:53.515 14428-16382/com.xinghuo.jingxin D/OkHttp: {"msg":"操作成功","msgType":"NORMAL","obj":"{\"code\":200,\"cmd\":\"/users/user/getUserInfoByUserCodes\",\"data\":[{\"userId\":\"22a319c7-1bd0-4d0a-b6c5-389dbe6f03cd\",\"userCode\":\"fxp\",\"birthday\":\"\",\"deptCode\":\"1021\",\"gender\":1,\"isDeleted\":0,\"listOrder\":10,\"position\":\"\",\"status\":1,\"userLoginId\":\"fxp\",\"userName\":\"方晓鹏\",\"departmentId\":\"e58c77f1-47d3-41dd-98c7-15e3e791a198\",\"lastUpdateName\":\"OAService\",\"lastUpdateTime\":\"2025-05-07 15:03:11\",\"rank\":null,\"policeRank\":null,\"job\":null,\"py\":null,\"fristLetter\":null,\"idcard\":\"******************\",\"mobilePhone\":\"18589082142\",\"mobilePhone2\":\"18589082142\",\"userStatus\":null,\"currentJob\":null,\"userType\":\"其他\",\"avatarUrl\":null,\"headImgRelative\":null,\"email\":\"\",\"source\":null,\"age\":null,\"imei\":null,\"equipmentNum\":null,\"leadercode\":null,\"identity\":null,\"deptName\":\"支持单位\",\"curstatus\":null,\"jmtAvatarUrl\":\"\",\"userRemark\":\"1\",\"idCard\":null,\"dept\":null,\"integral\":null,\"departmentIds\":[\"e58c77f1-47d3-41dd-98c7-15e3e791a198\"],\"depts\":null,\"isPublic\":1,\"isReceiveMsg\":1,\"isStatistics\":0,\"isSendMsgUnlimited\":0,\"ecNum\":null,\"softPhoneNum\":null,\"officeRoom\":null,\"officePhone\":null,\"remark\":\"1\",\"postLable\":null,\"deptNameList\":null,\"md5\":\"a3ad3b3c29407b243cdc99716541e82a\",\"base64Img\":null,\"userPhotoPath\":\"\",\"userBigPhotoPath\":\"\",\"actualDepartmentId\":null,\"actualPosition\":\"\",\"childDeptIds\":\"\",\"available\":1,\"hxId\":\"22a319c71bd04d0ab6c5389dbe6f03cd\"}]}","success":true}
2025-06-26 16:01:53.515 14428-16382/com.xinghuo.jingxin D/OkHttp: <-- END HTTP (1598-byte body)
2025-06-26 16:01:53.520 14428-14428/com.xinghuo.jingxin E/FXP1: Request User Detail - {"code":200,"count":1,"data":[{"actualDepartmentId":"6987103527239680","actualPosition":"市纪委常委、市监委委员","available":1,"childDeptIds":"","hxId":"5a47fff704674ad5a61daaaedfc6b63b","isDeleted":"1","isPublic":1,"isReceiveMsg":1,"isSendMsgUnlimited":"0","lastUpdateTime":"1750920592000","listOrder":"4","mobilePhone":"13902989328","mobilePhone2":"19928816636","platformType":0,"userId":"5a47fff7-0467-4ad5-a61d-aaaedfc6b63b","userLoginId":"huangzelin","userName":"黄泽琳","userPhotoPath":"http://10.224.182.141:8890/group1/M01/00/AF/CuC2i2csnuuAB5HcAAAJz5TZnDg935.jpg","userType":"1"}],"msg":"success","success":true}
2025-06-26 16:01:53.530 14428-14433/com.xinghuo.jingxin W/xinghuo.jingxin: Missing inline cache for void android.media.MediaCodecInfo$AudioCapabilities.limitSampleRates(android.util.Range[])
2025-06-26 16:01:53.531 14428-14433/com.xinghuo.jingxin W/xinghuo.jingxin: Missing inline cache for void android.media.MediaCodecInfo$AudioCapabilities.getDefaultFormat(android.media.MediaFormat)
2025-06-26 16:01:53.533 14428-14433/com.xinghuo.jingxin W/xinghuo.jingxin: Missing inline cache for void android.media.MediaCodecInfo$EncoderCapabilities.getDefaultFormat(android.media.MediaFormat)
2025-06-26 16:01:53.533 14428-14433/com.xinghuo.jingxin W/xinghuo.jingxin: Missing inline cache for void android.media.MediaCodecInfo$EncoderCapabilities.getDefaultFormat(android.media.MediaFormat)
2025-06-26 16:01:53.534 14428-16419/com.xinghuo.jingxin W/AudioCapabilities: Unsupported mime audio/vnd.dts.hd;profile=dtsma
2025-06-26 16:01:53.535 14428-16419/com.xinghuo.jingxin W/AudioCapabilities: Unsupported mime audio/vnd.dts.hd;profile=lbr
2025-06-26 16:01:53.535 14428-16419/com.xinghuo.jingxin W/AudioCapabilities: Unsupported mime audio/vnd.dts.uhd;profile=p1
2025-06-26 16:01:53.535 14428-16419/com.xinghuo.jingxin W/AudioCapabilities: Unsupported mime audio/vnd.dts.uhd;profile=p2
2025-06-26 16:01:53.535 14428-16419/com.xinghuo.jingxin W/AudioCapabilities: Unsupported mime audio/vnd.dts.hd;profile=dtsma
2025-06-26 16:01:53.535 14428-16419/com.xinghuo.jingxin W/AudioCapabilities: Unsupported mime audio/vnd.dts.hd;profile=lbr
2025-06-26 16:01:53.535 14428-16419/com.xinghuo.jingxin W/AudioCapabilities: Unsupported mime audio/vnd.dts.uhd;profile=p1
2025-06-26 16:01:53.536 14428-16419/com.xinghuo.jingxin W/AudioCapabilities: Unsupported mime audio/vnd.dts.uhd;profile=p2
2025-06-26 16:01:53.540 14428-14428/com.xinghuo.jingxin E/FXP1: updateCurrentUserInfo
2025-06-26 16:01:53.545 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(1)] "Uncaught ReferenceError: onHomePageResume is not defined", source:  (1)
2025-06-26 16:01:53.549 14428-14428/com.xinghuo.jingxin E/FXP1: Request Dept Detail - {"code":200,"count":0,"data":[],"msg":"success","success":true}
2025-06-26 16:01:53.549 14428-14428/com.xinghuo.jingxin E/UpdateAppUtils: CheckNewVersion Result:{	"cmd":"/appservice/main/getNewVer",	"code":200,	"data":{		"appId":"1044281837108592640",		"appName":"警信APP端",		"appState":2,		"appType":null,		"appVersion":"2239",		"appVersionName":"V2.23.20250314",		"authUserList":[],		"createTime":"2025-03-13 15:15:51",		"createUser":"",		"createUserId":"",		"fileName":"",		"installPackage":"http://*************:8008/group1/M02/00/BC/CuC2i2fTiu6AHT67BJg1ZZ2ezUs968.apk",		"isOnlineHistory":null,		"isStrongRemind":0,		"isStrongUpdate":0,		"jmtInstallPackage":"http://*************:8008/group1/M02/00/BC/CuC2i2fTiu6AHT67BJg1ZZ2ezUs968.apk",		"packageMd5":"",		"updateContent":"为解决部分用户在广东省外连接工作网异<span></span>常且返深后网络配置失效等问题，发布移动纪检云APP新版本(V2.2.3.9_250314）。您可点击“立即更新”自动升级安装，也可以关闭本界面稍后升级。若有疑问，请联系信息数据监督室(联系人：贾骅俊，19928875008）。感谢支持！",		"updateTime":"2025-03-13 15:15:51",		"updateUser":"",		"updateUserId":"",		"versionId":"a54b6407f26742ce9250ae444f6483b8"	}}
2025-06-26 16:01:53.550 14428-14428/com.xinghuo.jingxin E/UpdateAppUtils: CheckNewVersion Result:2239,您用的已是最新的版本
2025-06-26 16:01:53.553 14428-16385/com.xinghuo.jingxin D/UriCovertUtil: covertUriIfNeed,  new uri authority: com.hihonor.android.launcher.settings
2025-06-26 16:01:53.555 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:01:53:555]: Java_com_hyphenate_chat_adapter_EMAChatManager_nativeGetConversations 
2025-06-26 16:01:53.557 14428-14428/com.xinghuo.jingxin D/MineFragment: RES={"code":"200","data":[{"actualPosition":"","available":1,"birthday":"","childDeptIds":"","departmentId":"e58c77f1-47d3-41dd-98c7-15e3e791a198","deptCode":"1021","deptName":"支持单位","gender":"1","hxId":"22a319c71bd04d0ab6c5389dbe6f03cd","idcard":"******************","isDeleted":"0","isPublic":1,"isReceiveMsg":1,"isSendMsgUnlimited":"0","jmtAvatarUrl":"","lastUpdateName":"OAService","lastUpdateTime":"2025-05-07 15:03:11","listOrder":"10","md5":"a3ad3b3c29407b243cdc99716541e82a","mobilePhone":"18589082142","mobilePhone2":"18589082142","platformType":0,"position":"","status":"1","userBigPhotoPath":"","userCode":"fxp","userId":"22a319c7-1bd0-4d0a-b6c5-389dbe6f03cd","userLoginId":"fxp","userName":"方晓鹏","userPhotoPath":"","userRemark":"1","userType":"其他"}]}
2025-06-26 16:01:53.561 14428-16419/com.xinghuo.jingxin W/VideoCapabilities: Unsupported mime image/vnd.android.heic
2025-06-26 16:01:53.561 14428-16419/com.xinghuo.jingxin W/VideoCapabilities: Unsupported mime image/vnd.android.heic
2025-06-26 16:01:53.561 14428-16419/com.xinghuo.jingxin W/VideoCapabilities: Unsupported mime video/dolby-vision
2025-06-26 16:01:53.562 14428-16419/com.xinghuo.jingxin W/VideoCapabilities: Unsupported mime video/dolby-vision
2025-06-26 16:01:53.571 14428-14428/com.xinghuo.jingxin I/SurfaceControl: nativeRelease 0x723c7fee90 count: 1 name: Surface(name=8d3d89d NavigationBar0)/@0x69cabcd - animation-leash of insets_animation#4889
2025-06-26 16:01:53.571 14428-14428/com.xinghuo.jingxin I/SurfaceControl: nativeRelease 0x723c7e63b0 count: 1 name: Surface(name=76dbbb6 InputMethod SecureIme)/@0x551878d - animation-leash of insets_animation#4893
2025-06-26 16:01:53.571 14428-14428/com.xinghuo.jingxin I/SurfaceControl: nativeRelease 0x723c80fed0 count: 1 name: Surface(name=392bbe6 StatusBar)/@0xa9bcc82 - animation-leash of insets_animation#4890
2025-06-26 16:01:53.574 14428-14428/com.xinghuo.jingxin V/ImeFocusController: onWindowFocus: com.android.internal.policy.DecorView{3184eac V.ED..... R....... 0,0-1060,2376}[HomeActivityEx] softInputMode=STATE_ALWAYS_HIDDEN|ADJUST_NOTHING|IS_FORWARD_NAVIGATION
2025-06-26 16:01:53.574 14428-14428/com.xinghuo.jingxin W/InputMethodManager: startInputReason = 1
2025-06-26 16:01:53.575 14428-14428/com.xinghuo.jingxin V/InputMethodManager: Starting input: editorInfo=android.view.inputmethod.EditorInfo@74ded5b ic=null
2025-06-26 16:01:53.575 14428-14428/com.xinghuo.jingxin V/InputMethodManager: START INPUT: view=com.android.internal.policy.DecorView{3184eac V.ED..... R....... 0,0-1060,2376 aid=3}[HomeActivityEx],focus=false,windowFocus=true,window=android.view.ViewRootImpl$W@9e1f9f8,displayId=0,temporaryDetach=false,hasImeFocus=true ic=null editorInfo=android.view.inputmethod.EditorInfo@74ded5b startInputFlags=VIEW_HAS_FOCUS|INITIAL_CONNECTION
2025-06-26 16:01:53.575 14428-14428/com.xinghuo.jingxin D/VRI[LoadContactsActivity]: visibilityChanged oldVisibility=true newVisibility=false
2025-06-26 16:01:53.575 14428-14428/com.xinghuo.jingxin I/SurfaceControl: nativeRelease 0x723c801050 count: 3 name: com.xinghuo.jingxin/com.xinghuo.desktoptools.activity.LoadContactsActivity#4875
2025-06-26 16:01:53.576 14428-14428/com.xinghuo.jingxin D/MyApplication: onActivityStarted:ComponentInfo{com.xinghuo.jingxin/com.xinghuo.desktoptools.activity.LoadContactsActivity}
2025-06-26 16:01:53.576 14428-14428/com.xinghuo.jingxin I/DecorView[]: set decor visibility 4
2025-06-26 16:01:53.577 14428-14428/com.xinghuo.jingxin D/MyApplication: onActivityStarted:ComponentInfo{com.xinghuo.jingxin/com.xinghuo.desktoptools.activity.LoadContactsActivity}
2025-06-26 16:01:53.577 14428-14428/com.xinghuo.jingxin I/ActivityThread: Remove activity client record, r= ActivityRecord{931d1d8 token=android.os.BinderProxy@dd8b8bb {com.xinghuo.jingxin/com.xinghuo.desktoptools.activity.LoadContactsActivity}} token= android.os.BinderProxy@dd8b8bb
2025-06-26 16:01:53.577 14428-14428/com.xinghuo.jingxin W/WindowOnBackDispatcher: sendCancelIfRunning: isInProgress=false callback=android.view.ViewRootImpl$$ExternalSyntheticLambda13@10a0368
2025-06-26 16:01:53.578 14428-14428/com.xinghuo.jingxin I/VRI[LoadContactsActivity]: dispatchDetachedFromWindow in doDie
2025-06-26 16:01:53.578 14428-14428/com.xinghuo.jingxin D/HiTouch_PressGestureDetector: onDetached:false
2025-06-26 16:01:53.578 14428-16419/com.xinghuo.jingxin W/cr_VAUtil: Unknown level: 2048 for profile 32 of codec video/dolby-vision
2025-06-26 16:01:53.578 14428-16419/com.xinghuo.jingxin W/cr_VAUtil: Unknown profile: 16 for codec video/dolby-vision
2025-06-26 16:01:53.578 14428-16419/com.xinghuo.jingxin W/cr_VAUtil: Unknown level: 2048 for profile 256 of codec video/dolby-vision
2025-06-26 16:01:53.579 14428-16419/com.xinghuo.jingxin W/cr_VAUtil: Unknown level: 2048 for profile 32 of codec video/dolby-vision
2025-06-26 16:01:53.579 14428-16419/com.xinghuo.jingxin W/cr_VAUtil: Unknown profile: 16 for codec video/dolby-vision
2025-06-26 16:01:53.579 14428-16419/com.xinghuo.jingxin W/cr_VAUtil: Unknown level: 2048 for profile 256 of codec video/dolby-vision
2025-06-26 16:01:53.581 14428-14481/com.xinghuo.jingxin D/libEGL: [eglDestroySurface] start surface is 0x74348bd200
2025-06-26 16:01:53.581 14428-14481/com.xinghuo.jingxin I/BufferQueueProducer: [VRI[LoadContactsActivity]#3(BLAST Consumer)3](id:385c00000003,api:1,p:14428,c:14428) disconnect: api 1
2025-06-26 16:01:53.582 14428-14481/com.xinghuo.jingxin I/BufferQueueConsumer: [VRI[LoadContactsActivity]#3(BLAST Consumer)3](id:385c00000003,api:0,p:-1,c:14428) disconnect
2025-06-26 16:01:53.583 14428-16272/com.xinghuo.jingxin D/OKHTTP: : Response >> {"msg":"操作成功","msgType":"NORMAL","obj":"{\"access_token\":\"f97b011b-0bfb-40b4-a70f-e5b97c35d3e6\",\"token_type\":\"bearer\",\"refresh_token\":\"ad279cce-a61d-4e45-903f-6116accb3657\",\"expires_in\":694093,\"scope\":\"all\"}","success":true}
2025-06-26 16:01:53.583 14428-14428/com.xinghuo.jingxin I/SurfaceControl: nativeRelease 0x723c7ee630 count: 1 name: Surface(name=76dbbb6 InputMethod SecureIme)/@0x551878d - animation-leash of insets_animation#4893
2025-06-26 16:01:53.583 14428-14428/com.xinghuo.jingxin E/RtgSchedManager: endActivityTransaction: margin state not match
2025-06-26 16:01:53.584 14428-14428/com.xinghuo.jingxin E/RtgSchedManager: endActivityTransaction: margin state not match
2025-06-26 16:01:53.585 14428-14428/com.xinghuo.jingxin E/CordovaFragment: HomeFragment, onMessage2 : onPageStarted
2025-06-26 16:01:53.585 14428-14428/com.xinghuo.jingxin E/CordovaFragment: onMessage2 : onPageStarted
2025-06-26 16:01:53.585 14428-14428/com.xinghuo.jingxin D/ChatAllHistoryAdapter: EaseConversationAdapter.notifyDataSetChanged()
2025-06-26 16:01:53.585 14428-14428/com.xinghuo.jingxin D/MineFragment: actualPosition=
2025-06-26 16:01:53.586 14428-14428/com.xinghuo.jingxin V/InputMethodManager: Starting input: Bind result=InputBindResult{result=SUCCESS_WITH_IME_SESSION method=com.android.internal.inputmethod.IInputMethodSession$Stub$Proxy@3fe86c2 id=com.baidu.input_hihonor/com.baidu.input_honor.ImeService sequence=419 result=0 isInputMethodSuppressingSpellChecker=false}
2025-06-26 16:01:53.587 14428-14428/com.xinghuo.jingxin E/FXP: loginSSO Result - {"access_token":"f97b011b-0bfb-40b4-a70f-e5b97c35d3e6","token_type":"bearer","refresh_token":"ad279cce-a61d-4e45-903f-6116accb3657","expires_in":694093,"scope":"all"}
2025-06-26 16:01:53.589 14428-14428/com.xinghuo.jingxin E/CordovaFragment: HomeFragment, onMessage2 : onPageStarted
2025-06-26 16:01:53.589 14428-14428/com.xinghuo.jingxin E/CordovaFragment: onMessage2 : onPageStarted
2025-06-26 16:01:53.590 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:01:53:589]: [EMPushHelper] EMPushHelper unregister, unbind token: true
2025-06-26 16:01:53.590 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:01:53:589]: [EMNormalPush] stopChatService
2025-06-26 16:01:53.592 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:01:53:590]: [EMNormalPush] cancelJob
2025-06-26 16:01:53.592 14428-14428/com.xinghuo.jingxin I/SurfaceControl: nativeRelease 0x723c8135f0 count: 1 name: Surface(name=8d3d89d NavigationBar0)/@0x69cabcd - animation-leash of insets_animation#4897
2025-06-26 16:01:53.593 14428-14428/com.xinghuo.jingxin I/SurfaceControl: nativeRelease 0x723c813a70 count: 1 name: Surface(name=392bbe6 StatusBar)/@0xa9bcc82 - animation-leash of insets_animation#4898
2025-06-26 16:01:53.593 14428-14428/com.xinghuo.jingxin E/RtgSchedManager: endActivityTransaction: margin state not match
2025-06-26 16:01:53.596 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:01:53:596]: [EMPushHelper] uploadTokenInternal, token=, url=http://10.248.97.237:12001/easemob-szjw/chat/users/22a319c71bd04d0ab6c5389dbe6f03cd, notifier name=null
2025-06-26 16:01:53.597 14428-14456/com.xinghuo.jingxin D/TrafficStats: tagSocket(205) with statsTag=0xffffffff, statsUid=-1
2025-06-26 16:01:53.657 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:01:53:657]: [EMPushHelper] uploadTokenInternal success.
2025-06-26 16:01:53.657 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:01:53:657]: [EMPushHelper] Push type after unregister is null
2025-06-26 16:01:53.662 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:01:53:662]: begin logout ..
2025-06-26 16:01:53.662 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:01:53:662]: logout complete
2025-06-26 16:01:53.663 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:01:53:663]: [EMClient] emchat manager login in process:14428
2025-06-26 16:01:53.663 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:01:53:663]: [EMClient] emchat manager login in process:14428 threadName:pool-6-thread-3 ID:930
2025-06-26 16:01:53.663 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:01:53:663]: sqlite version: 3.26.0
2025-06-26 16:01:53.663 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:01:53:663]: performMigrationIfNecessary current DB version: 6
2025-06-26 16:01:53.663 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:01:53:663]: EMSessionManager::login(): is autoLogin? 0 type: 0, id: 22a319c71bd04d0ab6c5389dbe6f03cd
2025-06-26 16:01:53.664 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:01:53:663]: httprequest perform: http://10.248.97.237:12001/easemob-szjw/chat/token
2025-06-26 16:01:53.664 14428-16289/com.xinghuo.jingxin D/TrafficStats: tagSocket(205) with statsTag=0xffffffff, statsUid=-1
2025-06-26 16:01:53.687 14428-16464/com.xinghuo.jingxin I/Environment: path /storage/emulated/10 state is mounted
2025-06-26 16:01:53.694 14428-14428/com.xinghuo.jingxin E/CordovaFragment: HomeFragment, onMessage2 : spinner
2025-06-26 16:01:53.694 14428-14428/com.xinghuo.jingxin E/CordovaFragment: onMessage2 : spinner
2025-06-26 16:01:53.744 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:01:53:744]: httprequest perform: http://10.248.97.237:12001/easemob-szjw/chat/security
2025-06-26 16:01:53.744 14428-16289/com.xinghuo.jingxin D/TrafficStats: tagSocket(221) with statsTag=0xffffffff, statsUid=-1
2025-06-26 16:01:53.785 14428-14428/com.xinghuo.jingxin I/HwViewRootImpl: removeInvalidNode jank list is null
2025-06-26 16:01:53.789 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:01:53:788]: fetchEncryptionInfoForUser return code : 401  response : {"error":"unauthorized","exception":"EasemobSecurityException","timestamp":1750924912531,"duration":0,"error_description":"Unable to authenticate (OAuth)"}
     and error desc: 
2025-06-26 16:01:53.789 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:01:53:788]: fetchEncryptionInfo failure : 303
2025-06-26 16:01:53.789 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:01:53:788]: fetch encryption info from server failed status: 303
2025-06-26 16:01:53.789 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:01:53:789]: token status:  0
2025-06-26 16:01:54.553 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:01:54:552]: log: level: 2, area: 1, ChatClient::disconnect()
2025-06-26 16:01:54.553 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:01:54:552]: log: level: 1, area: 2, cleanup() 167
2025-06-26 16:01:54.553 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:01:54:552]: log: level: 1, area: 2, closeSocket() 167
2025-06-26 16:01:54.553 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:01:54:553]: log: level: 2, area: 1, handleDisconnect:14
2025-06-26 16:01:54.553 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:01:54:553]: log: level: 2, area: 1, ChatClient::connect() 
2025-06-26 16:01:54.553 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:01:54:553]: log: level: 1, area: 2, connectSocket(): start to connecting...
2025-06-26 16:01:54.592 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:01:54:592]: log: level: 1, area: 2, connectSocket(): connect finished
2025-06-26 16:01:54.593 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:01:54:592]: log: level: 2, area: 2, connectSocket() OK: fd: 167 Client:10.252.116.114:40466 Server: 10.248.97.237:16717
2025-06-26 16:01:54.633 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:01:54:630]: notify state change to connection listener
2025-06-26 16:01:54.633 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:01:54:630]: [im login time] 0: 0:841
2025-06-26 16:01:54.634 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:01:54:631]: EMConnectionListener onConnected
2025-06-26 16:01:54.634 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:01:54:631]: [chat login time] 0: 0:968
2025-06-26 16:01:54.634 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:01:54:631]: isUseRtcConfig:0
2025-06-26 16:01:54.635 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:01:54:633]: log: level: 2, area: 1, SEND:
    { verison : MSYNC_V1, compress_algorimth : 0, command : UNREAD, encrypt_type : [ 0 ], payload : {  } }
2025-06-26 16:01:54.655 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:01:54:655]: [EMPushHelper] EMPushHelper register, prefer push type: NORMAL
2025-06-26 16:01:54.656 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:01:54:655]: [EMPushHelper] NORMAL push already exists, unregister it and change to NORMAL push.
2025-06-26 16:01:54.656 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:01:54:655]: [EMNormalPush] stopChatService
2025-06-26 16:01:54.657 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:01:54:657]: [EMNormalPush] cancelJob
2025-06-26 16:01:54.658 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:01:54:658]: [EMNormalPush] startChatService
2025-06-26 16:01:54.660 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:01:54:659]: log: level: 1, area: 1, NO unread queue, an response for ping?
2025-06-26 16:01:54.660 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:01:54:660]: native_1sendPing
2025-06-26 16:01:54.663 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:01:54:660]: [EMNormalPush] scheduleJob
2025-06-26 16:01:54.666 14428-16289/com.xinghuo.jingxin W/JobInfo: Requested interval +1m0s0ms for job 11 is too small; raising to +15m0s0ms
2025-06-26 16:01:54.666 14428-16289/com.xinghuo.jingxin W/JobInfo: Requested flex +1m0s0ms for job 11 is too small; raising to +5m0s0ms
2025-06-26 16:01:54.673 14428-16289/com.xinghuo.jingxin D/com.hyphenate.chatkit.utils.LoginimUtils: 连接成功
2025-06-26 16:01:54.673 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:01:54:673]: Java_com_hyphenate_chat_adapter_EMAChatManager_nativeLoadAllConversationsFromDB
2025-06-26 16:01:54.674 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:01:54:674]: msgList.size0
2025-06-26 16:01:54.674 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:01:54:674]: conversationMap.size:0
2025-06-26 16:01:54.698 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:01:54:697]: log: level: 1, area: 1, NO unread queue, an response for ping?
2025-06-26 16:01:55.694 14428-14428/com.xinghuo.jingxin D/Choreographer: still have 5 traversal callbacks
2025-06-26 16:01:56.621 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(601)] "shifushuxing window.location.reload()", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (601)
2025-06-26 16:01:56.650 14428-14428/com.xinghuo.jingxin E/CordovaWebViewImpl: onPageFinished(file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html)
2025-06-26 16:01:56.650 14428-14428/com.xinghuo.jingxin W/System.err: org.json.JSONException: No value for params
2025-06-26 16:01:56.650 14428-14428/com.xinghuo.jingxin W/System.err:     at org.json.JSONObject.get(JSONObject.java:398)
2025-06-26 16:01:56.650 14428-14428/com.xinghuo.jingxin W/System.err:     at org.json.JSONObject.getJSONObject(JSONObject.java:618)
2025-06-26 16:01:56.650 14428-14428/com.xinghuo.jingxin W/System.err:     at org.apache.cordova.xhmpaas.Page.getParamFromPage(Page.java:501)
2025-06-26 16:01:56.650 14428-14428/com.xinghuo.jingxin W/System.err:     at org.apache.cordova.xhmpaas.Page.onMessage(Page.java:465)
2025-06-26 16:01:56.650 14428-14428/com.xinghuo.jingxin W/System.err:     at libcordova.src.org.apache.cordova.PluginManager.postMessage(PluginManager.java:317)
2025-06-26 16:01:56.650 14428-14428/com.xinghuo.jingxin W/System.err:     at libcordova.src.org.apache.cordova.CordovaWebViewImpl$EngineClient.onPageFinishedLoading(CordovaWebViewImpl.java:557)
2025-06-26 16:01:56.650 14428-14428/com.xinghuo.jingxin W/System.err:     at libcordova.src.org.apache.cordova.engine.SystemWebViewClient.onPageFinished(SystemWebViewClient.java:186)
2025-06-26 16:01:56.650 14428-14428/com.xinghuo.jingxin W/System.err:     at android.rms.iaware.contentload.HnWebViewClientWrapper.lambda$onPageFinished$2(HnWebViewClientWrapper.java:62)
2025-06-26 16:01:56.650 14428-14428/com.xinghuo.jingxin W/System.err:     at android.rms.iaware.contentload.HnWebViewClientWrapper$$ExternalSyntheticLambda2.accept(D8$$SyntheticClass:0)
2025-06-26 16:01:56.650 14428-14428/com.xinghuo.jingxin W/System.err:     at java.util.Optional.ifPresentOrElse(Optional.java:196)
2025-06-26 16:01:56.650 14428-14428/com.xinghuo.jingxin W/System.err:     at android.rms.iaware.contentload.HnWebViewClientWrapper.onPageFinished(HnWebViewClientWrapper.java:62)
2025-06-26 16:01:56.650 14428-14428/com.xinghuo.jingxin W/System.err:     at WV.LY.b(chromium-TrichromeWebViewGoogle.aab-stable-672305831:12)
2025-06-26 16:01:56.651 14428-14428/com.xinghuo.jingxin W/System.err:     at WV.h7.handleMessage(chromium-TrichromeWebViewGoogle.aab-stable-672305831:261)
2025-06-26 16:01:56.651 14428-14428/com.xinghuo.jingxin W/System.err:     at android.os.Handler.dispatchMessage(Handler.java:118)
2025-06-26 16:01:56.651 14428-14428/com.xinghuo.jingxin W/System.err:     at android.os.Looper.loopOnce(Looper.java:237)
2025-06-26 16:01:56.651 14428-14428/com.xinghuo.jingxin W/System.err:     at android.os.Looper.loop(Looper.java:325)
2025-06-26 16:01:56.651 14428-14428/com.xinghuo.jingxin W/System.err:     at android.app.ActivityThread.main(ActivityThread.java:10378)
2025-06-26 16:01:56.651 14428-14428/com.xinghuo.jingxin W/System.err:     at java.lang.reflect.Method.invoke(Native Method)
2025-06-26 16:01:56.651 14428-14428/com.xinghuo.jingxin W/System.err:     at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:635)
2025-06-26 16:01:56.651 14428-14428/com.xinghuo.jingxin W/System.err:     at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:961)
2025-06-26 16:01:56.654 14428-14428/com.xinghuo.jingxin E/CordovaFragment: HomeFragment, onMessage2 : onPageFinished
2025-06-26 16:01:56.654 14428-14428/com.xinghuo.jingxin D/CordovaFragment: onPageFinished, id: onPageFinished, data: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html
2025-06-26 16:01:56.654 14428-14428/com.xinghuo.jingxin E/Update UserInfo: HomeFragment.setDiscardMessageFlag(false)
2025-06-26 16:01:56.657 14428-14428/com.xinghuo.jingxin E/CordovaFragment: HomeFragment, onMessage2 : onPageStarted
2025-06-26 16:01:56.657 14428-14428/com.xinghuo.jingxin E/CordovaFragment: onMessage2 : onPageStarted
2025-06-26 16:01:56.715 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(610)] "window.addEventListener resize", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (610)
2025-06-26 16:01:56.716 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(969)] "setProgramConfig configuredProgram [{"id":"7947ffc6-5d27-45fe-a7e2-a667f4d2bde7","programCode":"20190325141821591","programName":"待办事项","iconUrl":"/filestore/2019/4/3/program/2019431039438220.png","createTime":"2019-03-25 14:18:22","createUserCode":"wangcan","programDes":"待办事项","lastUpdateTime":"2019-04-03 10:39:43","lastUpdateUserCode":"zhangxl","orderNo":20190325141821590,"showLimit":1,"apps":null},{"id":"119ac393-c92e-4e8e-811b-891120c147a6","programCode":"20190325141832479","programName":"我的应用","iconUrl":"/filestore/2019/5/29/program/20195291625507850.png","createTime":"2019-03-25 14:18:33","createUserCode":"wangcan","programDes":"我的应用","lastUpdateTime":"2019-05-29 16:22:48","lastUpdateUserCode":"taojunru","orderNo":20190325141832480,"showLimit":3,"apps":null}]", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (969)
2025-06-26 16:01:56.716 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(975)] "setProgramConfig 001", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (975)
2025-06-26 16:01:56.716 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(993)] "setProgramConfig 002", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (993)
2025-06-26 16:01:56.722 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(1010)] "setProgramConfig 003", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (1010)
2025-06-26 16:01:56.722 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(1014)] "setProgramConfig 003 codeNumber20190325141821591", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (1014)
2025-06-26 16:01:56.722 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(1017)] "setProgramConfig 003 loadData codeNumber20190325141821591", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (1017)
2025-06-26 16:01:56.722 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(549)] "window.onload-重新加载待办", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (549)
2025-06-26 16:01:56.723 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(1240)] "---------------------showCacheTodoList
    ", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (1240)
2025-06-26 16:01:56.723 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(1241)] "---------------------isShowItemButNoNum: true
    ", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (1241)
2025-06-26 16:01:56.723 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(1694)] "---------------------gettodoData
    ", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (1694)
2025-06-26 16:01:56.723 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(1268)] "---------------------Cache Todo List is null
    ", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (1268)
2025-06-26 16:01:56.723 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(797)] "---------------------showRefreshIcon
    ", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (797)
2025-06-26 16:01:56.724 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(1014)] "setProgramConfig 003 codeNumber20190325141832479", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (1014)
2025-06-26 16:01:56.724 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(1017)] "setProgramConfig 003 loadData codeNumber20190325141832479", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (1017)
2025-06-26 16:01:56.724 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(1366)] "getAllApp programCode 20190325141832479", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (1366)
2025-06-26 16:01:56.724 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(1390)] "getAllApp 001", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (1390)
2025-06-26 16:01:56.724 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(1397)] "getAllApp param {"APP_URL":"http://*************/supp/httpClient/msa_xhyzd_app/app/list"}", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (1397)
2025-06-26 16:01:56.724 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(51)] "Uncaught ReferenceError: Cordova is not defined", source: file:///android_asset/jiwei_default/js/xh_public.js (51)
2025-06-26 16:01:56.739 14428-14428/com.xinghuo.jingxin E/CordovaFragment: HomeFragment, onMessage2 : spinner
2025-06-26 16:01:56.739 14428-14428/com.xinghuo.jingxin E/CordovaFragment: onMessage2 : spinner
2025-06-26 16:01:56.742 14428-16464/com.xinghuo.jingxin I/Environment: path /storage/emulated/10 state is mounted
2025-06-26 16:01:57.324 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(1240)] "---------------------showCacheTodoList
    ", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (1240)
2025-06-26 16:01:57.325 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(1241)] "---------------------isShowItemButNoNum: true
    ", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (1241)
2025-06-26 16:01:57.325 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(1694)] "---------------------gettodoData
    ", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (1694)
2025-06-26 16:01:57.326 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(1268)] "---------------------Cache Todo List is null
    ", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (1268)
2025-06-26 16:01:57.326 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(1094)] "getToDoListOnHomePageResume：http://*************/supp/httpClient/xinghuo-admin/act/task/toDoTaskCountForPush?userId=22a319c7-1bd0-4d0a-b6c5-389dbe6f03cd&access_token=fe5639cd-5a0a-4962-a86b-fb9e5ed79c68", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (1094)
2025-06-26 16:01:57.327 14428-16440/com.xinghuo.jingxin E/FXP: HttpRequest ajax start old url - http://*************/supp/httpClient/xinghuo-admin/act/task/toDoTaskCountForPush?userId=22a319c7-1bd0-4d0a-b6c5-389dbe6f03cd&access_token=fe5639cd-5a0a-4962-a86b-fb9e5ed79c68
2025-06-26 16:01:57.327 14428-16440/com.xinghuo.jingxin E/FXP: HttpRequest ajax start new url -  https://*************/supp/httpClient/xinghuo-admin/act/task/toDoTaskCountForPush?userId=22a319c7-1bd0-4d0a-b6c5-389dbe6f03cd&access_token=fe5639cd-5a0a-4962-a86b-fb9e5ed79c68
2025-06-26 16:01:57.339 14428-16483/com.xinghuo.jingxin D/TrafficStats: tagSocket(221) with statsTag=0xffffffff, statsUid=-1
2025-06-26 16:01:57.476 14428-16483/com.xinghuo.jingxin E/FXP: HttpRequest ajax onSuccess - {"msg":"操作成功","msgType":"NORMAL","obj":"{\"code\":200,\"msg\":\"查询成功\",\"data\":[{\"appId\":\"a7a12aff202b4d57aca60e397e02e5b4\",\"appCode\":\"XINGHUO_ADMIN\",\"appVersion\":\"V1.44.20230722\",\"moduleName\":\"待办公文\",\"url\":\"modules3/OA_ZHBG/index.html\",\"iconUrl\":\"http://*************:8008/group2/M00/00/11/Cv1wGlz9sy6AGlvpAAANx3GGeiQ513.png\",\"count\":38,\"userId\":\"22a319c7-1bd0-4d0a-b6c5-389dbe6f03cd\"},{\"appId\":\"a7a12aff202b4d57aca60e397e02e5b4\",\"appCode\":\"XINGHUO_test_ADMINa\",\"appVersion\":\"V1.44.20230722\",\"moduleName\":\"待阅公文\",\"url\":\"modules3/OA_ZHBG/index.html?daibanType=UN_READ_COUNT\",\"iconUrl\":\"http://*************:8008/group2/M00/00/11/Cv1wGlz9sy6AGlvpAAANx3GGeiQ513.png\",\"count\":0,\"userId\":\"22a319c7-1bd0-4d0a-b6c5-389dbe6f03cd\"},{\"appId\":\"b7f068eafa4b4946962858aa8b91c46b\",\"appCode\":\"OA_ZHBG_SWL\",\"appVersion\":\"V1.86.20230722\",\"moduleName\":\"工作事务\",\"url\":\"modules3/OA_SWL/index.html\",\"iconUrl\":\"http://*************:8008/group2/M00/00/11/Cv1wHFz9sz2AQnoJAAAGC88wutk230.png\",\"count\":16,\"userId\":\"22a319c7-1bd0-4d0a-b6c5-389dbe6f03cd\"},{\"appId\":\"6fbdb5ecc07e4e4d8dc2399f0ac248d5\",\"appCode\":\"GWCL\",\"appVersion\":\"V1.20.20211127\",\"moduleName\":\"公务车辆\",\"url\":\"modules3/gwcl/index.html\",\"iconUrl\":\"http://*************:8008/group2/M02/00/48/Cv3LIF3boyyAc_PsAAAI8UTAuHA345.png\",\"count\":0,\"userId\":\"22a319c7-1bd0-4d0a-b6c5-389dbe6f03cd\"}],\"success\":true}","success":true}
2025-06-26 16:01:57.492 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(1107)] "---------------------request tododata success
    ", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (1107)
2025-06-26 16:01:57.493 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(1111)] "---------------------result：{"code":200,"msg":"查询成功","data":[{"appId":"a7a12aff202b4d57aca60e397e02e5b4","appCode":"XINGHUO_ADMIN","appVersion":"V1.44.20230722","moduleName":"待办公文","url":"modules3/OA_ZHBG/index.html","iconUrl":"http://*************:8008/group2/M00/00/11/Cv1wGlz9sy6AGlvpAAANx3GGeiQ513.png","count":38,"userId":"22a319c7-1bd0-4d0a-b6c5-389dbe6f03cd"},{"appId":"a7a12aff202b4d57aca60e397e02e5b4","appCode":"XINGHUO_test_ADMINa","appVersion":"V1.44.20230722","moduleName":"待阅公文","url":"modules3/OA_ZHBG/index.html?daibanType=UN_READ_COUNT","iconUrl":"http://*************:8008/group2/M00/00/11/Cv1wGlz9sy6AGlvpAAANx3GGeiQ513.png","count":0,"userId":"22a319c7-1bd0-4d0a-b6c5-389dbe6f03cd"},{"appId":"b7f068eafa4b4946962858aa8b91c46b","appCode":"OA_ZHBG_SWL","appVersion":"V1.86.20230722","moduleName":"工作事务","url":"modules3/OA_SWL/index.html","iconUrl":"http://*************:8008/group2/M00/00/11/Cv1wHFz9sz2AQnoJAAAGC88wutk230.png","count":16,"userId":"22a319c7-1bd0-4d0a-b6c5-389dbe6f03cd"},{"appId":"6fbdb5ecc07e4e4d8dc2399f0ac248d5","appCode":"GWCL","appVersion":"V1.20.20211127","moduleName":"公务车辆","url":"modules3/gwcl/index.html","iconUrl":"http://*************:8008/group2/M02/00/48/Cv3LIF3boyyAc_PsAAAI8UTAuHA345.png","count":0,"userId":"22a319c7-1bd0-4d0a-b6c5-389dbe6f03cd"}],"success":true}", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (1111)
2025-06-26 16:01:57.495 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(1273)] "---------------------showRequestTodoList
    ", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (1273)
2025-06-26 16:01:57.495 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(1274)] "---------------------isShowItemButNoNum: true
    ", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (1274)
2025-06-26 16:01:57.496 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(1208)] "---------------------isTodoNumChanged
    ", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (1208)
2025-06-26 16:01:57.497 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(1694)] "---------------------gettodoData
    ", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (1694)
2025-06-26 16:01:57.497 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(1232)] "---------------------TodoNum is Changed
    ", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (1232)
2025-06-26 16:01:57.497 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(1288)] "---------------------待办公文：38", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (1288)
2025-06-26 16:01:57.498 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(1288)] "---------------------待阅公文：0", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (1288)
2025-06-26 16:01:57.498 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(1288)] "---------------------工作事务：16", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (1288)
2025-06-26 16:01:57.498 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(1288)] "---------------------公务车辆：0", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (1288)
2025-06-26 16:01:57.500 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(1308)] "---------------------setToDoView
    ", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (1308)
2025-06-26 16:01:57.526 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(1346)] "---------------------total：54
    ", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (1346)
2025-06-26 16:01:57.526 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(806)] "---------------------hideRefreshIcon
    ", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (806)
2025-06-26 16:01:57.530 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(1689)] "---------------------settodoData
    ", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (1689)
2025-06-26 16:01:59.692 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(601)] "shifushuxing window.location.reload()", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (601)
2025-06-26 16:01:59.721 14428-14428/com.xinghuo.jingxin E/CordovaWebViewImpl: onPageFinished(file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html)
2025-06-26 16:01:59.722 14428-14428/com.xinghuo.jingxin E/CordovaFragment: HomeFragment, onMessage2 : onPageFinished
2025-06-26 16:01:59.722 14428-14428/com.xinghuo.jingxin D/CordovaFragment: onPageFinished, id: onPageFinished, data: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html
2025-06-26 16:01:59.722 14428-14428/com.xinghuo.jingxin E/Update UserInfo: HomeFragment.setDiscardMessageFlag(false)
2025-06-26 16:01:59.732 14428-14428/com.xinghuo.jingxin E/CordovaFragment: HomeFragment, onMessage2 : onPageStarted
2025-06-26 16:01:59.732 14428-14428/com.xinghuo.jingxin E/CordovaFragment: onMessage2 : onPageStarted
2025-06-26 16:01:59.784 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(610)] "window.addEventListener resize", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (610)
2025-06-26 16:01:59.785 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(969)] "setProgramConfig configuredProgram [{"id":"7947ffc6-5d27-45fe-a7e2-a667f4d2bde7","programCode":"20190325141821591","programName":"待办事项","iconUrl":"/filestore/2019/4/3/program/2019431039438220.png","createTime":"2019-03-25 14:18:22","createUserCode":"wangcan","programDes":"待办事项","lastUpdateTime":"2019-04-03 10:39:43","lastUpdateUserCode":"zhangxl","orderNo":20190325141821590,"showLimit":1,"apps":null},{"id":"119ac393-c92e-4e8e-811b-891120c147a6","programCode":"20190325141832479","programName":"我的应用","iconUrl":"/filestore/2019/5/29/program/20195291625507850.png","createTime":"2019-03-25 14:18:33","createUserCode":"wangcan","programDes":"我的应用","lastUpdateTime":"2019-05-29 16:22:48","lastUpdateUserCode":"taojunru","orderNo":20190325141832480,"showLimit":3,"apps":null}]", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (969)
2025-06-26 16:01:59.785 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(975)] "setProgramConfig 001", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (975)
2025-06-26 16:01:59.785 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(993)] "setProgramConfig 002", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (993)
2025-06-26 16:01:59.787 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(1010)] "setProgramConfig 003", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (1010)
2025-06-26 16:01:59.787 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(1014)] "setProgramConfig 003 codeNumber20190325141821591", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (1014)
2025-06-26 16:01:59.788 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(1017)] "setProgramConfig 003 loadData codeNumber20190325141821591", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (1017)
2025-06-26 16:01:59.788 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(549)] "window.onload-重新加载待办", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (549)
2025-06-26 16:01:59.788 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(1240)] "---------------------showCacheTodoList
    ", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (1240)
2025-06-26 16:01:59.788 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(1241)] "---------------------isShowItemButNoNum: true
    ", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (1241)
2025-06-26 16:01:59.788 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(1694)] "---------------------gettodoData
    ", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (1694)
2025-06-26 16:01:59.788 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(1245)] "---------------------Cache Todo List Not Null
    ", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (1245)
2025-06-26 16:01:59.788 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(1308)] "---------------------setToDoView
    ", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (1308)
2025-06-26 16:01:59.796 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(1346)] "---------------------total：54
    ", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (1346)
2025-06-26 16:01:59.796 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(806)] "---------------------hideRefreshIcon
    ", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (806)
2025-06-26 16:01:59.797 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(797)] "---------------------showRefreshIcon
    ", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (797)
2025-06-26 16:01:59.797 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(1014)] "setProgramConfig 003 codeNumber20190325141832479", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (1014)
2025-06-26 16:01:59.797 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(1017)] "setProgramConfig 003 loadData codeNumber20190325141832479", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (1017)
2025-06-26 16:01:59.797 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(1366)] "getAllApp programCode 20190325141832479", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (1366)
2025-06-26 16:01:59.797 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(1390)] "getAllApp 001", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (1390)
2025-06-26 16:01:59.797 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(1397)] "getAllApp param {"APP_URL":"http://*************/supp/httpClient/msa_xhyzd_app/app/list"}", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (1397)
2025-06-26 16:01:59.798 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(51)] "Uncaught ReferenceError: Cordova is not defined", source: file:///android_asset/jiwei_default/js/xh_public.js (51)
2025-06-26 16:01:59.809 14428-14428/com.xinghuo.jingxin E/CordovaFragment: HomeFragment, onMessage2 : spinner
2025-06-26 16:01:59.809 14428-14428/com.xinghuo.jingxin E/CordovaFragment: onMessage2 : spinner
2025-06-26 16:01:59.812 14428-16464/com.xinghuo.jingxin I/Environment: path /storage/emulated/10 state is mounted
2025-06-26 16:02:00.399 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(1240)] "---------------------showCacheTodoList
    ", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (1240)
2025-06-26 16:02:00.399 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(1241)] "---------------------isShowItemButNoNum: true
    ", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (1241)
2025-06-26 16:02:00.400 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(1694)] "---------------------gettodoData
    ", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (1694)
2025-06-26 16:02:00.400 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(1245)] "---------------------Cache Todo List Not Null
    ", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (1245)
2025-06-26 16:02:00.400 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(1308)] "---------------------setToDoView
    ", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (1308)
2025-06-26 16:02:00.413 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(1346)] "---------------------total：54
    ", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (1346)
2025-06-26 16:02:00.414 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(806)] "---------------------hideRefreshIcon
    ", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (806)
2025-06-26 16:02:00.417 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(1094)] "getToDoListOnHomePageResume：http://*************/supp/httpClient/xinghuo-admin/act/task/toDoTaskCountForPush?userId=22a319c7-1bd0-4d0a-b6c5-389dbe6f03cd&access_token=fe5639cd-5a0a-4962-a86b-fb9e5ed79c68", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (1094)
2025-06-26 16:02:00.417 14428-16440/com.xinghuo.jingxin E/FXP: HttpRequest ajax start old url - http://*************/supp/httpClient/xinghuo-admin/act/task/toDoTaskCountForPush?userId=22a319c7-1bd0-4d0a-b6c5-389dbe6f03cd&access_token=fe5639cd-5a0a-4962-a86b-fb9e5ed79c68
2025-06-26 16:02:00.417 14428-16440/com.xinghuo.jingxin E/FXP: HttpRequest ajax start new url -  https://*************/supp/httpClient/xinghuo-admin/act/task/toDoTaskCountForPush?userId=22a319c7-1bd0-4d0a-b6c5-389dbe6f03cd&access_token=fe5639cd-5a0a-4962-a86b-fb9e5ed79c68
2025-06-26 16:02:00.426 14428-16496/com.xinghuo.jingxin D/TrafficStats: tagSocket(204) with statsTag=0xffffffff, statsUid=-1
2025-06-26 16:02:00.579 14428-16496/com.xinghuo.jingxin E/FXP: HttpRequest ajax onSuccess - {"msg":"操作成功","msgType":"NORMAL","obj":"{\"code\":200,\"msg\":\"查询成功\",\"data\":[{\"appId\":\"a7a12aff202b4d57aca60e397e02e5b4\",\"appCode\":\"XINGHUO_ADMIN\",\"appVersion\":\"V1.44.20230722\",\"moduleName\":\"待办公文\",\"url\":\"modules3/OA_ZHBG/index.html\",\"iconUrl\":\"http://*************:8008/group2/M00/00/11/Cv1wGlz9sy6AGlvpAAANx3GGeiQ513.png\",\"count\":38,\"userId\":\"22a319c7-1bd0-4d0a-b6c5-389dbe6f03cd\"},{\"appId\":\"a7a12aff202b4d57aca60e397e02e5b4\",\"appCode\":\"XINGHUO_test_ADMINa\",\"appVersion\":\"V1.44.20230722\",\"moduleName\":\"待阅公文\",\"url\":\"modules3/OA_ZHBG/index.html?daibanType=UN_READ_COUNT\",\"iconUrl\":\"http://*************:8008/group2/M00/00/11/Cv1wGlz9sy6AGlvpAAANx3GGeiQ513.png\",\"count\":0,\"userId\":\"22a319c7-1bd0-4d0a-b6c5-389dbe6f03cd\"},{\"appId\":\"b7f068eafa4b4946962858aa8b91c46b\",\"appCode\":\"OA_ZHBG_SWL\",\"appVersion\":\"V1.86.20230722\",\"moduleName\":\"工作事务\",\"url\":\"modules3/OA_SWL/index.html\",\"iconUrl\":\"http://*************:8008/group2/M00/00/11/Cv1wHFz9sz2AQnoJAAAGC88wutk230.png\",\"count\":16,\"userId\":\"22a319c7-1bd0-4d0a-b6c5-389dbe6f03cd\"},{\"appId\":\"6fbdb5ecc07e4e4d8dc2399f0ac248d5\",\"appCode\":\"GWCL\",\"appVersion\":\"V1.20.20211127\",\"moduleName\":\"公务车辆\",\"url\":\"modules3/gwcl/index.html\",\"iconUrl\":\"http://*************:8008/group2/M02/00/48/Cv3LIF3boyyAc_PsAAAI8UTAuHA345.png\",\"count\":0,\"userId\":\"22a319c7-1bd0-4d0a-b6c5-389dbe6f03cd\"}],\"success\":true}","success":true}
2025-06-26 16:02:00.587 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(1107)] "---------------------request tododata success
    ", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (1107)
2025-06-26 16:02:00.588 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(1111)] "---------------------result：{"code":200,"msg":"查询成功","data":[{"appId":"a7a12aff202b4d57aca60e397e02e5b4","appCode":"XINGHUO_ADMIN","appVersion":"V1.44.20230722","moduleName":"待办公文","url":"modules3/OA_ZHBG/index.html","iconUrl":"http://*************:8008/group2/M00/00/11/Cv1wGlz9sy6AGlvpAAANx3GGeiQ513.png","count":38,"userId":"22a319c7-1bd0-4d0a-b6c5-389dbe6f03cd"},{"appId":"a7a12aff202b4d57aca60e397e02e5b4","appCode":"XINGHUO_test_ADMINa","appVersion":"V1.44.20230722","moduleName":"待阅公文","url":"modules3/OA_ZHBG/index.html?daibanType=UN_READ_COUNT","iconUrl":"http://*************:8008/group2/M00/00/11/Cv1wGlz9sy6AGlvpAAANx3GGeiQ513.png","count":0,"userId":"22a319c7-1bd0-4d0a-b6c5-389dbe6f03cd"},{"appId":"b7f068eafa4b4946962858aa8b91c46b","appCode":"OA_ZHBG_SWL","appVersion":"V1.86.20230722","moduleName":"工作事务","url":"modules3/OA_SWL/index.html","iconUrl":"http://*************:8008/group2/M00/00/11/Cv1wHFz9sz2AQnoJAAAGC88wutk230.png","count":16,"userId":"22a319c7-1bd0-4d0a-b6c5-389dbe6f03cd"},{"appId":"6fbdb5ecc07e4e4d8dc2399f0ac248d5","appCode":"GWCL","appVersion":"V1.20.20211127","moduleName":"公务车辆","url":"modules3/gwcl/index.html","iconUrl":"http://*************:8008/group2/M02/00/48/Cv3LIF3boyyAc_PsAAAI8UTAuHA345.png","count":0,"userId":"22a319c7-1bd0-4d0a-b6c5-389dbe6f03cd"}],"success":true}", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (1111)
2025-06-26 16:02:00.588 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(1273)] "---------------------showRequestTodoList
    ", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (1273)
2025-06-26 16:02:00.588 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(1274)] "---------------------isShowItemButNoNum: true
    ", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (1274)
2025-06-26 16:02:00.589 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(1208)] "---------------------isTodoNumChanged
    ", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (1208)
2025-06-26 16:02:00.589 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(1694)] "---------------------gettodoData
    ", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (1694)
2025-06-26 16:02:00.589 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(1234)] "---------------------TodoNum Not Changed
    ", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (1234)
2025-06-26 16:02:00.589 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(1277)] "---------------------return，不再重新渲染待办
    ", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (1277)
2025-06-26 16:02:00.589 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(1689)] "---------------------settodoData
    ", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (1689)
2025-06-26 16:02:02.763 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(601)] "shifushuxing window.location.reload()", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (601)
2025-06-26 16:02:02.795 14428-14428/com.xinghuo.jingxin E/CordovaWebViewImpl: onPageFinished(file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html)
2025-06-26 16:02:02.795 14428-14428/com.xinghuo.jingxin E/CordovaFragment: HomeFragment, onMessage2 : onPageFinished
2025-06-26 16:02:02.795 14428-14428/com.xinghuo.jingxin D/CordovaFragment: onPageFinished, id: onPageFinished, data: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html
2025-06-26 16:02:02.795 14428-14428/com.xinghuo.jingxin E/Update UserInfo: HomeFragment.setDiscardMessageFlag(false)
2025-06-26 16:02:02.808 14428-14428/com.xinghuo.jingxin E/CordovaFragment: HomeFragment, onMessage2 : onPageStarted
2025-06-26 16:02:02.808 14428-14428/com.xinghuo.jingxin E/CordovaFragment: onMessage2 : onPageStarted
2025-06-26 16:02:02.860 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(610)] "window.addEventListener resize", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (610)
2025-06-26 16:02:02.860 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(969)] "setProgramConfig configuredProgram [{"id":"7947ffc6-5d27-45fe-a7e2-a667f4d2bde7","programCode":"20190325141821591","programName":"待办事项","iconUrl":"/filestore/2019/4/3/program/2019431039438220.png","createTime":"2019-03-25 14:18:22","createUserCode":"wangcan","programDes":"待办事项","lastUpdateTime":"2019-04-03 10:39:43","lastUpdateUserCode":"zhangxl","orderNo":20190325141821590,"showLimit":1,"apps":null},{"id":"119ac393-c92e-4e8e-811b-891120c147a6","programCode":"20190325141832479","programName":"我的应用","iconUrl":"/filestore/2019/5/29/program/20195291625507850.png","createTime":"2019-03-25 14:18:33","createUserCode":"wangcan","programDes":"我的应用","lastUpdateTime":"2019-05-29 16:22:48","lastUpdateUserCode":"taojunru","orderNo":20190325141832480,"showLimit":3,"apps":null}]", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (969)
2025-06-26 16:02:02.860 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(975)] "setProgramConfig 001", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (975)
2025-06-26 16:02:02.860 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(993)] "setProgramConfig 002", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (993)
2025-06-26 16:02:02.865 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(1010)] "setProgramConfig 003", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (1010)
2025-06-26 16:02:02.865 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(1014)] "setProgramConfig 003 codeNumber20190325141821591", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (1014)
2025-06-26 16:02:02.865 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(1017)] "setProgramConfig 003 loadData codeNumber20190325141821591", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (1017)
2025-06-26 16:02:02.865 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(549)] "window.onload-重新加载待办", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (549)
2025-06-26 16:02:02.866 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(1240)] "---------------------showCacheTodoList
    ", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (1240)
2025-06-26 16:02:02.866 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(1241)] "---------------------isShowItemButNoNum: true
    ", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (1241)
2025-06-26 16:02:02.866 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(1694)] "---------------------gettodoData
    ", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (1694)
2025-06-26 16:02:02.866 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(1245)] "---------------------Cache Todo List Not Null
    ", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (1245)
2025-06-26 16:02:02.866 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(1308)] "---------------------setToDoView
    ", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (1308)
2025-06-26 16:02:02.871 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(1346)] "---------------------total：54
    ", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (1346)
2025-06-26 16:02:02.872 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(806)] "---------------------hideRefreshIcon
    ", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (806)
2025-06-26 16:02:02.873 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(797)] "---------------------showRefreshIcon
    ", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (797)
2025-06-26 16:02:02.874 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(1014)] "setProgramConfig 003 codeNumber20190325141832479", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (1014)
2025-06-26 16:02:02.874 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(1017)] "setProgramConfig 003 loadData codeNumber20190325141832479", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (1017)
2025-06-26 16:02:02.874 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(1366)] "getAllApp programCode 20190325141832479", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (1366)
2025-06-26 16:02:02.874 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(1390)] "getAllApp 001", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (1390)
2025-06-26 16:02:02.875 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(1397)] "getAllApp param {"APP_URL":"http://*************/supp/httpClient/msa_xhyzd_app/app/list"}", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (1397)
2025-06-26 16:02:02.875 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(51)] "Uncaught ReferenceError: Cordova is not defined", source: file:///android_asset/jiwei_default/js/xh_public.js (51)
2025-06-26 16:02:02.887 14428-14428/com.xinghuo.jingxin E/CordovaFragment: HomeFragment, onMessage2 : spinner
2025-06-26 16:02:02.888 14428-14428/com.xinghuo.jingxin E/CordovaFragment: onMessage2 : spinner
2025-06-26 16:02:02.892 14428-16464/com.xinghuo.jingxin I/Environment: path /storage/emulated/10 state is mounted
2025-06-26 16:02:03.475 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(1240)] "---------------------showCacheTodoList
    ", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (1240)
2025-06-26 16:02:03.475 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(1241)] "---------------------isShowItemButNoNum: true
    ", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (1241)
2025-06-26 16:02:03.475 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(1694)] "---------------------gettodoData
    ", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (1694)
2025-06-26 16:02:03.476 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(1245)] "---------------------Cache Todo List Not Null
    ", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (1245)
2025-06-26 16:02:03.476 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(1308)] "---------------------setToDoView
    ", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (1308)
2025-06-26 16:02:03.492 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(1346)] "---------------------total：54
    ", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (1346)
2025-06-26 16:02:03.492 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(806)] "---------------------hideRefreshIcon
    ", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (806)
2025-06-26 16:02:03.495 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(1094)] "getToDoListOnHomePageResume：http://*************/supp/httpClient/xinghuo-admin/act/task/toDoTaskCountForPush?userId=22a319c7-1bd0-4d0a-b6c5-389dbe6f03cd&access_token=fe5639cd-5a0a-4962-a86b-fb9e5ed79c68", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (1094)
2025-06-26 16:02:03.495 14428-16440/com.xinghuo.jingxin E/FXP: HttpRequest ajax start old url - http://*************/supp/httpClient/xinghuo-admin/act/task/toDoTaskCountForPush?userId=22a319c7-1bd0-4d0a-b6c5-389dbe6f03cd&access_token=fe5639cd-5a0a-4962-a86b-fb9e5ed79c68
2025-06-26 16:02:03.495 14428-16440/com.xinghuo.jingxin E/FXP: HttpRequest ajax start new url -  https://*************/supp/httpClient/xinghuo-admin/act/task/toDoTaskCountForPush?userId=22a319c7-1bd0-4d0a-b6c5-389dbe6f03cd&access_token=fe5639cd-5a0a-4962-a86b-fb9e5ed79c68
2025-06-26 16:02:03.504 14428-16516/com.xinghuo.jingxin D/TrafficStats: tagSocket(206) with statsTag=0xffffffff, statsUid=-1
2025-06-26 16:02:05.629 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(622)] "initialize", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (622)
2025-06-26 16:02:05.630 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(629)] "bindEvents", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (629)
2025-06-26 16:02:05.630 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(644)] "onDeviceReady appobj.receivedEvent", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (644)
2025-06-26 16:02:05.630 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(651)] "receivedEvent", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (651)
2025-06-26 16:02:05.630 14428-16440/com.xinghuo.jingxin E/FXP: CoreAndroid overrideBackbuttontrue
2025-06-26 16:02:05.631 14428-16440/com.xinghuo.jingxin E/FXP: LinkPlugin getLoginInfo
2025-06-26 16:02:05.634 14428-16440/com.xinghuo.jingxin E/FXP: LinkPlugin getLoginInfo result - {"userId":"22a319c7-1bd0-4d0a-b6c5-389dbe6f03cd","orgId":"e58c77f1-47d3-41dd-98c7-15e3e791a198","orgCode":"1021","orgName":"支持单位","loginId":"fxp","userName":"方晓鹏","userType":"5","picture_local":""}
2025-06-26 16:02:05.635 14428-16440/com.xinghuo.jingxin E/FXP: LinkPlugin getLoginInfo
2025-06-26 16:02:05.636 14428-16440/com.xinghuo.jingxin E/FXP: LinkPlugin getLoginInfo result - {"userId":"22a319c7-1bd0-4d0a-b6c5-389dbe6f03cd","orgId":"e58c77f1-47d3-41dd-98c7-15e3e791a198","orgCode":"1021","orgName":"支持单位","loginId":"fxp","userName":"方晓鹏","userType":"5","picture_local":""}
2025-06-26 16:02:05.640 14428-16440/com.xinghuo.jingxin E/ExtendApp: action: getJiJianYunVersion, args = []
2025-06-26 16:02:05.644 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(673)] "getLoginInfo1 res :{"userId":"22a319c7-1bd0-4d0a-b6c5-389dbe6f03cd","orgId":"e58c77f1-47d3-41dd-98c7-15e3e791a198","orgCode":"1021","orgName":"支持单位","loginId":"fxp","userName":"方晓鹏","userType":"5","picture_local":""}", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (673)
2025-06-26 16:02:05.644 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(1639)] "getAnnouncementToken-tokenUrl：http://*************/supp/httpClient/xinghuo-admin/sys/token", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (1639)
2025-06-26 16:02:05.644 14428-16440/com.xinghuo.jingxin E/FXP: LinkPlugin getLoginInfo
2025-06-26 16:02:05.645 14428-16440/com.xinghuo.jingxin E/FXP: LinkPlugin getLoginInfo result - {"userId":"22a319c7-1bd0-4d0a-b6c5-389dbe6f03cd","orgId":"e58c77f1-47d3-41dd-98c7-15e3e791a198","orgCode":"1021","orgName":"支持单位","loginId":"fxp","userName":"方晓鹏","userType":"5","picture_local":""}
2025-06-26 16:02:05.646 14428-16440/com.xinghuo.jingxin E/FXP: LinkPlugin getUserInfo
2025-06-26 16:02:05.646 14428-16440/com.xinghuo.jingxin I/System.out: -----------start
2025-06-26 16:02:05.649 14428-16440/com.xinghuo.jingxin E/FXP: LinkPlugin getUserInfo result - {"actualDepartmentId":"e58c77f1-47d3-41dd-98c7-15e3e791a198","actualPosition":"","available":1,"childDeptIds":"","deptCode":"1021","deptName":"支持单位","hxId":"22a319c71bd04d0ab6c5389dbe6f03cd","id":427,"isDeleted":"1","isPublic":1,"isReceiveMsg":1,"isSendMsgUnlimited":"0","lastUpdateTime":"1746601391480","listOrder":"10","mobilePhone":"18589082142","mobilePhone2":"18589082142","platformType":0,"userId":"22a319c7-1bd0-4d0a-b6c5-389dbe6f03cd","userLoginId":"fxp","userName":"方晓鹏","userPhotoPath":"","userType":"5"}
2025-06-26 16:02:05.652 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(969)] "setProgramConfig configuredProgram [{"id":"7947ffc6-5d27-45fe-a7e2-a667f4d2bde7","programCode":"20190325141821591","programName":"待办事项","iconUrl":"/filestore/2019/4/3/program/2019431039438220.png","createTime":"2019-03-25 14:18:22","createUserCode":"wangcan","programDes":"待办事项","lastUpdateTime":"2019-04-03 10:39:43","lastUpdateUserCode":"zhangxl","orderNo":20190325141821590,"showLimit":1,"apps":null},{"id":"119ac393-c92e-4e8e-811b-891120c147a6","programCode":"20190325141832479","programName":"我的应用","iconUrl":"/filestore/2019/5/29/program/20195291625507850.png","createTime":"2019-03-25 14:18:33","createUserCode":"wangcan","programDes":"我的应用","lastUpdateTime":"2019-05-29 16:22:48","lastUpdateUserCode":"taojunru","orderNo":20190325141832480,"showLimit":3,"apps":null}]", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (969)
2025-06-26 16:02:05.652 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(975)] "setProgramConfig 001", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (975)
2025-06-26 16:02:05.652 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(993)] "setProgramConfig 002", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (993)
2025-06-26 16:02:05.655 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(1010)] "setProgramConfig 003", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (1010)
2025-06-26 16:02:05.655 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(1014)] "setProgramConfig 003 codeNumber20190325141821591", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (1014)
2025-06-26 16:02:05.656 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(1017)] "setProgramConfig 003 loadData codeNumber20190325141821591", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (1017)
2025-06-26 16:02:05.656 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(549)] "window.onload-重新加载待办", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (549)
2025-06-26 16:02:05.656 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(1240)] "---------------------showCacheTodoList
    ", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (1240)
2025-06-26 16:02:05.656 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(1241)] "---------------------isShowItemButNoNum: true
    ", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (1241)
2025-06-26 16:02:05.656 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(1694)] "---------------------gettodoData
    ", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (1694)
2025-06-26 16:02:05.656 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(1245)] "---------------------Cache Todo List Not Null
    ", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (1245)
2025-06-26 16:02:05.656 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(1308)] "---------------------setToDoView
    ", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (1308)
2025-06-26 16:02:05.661 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(1346)] "---------------------total：54
    ", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (1346)
2025-06-26 16:02:05.661 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(806)] "---------------------hideRefreshIcon
    ", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (806)
2025-06-26 16:02:05.663 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(797)] "---------------------showRefreshIcon
    ", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (797)
2025-06-26 16:02:05.664 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(1014)] "setProgramConfig 003 codeNumber20190325141832479", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (1014)
2025-06-26 16:02:05.664 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(1017)] "setProgramConfig 003 loadData codeNumber20190325141832479", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (1017)
2025-06-26 16:02:05.664 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(1366)] "getAllApp programCode 20190325141832479", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (1366)
2025-06-26 16:02:05.664 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(1390)] "getAllApp 001", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (1390)
2025-06-26 16:02:05.664 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(1397)] "getAllApp param {"APP_URL":"http://*************/supp/httpClient/msa_xhyzd_app/app/list","userId":"22a319c7-1bd0-4d0a-b6c5-389dbe6f03cd"}", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (1397)
2025-06-26 16:02:05.665 14428-16440/com.xinghuo.jingxin E/FXP: LinkPlugin getLoginInfo
2025-06-26 16:02:05.666 14428-16440/com.xinghuo.jingxin E/FXP: LinkPlugin getLoginInfo result - {"userId":"22a319c7-1bd0-4d0a-b6c5-389dbe6f03cd","orgId":"e58c77f1-47d3-41dd-98c7-15e3e791a198","orgCode":"1021","orgName":"支持单位","loginId":"fxp","userName":"方晓鹏","userType":"5","picture_local":""}
2025-06-26 16:02:05.667 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(1023)] "setProgramConfig 004", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (1023)
2025-06-26 16:02:05.667 14428-16440/com.xinghuo.jingxin E/FXP: LinkPlugin getLoginInfo
2025-06-26 16:02:05.667 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(1497)] "getUserTestApps - applist_url：http://*************/supp/httpClient/msa_xhyzd_app/app/", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (1497)
2025-06-26 16:02:05.668 14428-16440/com.xinghuo.jingxin E/FXP: LinkPlugin getLoginInfo result - {"userId":"22a319c7-1bd0-4d0a-b6c5-389dbe6f03cd","orgId":"e58c77f1-47d3-41dd-98c7-15e3e791a198","orgCode":"1021","orgName":"支持单位","loginId":"fxp","userName":"方晓鹏","userType":"5","picture_local":""}
2025-06-26 16:02:05.673 14428-16440/com.xinghuo.jingxin E/ExtendApp: action: getLightAppVersionByCode, args = ["com.page.index"]
2025-06-26 16:02:05.675 14428-16440/com.xinghuo.jingxin E/ExtendApp: action: getDeviceId, args = []
2025-06-26 16:02:05.677 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(691)] "getUserInfo1 res :{"actualDepartmentId":"e58c77f1-47d3-41dd-98c7-15e3e791a198","actualPosition":"","available":1,"childDeptIds":"","deptCode":"1021","deptName":"支持单位","hxId":"22a319c71bd04d0ab6c5389dbe6f03cd","id":427,"isDeleted":"1","isPublic":1,"isReceiveMsg":1,"isSendMsgUnlimited":"0","lastUpdateTime":"1746601391480","listOrder":"10","mobilePhone":"18589082142","mobilePhone2":"18589082142","platformType":0,"userId":"22a319c7-1bd0-4d0a-b6c5-389dbe6f03cd","userLoginId":"fxp","userName":"方晓鹏","userPhotoPath":"","userType":"5"}", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (691)
2025-06-26 16:02:05.677 14428-16440/com.xinghuo.jingxin E/ExtendApp: action: getDeviceId, args = []
2025-06-26 16:02:05.678 14428-16440/com.xinghuo.jingxin E/ExtendApp: action: getDeviceId, args = []
2025-06-26 16:02:05.679 14428-16440/com.xinghuo.jingxin E/ExtendApp: action: getMeid, args = []
2025-06-26 16:02:05.682 14428-16440/com.xinghuo.jingxin E/ExtendApp: action: getMeid, args = []
2025-06-26 16:02:05.684 14428-16440/com.xinghuo.jingxin E/ExtendApp: action: getMeid, args = []
2025-06-26 16:02:05.686 14428-16440/com.xinghuo.jingxin E/ExtendApp: action: getImsi, args = []
2025-06-26 16:02:05.694 14428-16440/com.xinghuo.jingxin E/ExtendApp: action: getImsi, args = []
2025-06-26 16:02:05.696 14428-16440/com.xinghuo.jingxin E/ExtendApp: action: getImsi, args = []
2025-06-26 16:02:05.698 14428-16440/com.xinghuo.jingxin E/FXP: HttpRequest ajax start old url - http://*************/supp/httpClient/xinghuo-admin/sys/token
2025-06-26 16:02:05.699 14428-16440/com.xinghuo.jingxin E/FXP: HttpRequest ajax start new url -  https://*************/supp/httpClient/xinghuo-admin/sys/token
2025-06-26 16:02:05.704 14428-16440/com.xinghuo.jingxin E/FXP: HttpRequest ajax start old url - http://*************/supp/httpClient/msa_xhyzd_app/app/list
2025-06-26 16:02:05.704 14428-16440/com.xinghuo.jingxin E/FXP: HttpRequest ajax start new url -  https://*************/supp/httpClient/msa_xhyzd_app/app/list
2025-06-26 16:02:05.704 14428-16520/com.xinghuo.jingxin D/TrafficStats: tagSocket(224) with statsTag=0xffffffff, statsUid=-1
2025-06-26 16:02:05.710 14428-16440/com.xinghuo.jingxin E/FXP: HttpRequest ajax start old url - http://*************/supp/httpClient/msa_xhyzd_app/app/tsetList
2025-06-26 16:02:05.710 14428-16440/com.xinghuo.jingxin E/FXP: HttpRequest ajax start new url -  https://*************/supp/httpClient/msa_xhyzd_app/app/tsetList
2025-06-26 16:02:05.710 14428-16521/com.xinghuo.jingxin D/TrafficStats: tagSocket(333) with statsTag=0xffffffff, statsUid=-1
2025-06-26 16:02:05.715 14428-16522/com.xinghuo.jingxin D/TrafficStats: tagSocket(225) with statsTag=0xffffffff, statsUid=-1
2025-06-26 16:02:05.720 14428-14428/com.xinghuo.jingxin E/CordovaWebViewImpl: onPageFinished(file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html)
2025-06-26 16:02:05.721 14428-14428/com.xinghuo.jingxin E/CordovaFragment: HomeFragment, onMessage2 : onPageFinished
2025-06-26 16:02:05.721 14428-14428/com.xinghuo.jingxin D/CordovaFragment: onPageFinished, id: onPageFinished, data: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html
2025-06-26 16:02:05.721 14428-14428/com.xinghuo.jingxin E/Update UserInfo: HomeFragment.setDiscardMessageFlag(false)
2025-06-26 16:02:05.807 14428-14428/com.xinghuo.jingxin I/HwViewRootImpl: removeInvalidNode all the node in jank list is out of time
2025-06-26 16:02:06.265 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(1240)] "---------------------showCacheTodoList
    ", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (1240)
2025-06-26 16:02:06.265 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(1241)] "---------------------isShowItemButNoNum: true
    ", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (1241)
2025-06-26 16:02:06.266 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(1694)] "---------------------gettodoData
    ", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (1694)
2025-06-26 16:02:06.266 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(1245)] "---------------------Cache Todo List Not Null
    ", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (1245)
2025-06-26 16:02:06.266 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(1308)] "---------------------setToDoView
    ", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (1308)
2025-06-26 16:02:06.277 14428-16516/com.xinghuo.jingxin E/FXP: HttpRequest ajax onSuccess - {"msg":"操作成功","msgType":"NORMAL","obj":"{\"code\":200,\"msg\":\"查询成功\",\"data\":[{\"appId\":\"a7a12aff202b4d57aca60e397e02e5b4\",\"appCode\":\"XINGHUO_ADMIN\",\"appVersion\":\"V1.44.20230722\",\"moduleName\":\"待办公文\",\"url\":\"modules3/OA_ZHBG/index.html\",\"iconUrl\":\"http://*************:8008/group2/M00/00/11/Cv1wGlz9sy6AGlvpAAANx3GGeiQ513.png\",\"count\":38,\"userId\":\"22a319c7-1bd0-4d0a-b6c5-389dbe6f03cd\"},{\"appId\":\"a7a12aff202b4d57aca60e397e02e5b4\",\"appCode\":\"XINGHUO_test_ADMINa\",\"appVersion\":\"V1.44.20230722\",\"moduleName\":\"待阅公文\",\"url\":\"modules3/OA_ZHBG/index.html?daibanType=UN_READ_COUNT\",\"iconUrl\":\"http://*************:8008/group2/M00/00/11/Cv1wGlz9sy6AGlvpAAANx3GGeiQ513.png\",\"count\":0,\"userId\":\"22a319c7-1bd0-4d0a-b6c5-389dbe6f03cd\"},{\"appId\":\"b7f068eafa4b4946962858aa8b91c46b\",\"appCode\":\"OA_ZHBG_SWL\",\"appVersion\":\"V1.86.20230722\",\"moduleName\":\"工作事务\",\"url\":\"modules3/OA_SWL/index.html\",\"iconUrl\":\"http://*************:8008/group2/M00/00/11/Cv1wHFz9sz2AQnoJAAAGC88wutk230.png\",\"count\":16,\"userId\":\"22a319c7-1bd0-4d0a-b6c5-389dbe6f03cd\"},{\"appId\":\"6fbdb5ecc07e4e4d8dc2399f0ac248d5\",\"appCode\":\"GWCL\",\"appVersion\":\"V1.20.20211127\",\"moduleName\":\"公务车辆\",\"url\":\"modules3/gwcl/index.html\",\"iconUrl\":\"http://*************:8008/group2/M02/00/48/Cv3LIF3boyyAc_PsAAAI8UTAuHA345.png\",\"count\":0,\"userId\":\"22a319c7-1bd0-4d0a-b6c5-389dbe6f03cd\"}],\"success\":true}","success":true}
2025-06-26 16:02:06.281 14428-16440/com.xinghuo.jingxin E/FXP: HttpRequest ajax start old url - http://*************/supp/httpClient/xinghuo-admin/act/task/toDoTaskCountForPush?userId=22a319c7-1bd0-4d0a-b6c5-389dbe6f03cd&access_token=fe5639cd-5a0a-4962-a86b-fb9e5ed79c68
2025-06-26 16:02:06.282 14428-16440/com.xinghuo.jingxin E/FXP: HttpRequest ajax start new url -  https://*************/supp/httpClient/xinghuo-admin/act/task/toDoTaskCountForPush?userId=22a319c7-1bd0-4d0a-b6c5-389dbe6f03cd&access_token=fe5639cd-5a0a-4962-a86b-fb9e5ed79c68
2025-06-26 16:02:06.282 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(1346)] "---------------------total：54
    ", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (1346)
2025-06-26 16:02:06.283 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(806)] "---------------------hideRefreshIcon
    ", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (806)
2025-06-26 16:02:06.283 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(1094)] "getToDoListOnHomePageResume：http://*************/supp/httpClient/xinghuo-admin/act/task/toDoTaskCountForPush?userId=22a319c7-1bd0-4d0a-b6c5-389dbe6f03cd&access_token=fe5639cd-5a0a-4962-a86b-fb9e5ed79c68", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (1094)
2025-06-26 16:02:06.288 14428-16528/com.xinghuo.jingxin D/TrafficStats: tagSocket(240) with statsTag=0xffffffff, statsUid=-1
2025-06-26 16:02:06.295 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(1107)] "---------------------request tododata success
    ", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (1107)
2025-06-26 16:02:06.295 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(1111)] "---------------------result：{"code":200,"msg":"查询成功","data":[{"appId":"a7a12aff202b4d57aca60e397e02e5b4","appCode":"XINGHUO_ADMIN","appVersion":"V1.44.20230722","moduleName":"待办公文","url":"modules3/OA_ZHBG/index.html","iconUrl":"http://*************:8008/group2/M00/00/11/Cv1wGlz9sy6AGlvpAAANx3GGeiQ513.png","count":38,"userId":"22a319c7-1bd0-4d0a-b6c5-389dbe6f03cd"},{"appId":"a7a12aff202b4d57aca60e397e02e5b4","appCode":"XINGHUO_test_ADMINa","appVersion":"V1.44.20230722","moduleName":"待阅公文","url":"modules3/OA_ZHBG/index.html?daibanType=UN_READ_COUNT","iconUrl":"http://*************:8008/group2/M00/00/11/Cv1wGlz9sy6AGlvpAAANx3GGeiQ513.png","count":0,"userId":"22a319c7-1bd0-4d0a-b6c5-389dbe6f03cd"},{"appId":"b7f068eafa4b4946962858aa8b91c46b","appCode":"OA_ZHBG_SWL","appVersion":"V1.86.20230722","moduleName":"工作事务","url":"modules3/OA_SWL/index.html","iconUrl":"http://*************:8008/group2/M00/00/11/Cv1wHFz9sz2AQnoJAAAGC88wutk230.png","count":16,"userId":"22a319c7-1bd0-4d0a-b6c5-389dbe6f03cd"},{"appId":"6fbdb5ecc07e4e4d8dc2399f0ac248d5","appCode":"GWCL","appVersion":"V1.20.20211127","moduleName":"公务车辆","url":"modules3/gwcl/index.html","iconUrl":"http://*************:8008/group2/M02/00/48/Cv3LIF3boyyAc_PsAAAI8UTAuHA345.png","count":0,"userId":"22a319c7-1bd0-4d0a-b6c5-389dbe6f03cd"}],"success":true}", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (1111)
2025-06-26 16:02:06.295 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(1273)] "---------------------showRequestTodoList
    ", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (1273)
2025-06-26 16:02:06.296 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(1274)] "---------------------isShowItemButNoNum: true
    ", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (1274)
2025-06-26 16:02:06.296 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(1208)] "---------------------isTodoNumChanged
    ", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (1208)
2025-06-26 16:02:06.296 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(1694)] "---------------------gettodoData
    ", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (1694)
2025-06-26 16:02:06.296 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(1234)] "---------------------TodoNum Not Changed
    ", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (1234)
2025-06-26 16:02:06.296 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(1277)] "---------------------return，不再重新渲染待办
    ", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (1277)
2025-06-26 16:02:06.296 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(1689)] "---------------------settodoData
    ", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (1689)
2025-06-26 16:02:06.318 14428-16520/com.xinghuo.jingxin E/FXP: HttpRequest ajax onSuccess - {"msg":"操作成功","msgType":"NORMAL","obj":"{\"msg\":\"success\",\"access_token\":\"fe5639cd-5a0a-4962-a86b-fb9e5ed79c68\",\"code\":0,\"success\":true,\"expire\":43199}","success":true}
2025-06-26 16:02:06.322 14428-16440/com.xinghuo.jingxin E/FXP: HttpRequest ajax start old url - http://*************/supp/httpClient/xinghuo-admin/oa/info/notifyList
2025-06-26 16:02:06.323 14428-16440/com.xinghuo.jingxin E/FXP: HttpRequest ajax start new url -  https://*************/supp/httpClient/xinghuo-admin/oa/info/notifyList
2025-06-26 16:02:06.327 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(1643)] "getAnnouncementToken success result  {"returnValue":"{\"msg\":\"success\",\"access_token\":\"fe5639cd-5a0a-4962-a86b-fb9e5ed79c68\",\"code\":0,\"success\":true,\"expire\":43199}"}", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (1643)
2025-06-26 16:02:06.328 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(1713)] "getAnnouncementData requestUrl http://*************/supp/httpClient/xinghuo-admin/oa/info/notifyList", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (1713)
2025-06-26 16:02:06.330 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(1775)] "getWorkMomentData requestUrl http://*************/supp/httpClient/xinghuo-admin//oa/info/notifyList", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (1775)
2025-06-26 16:02:06.330 14428-16440/com.xinghuo.jingxin E/FXP: HttpRequest ajax start old url - http://*************/supp/httpClient/xinghuo-admin//oa/info/notifyList
2025-06-26 16:02:06.330 14428-16531/com.xinghuo.jingxin D/TrafficStats: tagSocket(231) with statsTag=0xffffffff, statsUid=-1
2025-06-26 16:02:06.330 14428-16440/com.xinghuo.jingxin E/FXP: HttpRequest ajax start new url -  https://*************/supp/httpClient/xinghuo-admin//oa/info/notifyList
2025-06-26 16:02:06.341 14428-16532/com.xinghuo.jingxin D/TrafficStats: tagSocket(324) with statsTag=0xffffffff, statsUid=-1
2025-06-26 16:02:06.345 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(1658)] "getAnnouncementToken-success：{"msg":"success","access_token":"fe5639cd-5a0a-4962-a86b-fb9e5ed79c68","code":0,"success":true,"expire":43199}", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (1658)
2025-06-26 16:02:06.453 14428-16521/com.xinghuo.jingxin E/FXP: HttpRequest ajax onSuccess - {"msg":"操作成功","msgType":"NORMAL","obj":"{\"msg\":\"success\",\"code\":0,\"data\":[{\"appList\":[{\"uuid\":\"a7a12aff202b4d57aca60e397e02e5b4\",\"appId\":\"262727250493837312\",\"appCode\":\"XINGHUO_ADMIN\",\"appName\":\"公文业务\",\"appTerminalType\":\"3\",\"appIntro\":\"公文业务\",\"appUrl\":\"\",\"describe\":\"\",\"appIcon\":\"http://*************:8008/group2/M00/00/11/Cv1wGlz9sy6AGlvpAAANx3GGeiQ513.png\",\"jmtAppIcon\":\"http://*************:8008/group2/M00/00/11/Cv1wGlz9sy6AGlvpAAANx3GGeiQ513.png\",\"respName\":\"星火电子\",\"respCode\":\"星火电子\",\"respTelPhone\":null,\"enclosure\":\"http://*************:8008/group1/M00/00/BE/CuC2iWffsbyAM9yiACnFt7cahpc489.zip\",\"jmtEnclosure\":\"http://*************:8008/group1/M00/00/BE/CuC2iWffsbyAM9yiACnFt7cahpc489.zip\",\"appEntry\":\"modules3/OA_ZHBG/index.html\",\"appState\":\"6\",\"exampleContent\":\"\",\"listOrder\":\"\",\"constructionUnit\":\"星火电子\",\"lastUpdateTime\":1742713180000,\"createTime\":1562918400000,\"testUser\":null,\"createSupplier\":\"深圳市星火电子工程公司深云团队\",\"version\":\"V2.18.20250323\",\"md5\":\"d022b0fcb8c1b8e193668c33b06cdf08\",\"openIE\":\"\",\"isIframe\":\"\",\"appUuid\":\"\",\"gradeLevel\":0.0,\"appClassify\":\"1\",\"appClassifyCode\":\"SYYY\",\"appClassifyName\":\"\",\"versionId\":null,\"isCollect\":null,\"isBuiltIn\":\"0\",\"maintainName\":null,\"maintainTel\":null,\"backColor\":null,\"fontColor\":null,\"isRecommend\":0,\"appRange\":null,\"clickCnt\":0},{\"uuid\":\"b7f068eafa4b4946962858aa8b91c46b\",\"appId\":\"313681495313092608\",\"appCode\":\"OA_ZHBG_SWL\",\"appName\":\"工作事务\",\"appTerminalType\":\"3\",\"appIntro\":\"工作事务\",\"appUrl\":\"\",\"describe\":\"\",\"appIcon\":\"http://*************:8008/group2/M00/00/11/Cv1wHFz9sz2AQnoJAAAGC88wutk230.png\",\"jmtAppIcon\":\"http://*************:8008/group2/M00/00/11/Cv1wHFz9sz2AQnoJAAAGC88wutk230.png\",\"respName\":\"彭方启\",\"respCode\":\"pengfangqi\",\"respTelPhone\":null,\"enclosure\":\"http://*************:8008/group2/M00/01/70/CuC2jGhL-iGAbeQNADWLdzD0FdM087.zip\",\"jmtEnclosure\":\"http://*************:8008/group2/M00/01/70/CuC2jGhL-iGAbeQNADWLdzD0FdM087.zip\",\"appEntry\":\"modules3/OA_SWL/index.html\",\"appState\":\"6\",\"exampleContent\":\"\",\"listOrder\":\"\",\"constructionUnit\":\"星火电子工程公司\",\"lastUpdateTime\":1749809988000,\"createTime\":1562918400000,\"testUser\":null,\"createSupplier\":\"深圳市星火电子工程公司深云团队\",\"version\":\"V3.20.20250613\",\"md5\":\"1091bb0374090760859026eda75af6bc\",\"openIE\":\"\",\"isIframe\":\"\",\"appUuid\":\"\",\"gradeLevel\":0.0,\"appClassify\":\"1\",\"appClassifyCode\":\"SYYY\",\"appClassifyName\":\"\",\"versionId\":null,\"isCollect\":null,\"isBuiltIn\":\"0\",\"maintainName\":null,\"maintainTel\":null,\"backColor\":null,\"fontColor\":null,\"isRecommend\":0,\"appRange\":null,\"clickCnt\":0},{\"uuid\":\"53efeb0ed9b44c0c8aeb2f5cd2898426\",\"appId\":\"326868391677661184\",\"appCode\":\"LDGZAP\",\"appName\":\"工作助手\",\"appTerminalType\":\"3\",\"appIntro\":\"工作助手\",\"appUrl\":\"\",\"describe\":\"\",\"appIcon\":\"http://*************:8008/group2/M01/00/11/Cv1wGlz9s1CAKbg0AAAFiVquXCw922.png\",\"jmtAppIcon\":\"http://*************:8008/group2/M01/00/11/Cv1wGlz9s1CAKbg0AAAFiVquXCw922.png\",\"respName\":\"星火电子\",\"respCode\":\"***********\",\"respTelPhone\":null,\"enclosure\":\"http://*************:8008/group2/M00/01/72/CuC2jGhc76OAbNprACaQn0yqW0M642.zip\",\"jmtEnclosure\":\"http://*************:8008/group2/M00/01/72/CuC2jGhc76OAbNprACaQn0yqW0M642.zip\",\"appEntry\":\"modules3/lingdaogongzuo/index.html\",\"appState\":\"6\",\"exampleContent\":\"\",\"listOrder\":\"\",\"constructionUnit\":\"星火电子\",\"lastUpdateTime\":1750921405000,\"createTime\":1562918400000,\"testUser\":null,\"createSupplier\":\"深圳市星火电子工程公司深云团队\",\"version\":\"V1.40.20250626\",\"md5\":\"08f4ed20b816c43777d8a36bf8db4670\",\"openIE\":\"\",\"isIframe\":\"\",\"appUuid\":\"\",
2025-06-26 16:02:06.456 14428-16528/com.xinghuo.jingxin E/FXP: HttpRequest ajax onSuccess - {"msg":"操作成功","msgType":"NORMAL","obj":"{\"code\":200,\"msg\":\"查询成功\",\"data\":[{\"appId\":\"a7a12aff202b4d57aca60e397e02e5b4\",\"appCode\":\"XINGHUO_ADMIN\",\"appVersion\":\"V1.44.20230722\",\"moduleName\":\"待办公文\",\"url\":\"modules3/OA_ZHBG/index.html\",\"iconUrl\":\"http://*************:8008/group2/M00/00/11/Cv1wGlz9sy6AGlvpAAANx3GGeiQ513.png\",\"count\":38,\"userId\":\"22a319c7-1bd0-4d0a-b6c5-389dbe6f03cd\"},{\"appId\":\"a7a12aff202b4d57aca60e397e02e5b4\",\"appCode\":\"XINGHUO_test_ADMINa\",\"appVersion\":\"V1.44.20230722\",\"moduleName\":\"待阅公文\",\"url\":\"modules3/OA_ZHBG/index.html?daibanType=UN_READ_COUNT\",\"iconUrl\":\"http://*************:8008/group2/M00/00/11/Cv1wGlz9sy6AGlvpAAANx3GGeiQ513.png\",\"count\":0,\"userId\":\"22a319c7-1bd0-4d0a-b6c5-389dbe6f03cd\"},{\"appId\":\"b7f068eafa4b4946962858aa8b91c46b\",\"appCode\":\"OA_ZHBG_SWL\",\"appVersion\":\"V1.86.20230722\",\"moduleName\":\"工作事务\",\"url\":\"modules3/OA_SWL/index.html\",\"iconUrl\":\"http://*************:8008/group2/M00/00/11/Cv1wHFz9sz2AQnoJAAAGC88wutk230.png\",\"count\":16,\"userId\":\"22a319c7-1bd0-4d0a-b6c5-389dbe6f03cd\"},{\"appId\":\"6fbdb5ecc07e4e4d8dc2399f0ac248d5\",\"appCode\":\"GWCL\",\"appVersion\":\"V1.20.20211127\",\"moduleName\":\"公务车辆\",\"url\":\"modules3/gwcl/index.html\",\"iconUrl\":\"http://*************:8008/group2/M02/00/48/Cv3LIF3boyyAc_PsAAAI8UTAuHA345.png\",\"count\":0,\"userId\":\"22a319c7-1bd0-4d0a-b6c5-389dbe6f03cd\"}],\"success\":true}","success":true}
2025-06-26 16:02:06.469 14428-16531/com.xinghuo.jingxin E/FXP: HttpRequest ajax onSuccess - {"msg":"操作成功","msgType":"NORMAL","obj":"{\"msg\":\"success\",\"code\":0,\"success\":true,\"oaInfoList\":[{\"id\":\"fd282eb8-6d14-4b9a-9077-f72f10058e41\",\"remarks\":\"\",\"createBy\":\"\",\"createDate\":\"2025-05-30 18:08:05\",\"updateDate\":\"2025-05-30 18:08:05\",\"isSendMessage\":\"\",\"type\":\"3\",\"title\":\"市纪委监委2025年“端午节”假期值班安排表\",\"contentText\":\"\",\"contentPc\":\"\",\"contentApp\":\"\",\"files\":\"\",\"status\":\"\",\"effectiveDate\":\"\",\"listOrder\":1,\"receiveRange\":\"\",\"receiveUserId\":\"\",\"receiveUserName\":\"\",\"receiveDeptName\":\"\",\"receiveDeptId\":\"\",\"createByName\":\"\",\"publishBy\":\"\",\"publishDate\":\"2025-05-30 18:08:05\",\"filePath\":\"\",\"isCarousel\":\"\",\"fileThumbnailPath\":\"\",\"readFlag\":\"\",\"systemNumber\":\"\",\"subType\":\"\",\"subTypeName\":\"\",\"redirectUrl\":\"\"},{\"id\":\"4eb2df1f-e560-4680-b0c9-ec1726d6f91b\",\"remarks\":\"\",\"createBy\":\"\",\"createDate\":\"2025-05-30 17:21:51\",\"updateDate\":\"2025-05-30 17:21:51\",\"isSendMessage\":\"\",\"type\":\"3\",\"title\":\"关于“端午节”放假的通知\",\"contentText\":\"\",\"contentPc\":\"\",\"contentApp\":\"\",\"files\":\"\",\"status\":\"\",\"effectiveDate\":\"\",\"listOrder\":1,\"receiveRange\":\"\",\"receiveUserId\":\"\",\"receiveUserName\":\"\",\"receiveDeptName\":\"\",\"receiveDeptId\":\"\",\"createByName\":\"\",\"publishBy\":\"\",\"publishDate\":\"2025-05-30 17:21:51\",\"filePath\":\"\",\"isCarousel\":\"\",\"fileThumbnailPath\":\"\",\"readFlag\":\"\",\"systemNumber\":\"\",\"subType\":\"\",\"subTypeName\":\"\",\"redirectUrl\":\"\"}],\"notReadNum\":11}","success":true}
2025-06-26 16:02:06.470 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(1107)] "---------------------request tododata success
    ", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (1107)
2025-06-26 16:02:06.471 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(1111)] "---------------------result：{"code":200,"msg":"查询成功","data":[{"appId":"a7a12aff202b4d57aca60e397e02e5b4","appCode":"XINGHUO_ADMIN","appVersion":"V1.44.20230722","moduleName":"待办公文","url":"modules3/OA_ZHBG/index.html","iconUrl":"http://*************:8008/group2/M00/00/11/Cv1wGlz9sy6AGlvpAAANx3GGeiQ513.png","count":38,"userId":"22a319c7-1bd0-4d0a-b6c5-389dbe6f03cd"},{"appId":"a7a12aff202b4d57aca60e397e02e5b4","appCode":"XINGHUO_test_ADMINa","appVersion":"V1.44.20230722","moduleName":"待阅公文","url":"modules3/OA_ZHBG/index.html?daibanType=UN_READ_COUNT","iconUrl":"http://*************:8008/group2/M00/00/11/Cv1wGlz9sy6AGlvpAAANx3GGeiQ513.png","count":0,"userId":"22a319c7-1bd0-4d0a-b6c5-389dbe6f03cd"},{"appId":"b7f068eafa4b4946962858aa8b91c46b","appCode":"OA_ZHBG_SWL","appVersion":"V1.86.20230722","moduleName":"工作事务","url":"modules3/OA_SWL/index.html","iconUrl":"http://*************:8008/group2/M00/00/11/Cv1wHFz9sz2AQnoJAAAGC88wutk230.png","count":16,"userId":"22a319c7-1bd0-4d0a-b6c5-389dbe6f03cd"},{"appId":"6fbdb5ecc07e4e4d8dc2399f0ac248d5","appCode":"GWCL","appVersion":"V1.20.20211127","moduleName":"公务车辆","url":"modules3/gwcl/index.html","iconUrl":"http://*************:8008/group2/M02/00/48/Cv3LIF3boyyAc_PsAAAI8UTAuHA345.png","count":0,"userId":"22a319c7-1bd0-4d0a-b6c5-389dbe6f03cd"}],"success":true}", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (1111)
2025-06-26 16:02:06.471 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(1273)] "---------------------showRequestTodoList
    ", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (1273)
2025-06-26 16:02:06.471 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(1274)] "---------------------isShowItemButNoNum: true
    ", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (1274)
2025-06-26 16:02:06.471 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(1208)] "---------------------isTodoNumChanged
    ", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (1208)
2025-06-26 16:02:06.471 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(1694)] "---------------------gettodoData
    ", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (1694)
2025-06-26 16:02:06.471 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(1234)] "---------------------TodoNum Not Changed
    ", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (1234)
2025-06-26 16:02:06.472 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(1277)] "---------------------return，不再重新渲染待办
    ", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (1277)
2025-06-26 16:02:06.477 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(1689)] "---------------------settodoData
    ", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (1689)
2025-06-26 16:02:06.480 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(1717)] "getAnnouncementData success result {"returnValue":"{\"msg\":\"success\",\"code\":0,\"success\":true,\"oaInfoList\":[{\"id\":\"fd282eb8-6d14-4b9a-9077-f72f10058e41\",\"remarks\":\"\",\"createBy\":\"\",\"createDate\":\"2025-05-30 18:08:05\",\"updateDate\":\"2025-05-30 18:08:05\",\"isSendMessage\":\"\",\"type\":\"3\",\"title\":\"市纪委监委2025年“端午节”假期值班安排表\",\"contentText\":\"\",\"contentPc\":\"\",\"contentApp\":\"\",\"files\":\"\",\"status\":\"\",\"effectiveDate\":\"\",\"listOrder\":1,\"receiveRange\":\"\",\"receiveUserId\":\"\",\"receiveUserName\":\"\",\"receiveDeptName\":\"\",\"receiveDeptId\":\"\",\"createByName\":\"\",\"publishBy\":\"\",\"publishDate\":\"2025-05-30 18:08:05\",\"filePath\":\"\",\"isCarousel\":\"\",\"fileThumbnailPath\":\"\",\"readFlag\":\"\",\"systemNumber\":\"\",\"subType\":\"\",\"subTypeName\":\"\",\"redirectUrl\":\"\"},{\"id\":\"4eb2df1f-e560-4680-b0c9-ec1726d6f91b\",\"remarks\":\"\",\"createBy\":\"\",\"createDate\":\"2025-05-30 17:21:51\",\"updateDate\":\"2025-05-30 17:21:51\",\"isSendMessage\":\"\",\"type\":\"3\",\"title\":\"关于“端午节”放假的通知\",\"contentText\":\"\",\"contentPc\":\"\",\"contentApp\":\"\",\"files\":\"\",\"status\":\"\",\"effectiveDate\":\"\",\"listOrder\":1,\"receiveRange\":\"\",\"receiveUserId\":\"\",\"receiveUserName\":\"\",\"receiveDeptName\":\"\",\"receiveDeptId\":\"\",\"createByName\":\"\",\"publishBy\":\"\",\"publishDate\":\"2025-05-30 17:21:51\",\"filePath\":\"\",\"isCarousel\":\"\",\"fileThumbnailPath\":\"\",\"readFlag\":\"\",\"systemNumber\":\"\",\"subType\":\"\",\"subTypeName\":\"\",\"redirectUrl\":\"\"}],\"notReadNum\":11}"}", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (1717)
2025-06-26 16:02:06.483 14428-16522/com.xinghuo.jingxin E/FXP: HttpRequest ajax onSuccess - {"msg":"操作成功","msgType":"NORMAL","obj":"{\"msg\":\"success\",\"code\":0,\"data\":[{\"appList\":[{\"uuid\":\"0b529919894549c6854dbf57bdc61594\",\"appId\":\"843867742221107200\",\"appCode\":\"wenba.test\",\"appName\":\"问吧试用\",\"appTerminalType\":\"3\",\"appIntro\":\"问吧\",\"appUrl\":\"\",\"describe\":\"\",\"appIcon\":\"http://*************:8008/group1/M02/00/A0/Cv3LG2CiFryAS4IgAAAqAE81DqA561.jpg\",\"jmtAppIcon\":\"http://*************:8008/group1/M02/00/A0/Cv3LG2CiFryAS4IgAAAqAE81DqA561.jpg\",\"respName\":\"余均发\",\"respCode\":\"18589082142\",\"respTelPhone\":null,\"enclosure\":\"http://*************:8008/group2/M03/00/C1/CuC2imNbKweAXmmkACC_cfHaLqc872.zip\",\"jmtEnclosure\":\"http://*************:8008/group2/M03/00/C1/CuC2imNbKweAXmmkACC_cfHaLqc872.zip\",\"appEntry\":\"index.html\",\"appState\":\"1\",\"exampleContent\":\"\",\"listOrder\":\"\",\"constructionUnit\":\"星火\",\"lastUpdateTime\":1666919189000,\"createTime\":1621235405000,\"testUser\":\"[{\\\"userLoginId\\\":\\\"dzq\\\",\\\"userName\\\":\\\"星火开发邓赞强\\\",\\\"userId\\\":\\\"555d4dc7-3282-42b6-b895-e773f8ed821e\\\"},{\\\"userLoginId\\\":\\\"fxp\\\",\\\"userName\\\":\\\"星火开发方晓鹏\\\",\\\"userId\\\":\\\"22a319c7-1bd0-4d0a-b6c5-389dbe6f03cd\\\"}]\",\"createSupplier\":\"深圳市星火电子工程公司深云团队\",\"version\":\"V1.06.20221028\",\"md5\":\"354eb92085381bfc6525d19b5848f9ba\",\"openIE\":\"\",\"isIframe\":\"\",\"appUuid\":\"\",\"gradeLevel\":0.0,\"appClassify\":\"1\",\"appClassifyCode\":\"SYYY\",\"appClassifyName\":\"所有应用\",\"versionId\":null,\"isCollect\":null,\"isBuiltIn\":\"0\",\"maintainName\":null,\"maintainTel\":null,\"backColor\":null,\"fontColor\":null,\"isRecommend\":0,\"appRange\":null,\"clickCnt\":0},{\"uuid\":\"1341ef77b98f4088abc873a70958fa88\",\"appId\":\"867824935890845696\",\"appCode\":\"foxmail_test.test\",\"appName\":\"邮箱测试\",\"appTerminalType\":\"3\",\"appIntro\":\"123\",\"appUrl\":\"\",\"describe\":\"\",\"appIcon\":\"http://*************:8008/group1/M00/00/AD/Cv3LG2D5PtKAOB8VAAG_Gx76k7Q93.jpeg\",\"jmtAppIcon\":\"http://*************:8008/group1/M00/00/AD/Cv3LG2D5PtKAOB8VAAG_Gx76k7Q93.jpeg\",\"respName\":\"邓赞强\",\"respCode\":\"123456\",\"respTelPhone\":null,\"enclosure\":\"http://*************:8008/group2/M03/00/C7/Cv3LIGD5P0mAIdHvABVN9oZ_SO0509.zip\",\"jmtEnclosure\":\"http://*************:8008/group2/M03/00/C7/Cv3LIGD5P0mAIdHvABVN9oZ_SO0509.zip\",\"appEntry\":\"modules3/OA_MAIL/index.html\",\"appState\":\"1\",\"exampleContent\":\"\",\"listOrder\":\"\",\"constructionUnit\":\"星火\",\"lastUpdateTime\":1734947234000,\"createTime\":1626947284000,\"testUser\":\"[{\\\"userLoginId\\\":\\\"fxp\\\",\\\"userName\\\":\\\"方晓鹏\\\",\\\"userId\\\":\\\"22a319c7-1bd0-4d0a-b6c5-389dbe6f03cd\\\"}]\",\"createSupplier\":\"深圳市星火电子工程公司深云团队\",\"version\":\"V1.01.20210722\",\"md5\":\"83a2b580031926d08f24641ed7f26973\",\"openIE\":\"\",\"isIframe\":\"\",\"appUuid\":\"\",\"gradeLevel\":0.0,\"appClassify\":\"1\",\"appClassifyCode\":\"SYYY\",\"appClassifyName\":\"所有应用\",\"versionId\":null,\"isCollect\":null,\"isBuiltIn\":\"0\",\"maintainName\":null,\"maintainTel\":null,\"backColor\":null,\"fontColor\":null,\"isRecommend\":0,\"appRange\":null,\"clickCnt\":0},{\"uuid\":\"7ea6f3d4d1b9459cb5c741980e746888\",\"appId\":\"633686850237628416\",\"appCode\":\"MY_INFO.test\",\"appName\":\"干部管理系统测试\",\"appTerminalType\":\"3\",\"appIntro\":\"干部管理\",\"appUrl\":\"\",\"describe\":\"\",\"appIcon\":\"http://*************:8008/group2/M02/00/2E/Cv3LIF2ldLGARDbtAABEHytGHn0984.png\",\"jmtAppIcon\":\"http://*************:8008/group2/M02/00/2E/Cv3LIF2ldLGARDbtAABEHytGHn0984.png\",\"respName\":\"测试张\",\"respCode\":\"123465\",\"respTelPhone\":null,\"enclosure\":\"http://*************:8008/group2/M02/00/67/CuC2jGJ-IeKAdT9pAEc4R-xilpU798.zip\",\"jmtEnclosure\":\"http://*************:8008/group2/M02/00/67/CuC2jGJ-IeKAdT9pAEc4R-xilpU798.zip\",\"appEntry\":\"module/index.html\",\"appS
2025-06-26 16:02:06.499 14428-16532/com.xinghuo.jingxin E/FXP: HttpRequest ajax onSuccess - {"msg":"操作成功","msgType":"NORMAL","obj":"{\"msg\":\"success\",\"code\":0,\"success\":true,\"oaInfoList\":[{\"id\":\"5f62b12a-f893-40ea-b12f-3c151f96e530\",\"remarks\":\"\",\"createBy\":\"\",\"createDate\":\"2025-06-20 15:11:46\",\"updateDate\":\"2025-06-20 15:11:46\",\"isSendMessage\":\"\",\"type\":\"4\",\"title\":\"宗理同志到政策法规研究室参加支部主题党日活动并讲授深入贯彻中央八项规定精神学习教育专题党课和警示教育课\",\"contentText\":\"\",\"contentPc\":\"\",\"contentApp\":\"\",\"files\":\"\",\"status\":\"\",\"effectiveDate\":\"\",\"listOrder\":1,\"receiveRange\":\"\",\"receiveUserId\":\"\",\"receiveUserName\":\"\",\"receiveDeptName\":\"\",\"receiveDeptId\":\"\",\"createByName\":\"\",\"publishBy\":\"\",\"publishDate\":\"2025-06-20 15:11:46\",\"filePath\":\"\",\"isCarousel\":\"\",\"fileThumbnailPath\":\"\",\"readFlag\":\"\",\"systemNumber\":\"\",\"subType\":\"\",\"subTypeName\":\"\",\"redirectUrl\":\"\"},{\"id\":\"264e7946-58f0-4d3d-a07a-7086037c8f02\",\"remarks\":\"\",\"createBy\":\"\",\"createDate\":\"2025-05-16 17:21:12\",\"updateDate\":\"2025-05-16 17:21:12\",\"isSendMessage\":\"\",\"type\":\"4\",\"title\":\"市纪委监委举办人工智能全员培训\",\"contentText\":\"\",\"contentPc\":\"\",\"contentApp\":\"\",\"files\":\"\",\"status\":\"\",\"effectiveDate\":\"\",\"listOrder\":1,\"receiveRange\":\"\",\"receiveUserId\":\"\",\"receiveUserName\":\"\",\"receiveDeptName\":\"\",\"receiveDeptId\":\"\",\"createByName\":\"\",\"publishBy\":\"\",\"publishDate\":\"2025-05-16 17:21:12\",\"filePath\":\"\",\"isCarousel\":\"\",\"fileThumbnailPath\":\"\",\"readFlag\":\"\",\"systemNumber\":\"\",\"subType\":\"\",\"subTypeName\":\"\",\"redirectUrl\":\"\"}],\"notReadNum\":79}","success":true}
2025-06-26 16:02:06.521 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(1400)] "getAllApp success result {"returnValue":"{\"msg\":\"success\",\"code\":0,\"data\":[{\"appList\":[{\"uuid\":\"a7a12aff202b4d57aca60e397e02e5b4\",\"appId\":\"262727250493837312\",\"appCode\":\"XINGHUO_ADMIN\",\"appName\":\"公文业务\",\"appTerminalType\":\"3\",\"appIntro\":\"公文业务\",\"appUrl\":\"\",\"describe\":\"\",\"appIcon\":\"http://*************:8008/group2/M00/00/11/Cv1wGlz9sy6AGlvpAAANx3GGeiQ513.png\",\"jmtAppIcon\":\"http://*************:8008/group2/M00/00/11/Cv1wGlz9sy6AGlvpAAANx3GGeiQ513.png\",\"respName\":\"星火电子\",\"respCode\":\"星火电子\",\"respTelPhone\":null,\"enclosure\":\"http://*************:8008/group1/M00/00/BE/CuC2iWffsbyAM9yiACnFt7cahpc489.zip\",\"jmtEnclosure\":\"http://*************:8008/group1/M00/00/BE/CuC2iWffsbyAM9yiACnFt7cahpc489.zip\",\"appEntry\":\"modules3/OA_ZHBG/index.html\",\"appState\":\"6\",\"exampleContent\":\"\",\"listOrder\":\"\",\"constructionUnit\":\"星火电子\",\"lastUpdateTime\":1742713180000,\"createTime\":1562918400000,\"testUser\":null,\"createSupplier\":\"深圳市星火电子工程公司深云团队\",\"version\":\"V2.18.20250323\",\"md5\":\"d022b0fcb8c1b8e193668c33b06cdf08\",\"openIE\":\"\",\"isIframe\":\"\",\"appUuid\":\"\",\"gradeLevel\":0.0,\"appClassify\":\"1\",\"appClassifyCode\":\"SYYY\",\"appClassifyName\":\"\",\"versionId\":null,\"isCollect\":null,\"isBuiltIn\":\"0\",\"maintainName\":null,\"maintainTel\":null,\"backColor\":null,\"fontColor\":null,\"isRecommend\":0,\"appRange\":null,\"clickCnt\":0},{\"uuid\":\"b7f068eafa4b4946962858aa8b91c46b\",\"appId\":\"313681495313092608\",\"appCode\":\"OA_ZHBG_SWL\",\"appName\":\"工作事务\",\"appTerminalType\":\"3\",\"appIntro\":\"工作事务\",\"appUrl\":\"\",\"describe\":\"\",\"appIcon\":\"http://*************:8008/group2/M00/00/11/Cv1wHFz9sz2AQnoJAAAGC88wutk230.png\",\"jmtAppIcon\":\"http://*************:8008/group2/M00/00/11/Cv1wHFz9sz2AQnoJAAAGC88wutk230.png\",\"respName\":\"彭方启\",\"respCode\":\"pengfangqi\",\"respTelPhone\":null,\"enclosure\":\"http://*************:8008/group2/M00/01/70/CuC2jGhL-iGAbeQNADWLdzD0FdM087.zip\",\"jmtEnclosure\":\"http://*************:8008/group2/M00/01/70/CuC2jGhL-iGAbeQNADWLdzD0FdM087.zip\",\"appEntry\":\"modules3/OA_SWL/index.html\",\"appState\":\"6\",\"exampleContent\":\"\",\"listOrder\":\"\",\"constructionUnit\":\"星火电子工程公司\",\"lastUpdateTime\":1749809988000,\"createTime\":1562918400000,\"testUser\":null,\"createSupplier\":\"深圳市星火电子工程公司深云团队\",\"version\":\"V3.20.20250613\",\"md5\":\"1091bb0374090760859026eda75af6bc\",\"openIE\":\"\",\"isIframe\":\"\",\"appUuid\":\"\",\"gradeLevel\":0.0,\"appClassify\":\"1\",\"appClassifyCode\":\"SYYY\",\"appClassifyName\":\"\",\"versionId\":null,\"isCollect\":null,\"isBuiltIn\":\"0\",\"maintainName\":null,\"maintainTel\":null,\"backColor\":null,\"fontColor\":null,\"isRecommend\":0,\"appRange\":null,\"clickCnt\":0},{\"uuid\":\"53efeb0ed9b44c0c8aeb2f5cd2898426\",\"appId\":\"326868391677661184\",\"appCode\":\"LDGZAP\",\"appName\":\"工作助手\",\"appTerminalType\":\"3\",\"appIntro\":\"工作助手\",\"appUrl\":\"\",\"describe\":\"\",\"appIcon\":\"http://*************:8008/group2/M01/00/11/Cv1wGlz9s1CAKbg0AAAFiVquXCw922.png\",\"jmtAppIcon\":\"http://*************:8008/group2/M01/00/11/Cv1wGlz9s1CAKbg0AAAFiVquXCw922.png\",\"respName\":\"星火电子\",\"respCode\":\"***********\",\"respTelPhone\":null,\"enclosure\":\"http://*************:8008/group2/M00/01/72/CuC2jGhc76OAbNprACaQn0yqW0M642.zip\",\"jmtEnclosure\":\"http://*************:8008/group2/M00/01/72/CuC2jGhc76OAbNprACaQn0yqW0M642.zip\",\"appEntry\":\"modules3/lingdaogongzuo/index.html\",\"appState\":\"6\",\"exampleContent\":\"\",\"listOrder\":\"\",\"constructionUnit\":\"星火电子\",\"lastUpdateTime\":1750921405000,\"createTime\":1562918400000,\"testUser\":null,\"createSupplier\":\"深圳市星火电子工程公司深云团队\",\"version\":\"V1.40.20250626\",\"md5\":\"08f4ed20b816c43777d8a36bf8db4670\",\"openIE\":\"\",\"isIframe\"
2025-06-26 16:02:06.521 14428-14428/com.xinghuo.jingxin I/chromium: 
2025-06-26 16:02:06.569 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(1779)] "getWorkMomentData success result {"returnValue":"{\"msg\":\"success\",\"code\":0,\"success\":true,\"oaInfoList\":[{\"id\":\"5f62b12a-f893-40ea-b12f-3c151f96e530\",\"remarks\":\"\",\"createBy\":\"\",\"createDate\":\"2025-06-20 15:11:46\",\"updateDate\":\"2025-06-20 15:11:46\",\"isSendMessage\":\"\",\"type\":\"4\",\"title\":\"宗理同志到政策法规研究室参加支部主题党日活动并讲授深入贯彻中央八项规定精神学习教育专题党课和警示教育课\",\"contentText\":\"\",\"contentPc\":\"\",\"contentApp\":\"\",\"files\":\"\",\"status\":\"\",\"effectiveDate\":\"\",\"listOrder\":1,\"receiveRange\":\"\",\"receiveUserId\":\"\",\"receiveUserName\":\"\",\"receiveDeptName\":\"\",\"receiveDeptId\":\"\",\"createByName\":\"\",\"publishBy\":\"\",\"publishDate\":\"2025-06-20 15:11:46\",\"filePath\":\"\",\"isCarousel\":\"\",\"fileThumbnailPath\":\"\",\"readFlag\":\"\",\"systemNumber\":\"\",\"subType\":\"\",\"subTypeName\":\"\",\"redirectUrl\":\"\"},{\"id\":\"264e7946-58f0-4d3d-a07a-7086037c8f02\",\"remarks\":\"\",\"createBy\":\"\",\"createDate\":\"2025-05-16 17:21:12\",\"updateDate\":\"2025-05-16 17:21:12\",\"isSendMessage\":\"\",\"type\":\"4\",\"title\":\"市纪委监委举办人工智能全员培训\",\"contentText\":\"\",\"contentPc\":\"\",\"contentApp\":\"\",\"files\":\"\",\"status\":\"\",\"effectiveDate\":\"\",\"listOrder\":1,\"receiveRange\":\"\",\"receiveUserId\":\"\",\"receiveUserName\":\"\",\"receiveDeptName\":\"\",\"receiveDeptId\":\"\",\"createByName\":\"\",\"publishBy\":\"\",\"publishDate\":\"2025-05-16 17:21:12\",\"filePath\":\"\",\"isCarousel\":\"\",\"fileThumbnailPath\":\"\",\"readFlag\":\"\",\"systemNumber\":\"\",\"subType\":\"\",\"subTypeName\":\"\",\"redirectUrl\":\"\"}],\"notReadNum\":79}"}", source: file:///android_asset/jiwei_default/modules3/jwyDesktop/index.html (1779)
2025-06-26 16:02:06.577 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(1500)] "getUserTestApps success result {"returnValue":"{\"msg\":\"success\",\"code\":0,\"data\":[{\"appList\":[{\"uuid\":\"0b529919894549c6854dbf57bdc61594\",\"appId\":\"843867742221107200\",\"appCode\":\"wenba.test\",\"appName\":\"问吧试用\",\"appTerminalType\":\"3\",\"appIntro\":\"问吧\",\"appUrl\":\"\",\"describe\":\"\",\"appIcon\":\"http://*************:8008/group1/M02/00/A0/Cv3LG2CiFryAS4IgAAAqAE81DqA561.jpg\",\"jmtAppIcon\":\"http://*************:8008/group1/M02/00/A0/Cv3LG2CiFryAS4IgAAAqAE81DqA561.jpg\",\"respName\":\"余均发\",\"respCode\":\"18589082142\",\"respTelPhone\":null,\"enclosure\":\"http://*************:8008/group2/M03/00/C1/CuC2imNbKweAXmmkACC_cfHaLqc872.zip\",\"jmtEnclosure\":\"http://*************:8008/group2/M03/00/C1/CuC2imNbKweAXmmkACC_cfHaLqc872.zip\",\"appEntry\":\"index.html\",\"appState\":\"1\",\"exampleContent\":\"\",\"listOrder\":\"\",\"constructionUnit\":\"星火\",\"lastUpdateTime\":1666919189000,\"createTime\":1621235405000,\"testUser\":\"[{\\\"userLoginId\\\":\\\"dzq\\\",\\\"userName\\\":\\\"星火开发邓赞强\\\",\\\"userId\\\":\\\"555d4dc7-3282-42b6-b895-e773f8ed821e\\\"},{\\\"userLoginId\\\":\\\"fxp\\\",\\\"userName\\\":\\\"星火开发方晓鹏\\\",\\\"userId\\\":\\\"22a319c7-1bd0-4d0a-b6c5-389dbe6f03cd\\\"}]\",\"createSupplier\":\"深圳市星火电子工程公司深云团队\",\"version\":\"V1.06.20221028\",\"md5\":\"354eb92085381bfc6525d19b5848f9ba\",\"openIE\":\"\",\"isIframe\":\"\",\"appUuid\":\"\",\"gradeLevel\":0.0,\"appClassify\":\"1\",\"appClassifyCode\":\"SYYY\",\"appClassifyName\":\"所有应用\",\"versionId\":null,\"isCollect\":null,\"isBuiltIn\":\"0\",\"maintainName\":null,\"maintainTel\":null,\"backColor\":null,\"fontColor\":null,\"isRecommend\":0,\"appRange\":null,\"clickCnt\":0},{\"uuid\":\"1341ef77b98f4088abc873a70958fa88\",\"appId\":\"867824935890845696\",\"appCode\":\"foxmail_test.test\",\"appName\":\"邮箱测试\",\"appTerminalType\":\"3\",\"appIntro\":\"123\",\"appUrl\":\"\",\"describe\":\"\",\"appIcon\":\"http://*************:8008/group1/M00/00/AD/Cv3LG2D5PtKAOB8VAAG_Gx76k7Q93.jpeg\",\"jmtAppIcon\":\"http://*************:8008/group1/M00/00/AD/Cv3LG2D5PtKAOB8VAAG_Gx76k7Q93.jpeg\",\"respName\":\"邓赞强\",\"respCode\":\"123456\",\"respTelPhone\":null,\"enclosure\":\"http://*************:8008/group2/M03/00/C7/Cv3LIGD5P0mAIdHvABVN9oZ_SO0509.zip\",\"jmtEnclosure\":\"http://*************:8008/group2/M03/00/C7/Cv3LIGD5P0mAIdHvABVN9oZ_SO0509.zip\",\"appEntry\":\"modules3/OA_MAIL/index.html\",\"appState\":\"1\",\"exampleContent\":\"\",\"listOrder\":\"\",\"constructionUnit\":\"星火\",\"lastUpdateTime\":1734947234000,\"createTime\":1626947284000,\"testUser\":\"[{\\\"userLoginId\\\":\\\"fxp\\\",\\\"userName\\\":\\\"方晓鹏\\\",\\\"userId\\\":\\\"22a319c7-1bd0-4d0a-b6c5-389dbe6f03cd\\\"}]\",\"createSupplier\":\"深圳市星火电子工程公司深云团队\",\"version\":\"V1.01.20210722\",\"md5\":\"83a2b580031926d08f24641ed7f26973\",\"openIE\":\"\",\"isIframe\":\"\",\"appUuid\":\"\",\"gradeLevel\":0.0,\"appClassify\":\"1\",\"appClassifyCode\":\"SYYY\",\"appClassifyName\":\"所有应用\",\"versionId\":null,\"isCollect\":null,\"isBuiltIn\":\"0\",\"maintainName\":null,\"maintainTel\":null,\"backColor\":null,\"fontColor\":null,\"isRecommend\":0,\"appRange\":null,\"clickCnt\":0},{\"uuid\":\"7ea6f3d4d1b9459cb5c741980e746888\",\"appId\":\"633686850237628416\",\"appCode\":\"MY_INFO.test\",\"appName\":\"干部管理系统测试\",\"appTerminalType\":\"3\",\"appIntro\":\"干部管理\",\"appUrl\":\"\",\"describe\":\"\",\"appIcon\":\"http://*************:8008/group2/M02/00/2E/Cv3LIF2ldLGARDbtAABEHytGHn0984.png\",\"jmtAppIcon\":\"http://*************:8008/group2/M02/00/2E/Cv3LIF2ldLGARDbtAABEHytGHn0984.png\",\"respName\":\"测试张\",\"respCode\":\"123465\",\"respTelPhone\":null,\"enclosure\":\"http://*************:8008/group2/M02/00/67/CuC2jGJ-IeKAdT9pAEc4R-xilpU798.zip\",\"jmtEnclosure\":\"http://*************:8008/group2/M02/00/67/CuC2jGJ-IeKAdT9pAEc4R-xilpU798.zip\",\"appEntry\"
2025-06-26 16:02:06.577 14428-14428/com.xinghuo.jingxin I/chromium: 
2025-06-26 16:02:18.379 14428-14428/com.xinghuo.jingxin I/fxp: reload - handleMessage - RELOAD_OVER_TIME_WHAT
2025-06-26 16:02:54.739 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:02:54:738]: log: level: 1, area: 2, cleanup() 167
2025-06-26 16:02:54.739 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:02:54:738]: log: level: 1, area: 2, closeSocket() 167
2025-06-26 16:02:54.739 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:02:54:739]: log: level: 2, area: 1, handleDisconnect:3
2025-06-26 16:02:54.739 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:02:54:739]: EMSessionManager::onDisConnect(): 3
2025-06-26 16:02:54.739 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:02:54:739]: log: level: 2, area: 1, ChatClient::disconnect()
2025-06-26 16:02:54.739 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:02:54:739]: log: level: 1, area: 2, cleanup() -1
2025-06-26 16:02:54.739 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:02:54:739]: log: level: 2, area: 1, handleDisconnect:14
2025-06-26 16:02:54.739 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:02:54:739]: ConnStreamClosed, reconnect using different server
2025-06-26 16:02:54.739 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:02:54:739]: notify state change to connection listener
2025-06-26 16:02:54.739 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:02:54:739]: EMConnectionListener onDisconnected
2025-06-26 16:02:55.699 14428-14428/com.xinghuo.jingxin D/Choreographer: still have 4 traversal callbacks
2025-06-26 16:02:55.742 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:02:55:740]: log: level: 2, area: 1, ChatClient::connect() 
2025-06-26 16:02:55.742 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:02:55:741]: log: level: 1, area: 2, connectSocket(): start to connecting...
2025-06-26 16:02:55.813 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:02:55:812]: log: level: 1, area: 2, connectSocket(): connect finished
2025-06-26 16:02:55.813 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:02:55:812]: log: level: 2, area: 2, connectSocket() OK: fd: 232 Client:10.252.116.114:39898 Server: 10.248.97.237:16717
2025-06-26 16:02:55.850 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:02:55:849]: notify state change to connection listener
2025-06-26 16:02:55.850 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:02:55:849]: EMConnectionListener onConnected
2025-06-26 16:02:55.850 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:02:55:850]: log: level: 2, area: 1, SEND:
    { verison : MSYNC_V1, compress_algorimth : 0, command : UNREAD, encrypt_type : [ 0 ], payload : {  } }
2025-06-26 16:02:55.877 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:02:55:877]: log: level: 1, area: 1, NO unread queue, an response for ping?
2025-06-26 16:03:55.701 14428-14428/com.xinghuo.jingxin D/Choreographer: still have 5 traversal callbacks
2025-06-26 16:03:56.119 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:03:56:119]: log: level: 1, area: 2, cleanup() 232
2025-06-26 16:03:56.121 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:03:56:121]: log: level: 1, area: 2, closeSocket() 232
2025-06-26 16:03:56.121 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:03:56:121]: log: level: 2, area: 1, handleDisconnect:3
2025-06-26 16:03:56.122 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:03:56:121]: EMSessionManager::onDisConnect(): 3
2025-06-26 16:03:56.122 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:03:56:121]: log: level: 2, area: 1, ChatClient::disconnect()
2025-06-26 16:03:56.122 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:03:56:121]: log: level: 1, area: 2, cleanup() -1
2025-06-26 16:03:56.122 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:03:56:121]: log: level: 2, area: 1, handleDisconnect:14
2025-06-26 16:03:56.123 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:03:56:121]: ConnStreamClosed, reconnect using different server
2025-06-26 16:03:56.124 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:03:56:123]: notify state change to connection listener
2025-06-26 16:03:56.124 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:03:56:123]: EMConnectionListener onDisconnected
2025-06-26 16:04:02.124 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:04:02:123]: log: level: 2, area: 1, ChatClient::connect() 
2025-06-26 16:04:02.125 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:04:02:124]: log: level: 1, area: 2, connectSocket(): start to connecting...
2025-06-26 16:04:02.180 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:04:02:179]: log: level: 1, area: 2, connectSocket(): connect finished
2025-06-26 16:04:02.181 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:04:02:180]: log: level: 2, area: 2, connectSocket() OK: fd: 167 Client:10.252.116.114:49378 Server: 10.248.97.237:16717
2025-06-26 16:04:02.220 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:04:02:218]: log: level: 2, area: 1, SEND:
    { verison : MSYNC_V1, compress_algorimth : 0, command : UNREAD, encrypt_type : [ 0 ], payload : {  } }
2025-06-26 16:04:02.221 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:04:02:219]: notify state change to connection listener
2025-06-26 16:04:02.221 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:04:02:220]: EMConnectionListener onConnected
2025-06-26 16:04:02.250 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:04:02:249]: log: level: 1, area: 1, NO unread queue, an response for ping?
2025-06-26 16:04:18.460 14428-14428/com.xinghuo.jingxin I/HwViewRootImpl: removeInvalidNode all the node in jank list is out of time
2025-06-26 16:04:47.096 14428-14428/com.xinghuo.jingxin D/HnContentRecognizerManager: getCloudConfigsBundle: cache
2025-06-26 16:04:47.096 14428-14428/com.xinghuo.jingxin D/HnContentRecognizerManager: getCloudConfigsBundle: cache
2025-06-26 16:04:47.096 14428-14428/com.xinghuo.jingxin I/HiTouch_PressGestureDetector: checkDoublePointerLimit: false
2025-06-26 16:04:47.099 14428-14428/com.xinghuo.jingxin D/HwViewRootImpl: GPLOG: params  TraceInputParams 0 pkg empty self com.xinghuo.jingxin
2025-06-26 16:04:47.102 14428-14428/com.xinghuo.jingxin D/HnContentRecognizerManager: getCloudConfigsBundle: cache
2025-06-26 16:04:47.102 14428-14428/com.xinghuo.jingxin D/HnContentRecognizerManager: getCloudConfigsBundle: cache
2025-06-26 16:04:47.102 14428-14428/com.xinghuo.jingxin D/HnContentRecognizerManager: getCloudConfigsBundle: cache
2025-06-26 16:04:47.102 14428-14428/com.xinghuo.jingxin D/HnContentRecognizerManager: getCloudConfigsBundle: cache
2025-06-26 16:04:47.107 14428-14428/com.xinghuo.jingxin W/InputMethodManager: startInputReason = 3
2025-06-26 16:04:47.107 14428-14428/com.xinghuo.jingxin V/InputMethodManager: Starting input: editorInfo=android.view.inputmethod.EditorInfo@63250fd ic=null
2025-06-26 16:04:47.107 14428-14428/com.xinghuo.jingxin V/InputMethodManager: START INPUT: view=libcordova.src.org.apache.cordova.engine.SystemWebView{dfca34 VFEDH.C.. .F...... 0,0-1060,1969 #cc78f aid=1073741830},focus=true,windowFocus=true,window=android.view.ViewRootImpl$W@9e1f9f8,displayId=0,temporaryDetach=false,hasImeFocus=true ic=null editorInfo=android.view.inputmethod.EditorInfo@63250fd startInputFlags=VIEW_HAS_FOCUS|INITIAL_CONNECTION
2025-06-26 16:04:47.109 14428-14428/com.xinghuo.jingxin I/chromium: [INFO:CONSOLE(2325)] "Uncaught TypeError: Cannot read properties of undefined (reading 'trigger')", source: file:///android_asset/jiwei_default/frame3/js/zepto.js (2325)
2025-06-26 16:04:47.111 14428-14428/com.xinghuo.jingxin V/InputMethodManager: Starting input: Bind result=InputBindResult{result=SUCCESS_WITH_IME_SESSION method=com.android.internal.inputmethod.IInputMethodSession$Stub$Proxy@8c7c1f2 id=com.baidu.input_hihonor/com.baidu.input_honor.ImeService sequence=420 result=0 isInputMethodSuppressingSpellChecker=false}
2025-06-26 16:04:55.702 14428-14428/com.xinghuo.jingxin D/Choreographer: still have 4 traversal callbacks
2025-06-26 16:05:02.257 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:05:02:255]: log: level: 1, area: 2, cleanup() 167
2025-06-26 16:05:02.261 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:05:02:258]: log: level: 1, area: 2, closeSocket() 167
2025-06-26 16:05:02.261 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:05:02:259]: log: level: 2, area: 1, handleDisconnect:3
2025-06-26 16:05:02.261 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:05:02:259]: EMSessionManager::onDisConnect(): 3
2025-06-26 16:05:02.261 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:05:02:259]: log: level: 2, area: 1, ChatClient::disconnect()
2025-06-26 16:05:02.261 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:05:02:259]: log: level: 1, area: 2, cleanup() -1
2025-06-26 16:05:02.261 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:05:02:259]: log: level: 2, area: 1, handleDisconnect:14
2025-06-26 16:05:02.261 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:05:02:259]: ConnStreamClosed, reconnect using different server
2025-06-26 16:05:02.261 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:05:02:260]: notify state change to connection listener
2025-06-26 16:05:02.262 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:05:02:260]: EMConnectionListener onDisconnected
2025-06-26 16:05:10.261 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:05:10:259]: log: level: 2, area: 1, ChatClient::connect() 
2025-06-26 16:05:10.261 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:05:10:260]: log: level: 1, area: 2, connectSocket(): start to connecting...
2025-06-26 16:05:10.348 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:05:10:346]: log: level: 1, area: 2, connectSocket(): connect finished
2025-06-26 16:05:10.348 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:05:10:346]: log: level: 2, area: 2, connectSocket() OK: fd: 167 Client:10.252.116.114:42040 Server: 10.248.97.237:16717
2025-06-26 16:05:10.399 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:05:10:398]: log: level: 2, area: 1, SEND:
    { verison : MSYNC_V1, compress_algorimth : 0, command : UNREAD, encrypt_type : [ 0 ], payload : {  } }
2025-06-26 16:05:10.400 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:05:10:399]: notify state change to connection listener
2025-06-26 16:05:10.401 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:05:10:400]: EMConnectionListener onConnected
2025-06-26 16:05:10.446 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:05:10:445]: log: level: 1, area: 1, NO unread queue, an response for ping?
2025-06-26 16:05:55.705 14428-14428/com.xinghuo.jingxin D/Choreographer: still have 5 traversal callbacks
2025-06-26 16:06:10.498 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:06:10:497]: log: level: 1, area: 2, cleanup() 167
2025-06-26 16:06:10.499 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:06:10:497]: log: level: 1, area: 2, closeSocket() 167
2025-06-26 16:06:10.499 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:06:10:498]: log: level: 2, area: 1, handleDisconnect:3
2025-06-26 16:06:10.499 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:06:10:498]: EMSessionManager::onDisConnect(): 3
2025-06-26 16:06:10.499 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:06:10:498]: log: level: 2, area: 1, ChatClient::disconnect()
2025-06-26 16:06:10.499 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:06:10:498]: log: level: 1, area: 2, cleanup() -1
2025-06-26 16:06:10.499 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:06:10:498]: log: level: 2, area: 1, handleDisconnect:14
2025-06-26 16:06:10.499 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:06:10:498]: ConnStreamClosed, reconnect using different server
2025-06-26 16:06:10.499 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:06:10:499]: notify state change to connection listener
2025-06-26 16:06:10.500 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:06:10:499]: EMConnectionListener onDisconnected
2025-06-26 16:06:19.503 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:06:19:499]: log: level: 2, area: 1, ChatClient::connect() 
2025-06-26 16:06:19.504 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:06:19:500]: log: level: 1, area: 2, connectSocket(): start to connecting...
2025-06-26 16:06:19.572 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:06:19:571]: log: level: 1, area: 2, connectSocket(): connect finished
2025-06-26 16:06:19.573 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:06:19:572]: log: level: 2, area: 2, connectSocket() OK: fd: 232 Client:10.252.116.114:45364 Server: 10.248.97.237:16717
2025-06-26 16:06:19.617 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:06:19:616]: log: level: 2, area: 1, SEND:
    { verison : MSYNC_V1, compress_algorimth : 0, command : UNREAD, encrypt_type : [ 0 ], payload : {  } }
2025-06-26 16:06:19.619 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:06:19:616]: notify state change to connection listener
2025-06-26 16:06:19.619 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:06:19:617]: EMConnectionListener onConnected
2025-06-26 16:06:19.647 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:06:19:646]: log: level: 1, area: 1, NO unread queue, an response for ping?
2025-06-26 16:06:55.713 14428-14428/com.xinghuo.jingxin D/Choreographer: still have 5 traversal callbacks
2025-06-26 16:07:10.683 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:07:10:682]: log: level: 1, area: 1, NO unread queue, an response for ping?
2025-06-26 16:07:10.684 14428-14459/com.xinghuo.jingxin D/ONE SDK: [2025/06/26 16:07:10:683]: native_1sendPing
