/**
 * Created by dzq on 2019/3/27.
 */
function tapAppSetting() {
    load("app_settings.html", {loginId: loginId});
}

function tapHotPost() {
    load("hot_post.html", {loginId: loginId});
}
function openColumnMore(programCode) {
    if (programConfigList[programCode].openMore) {
        programConfigList[programCode].openMore();
    }
}

function openColumnDetail(programCode) {
    if (programConfigList[programCode].openDetail) {
        programConfigList[programCode].openDetail();
    }
}
function openUserInfo() {
    var params = {
        action: "com_xh_info_deepcloud_ui_activity_MyInfo",
        names: ["userInfoString"],
        values: [JSON.stringify(mUserInfo)]
    };

    app.szgaplugin.startActivityForResult(params, function (res) {
        //alert("success: " + res);
        //var result = eval('(' + res + ')');
    }, function (err) {
        //alert("fail: " + err);
    });
}

//    var touchX1;
//    var touchX2;
//    var touchY1;
//    var touchY2;
//    var blogListData;
//    var blogIndex;

//    var statChart;
//    var statData;
//    var statType = "dj";
//    var xAxisData;
//    var categories;
//    var series;
//function getUserInfo(programCode) {
//    Cordova.exec(function (result) {
//        //alert(result);
//        var userInfo = eval('(' + result + ')');
//
//        userPosition = userInfo.actualPosition;
//        if (userPosition == "") {
//            userPosition = "其他";
//        }
//        deptName = userInfo.deptName;
//        userPhotoPath = userInfo.userPhotoPath;
//
//        var bt = baidu.template;
//        var html = bt("user_info", {
//            loginId: loginId,
//            userName: userName,
//            userPosition: userPosition,
//            deptName: deptName,
//            userPhotoPath: userInfo.userPhotoPath
//        });
//
//        $(".info-column-content[value='" + programCode + "']").html(html);
//    }, null, "LinkPlugin", "getUserInfo", [userId]);
//}
function tapAnnouncement(type) {
    if(!isonline){
        app.hint('当前网络不可用')
        return;
    }

    if (type === 1 && announceUnreadCount === 0) return;

    var screenWidth = window.screen.width;  //X6折叠屏窄屏346，宽屏717；5Pro单屏375；7Pro单屏366

    if(screenWidth == 346 || screenWidth == 717){ // X6折叠屏窄屏346，宽屏717
        var params = {
            "url": "file:///storage/emulated/0/.hiboard/com.xinghuo.jingxin/Unpack/com.page.index/" + LIGHT_APP_VERSION + "/jwyDesktop_xc/modules3/jwyDesktop/announcement.html"
        };
    } else if(screenWidth == 375){  //5Pro单屏375
        var params = {
            "url": "file:///storage/emulated/10/.hiboard/com.xinghuo.jingxin/Unpack/com.page.index/" + LIGHT_APP_VERSION + "/jwyDesktop_xc/modules3/jwyDesktop/announcement.html"
        };
    } else if(screenWidth == 366){  //7Pro单屏366
        var params = {
            "url": "file:///storage/emulated/0/.hiboard/com.xinghuo.jingxin/Unpack/com.page.index/" + LIGHT_APP_VERSION + "/jwyDesktop_xc/modules3/jwyDesktop/announcement.html"
        };
    } else {
        var params = {
            "url": "file:///storage/emulated/10/.hiboard/com.xinghuo.jingxin/Unpack/com.page.index/" + LIGHT_APP_VERSION + "/jwyDesktop_xc/modules3/jwyDesktop/announcement.html"
        };
    }

    Cordova.exec(onSuccess, onError, "Page", "loadUrlInNewPage", [params]);
    function onSuccess(res) {
        console.log(getTime() + res)
    }
    function onError(err) {
        console.log(getTime() + err)
    }
}
function tapWorkMoment(type) {
    if(!isonline){
        app.hint('当前网络不可用')
        return;
    }
    if (type === 1 && workUnreadCount === 0) return;

    var screenWidth = window.screen.width;  //X6折叠屏窄屏346，宽屏717；5Pro单屏375；7Pro单屏366
    if(screenWidth == 346 || screenWidth == 717){ // X6折叠屏窄屏346，宽屏717
        var params = {
            "url": "file:///storage/emulated/0/.hiboard/com.xinghuo.jingxin/Unpack/com.page.index/" + LIGHT_APP_VERSION + "/jwyDesktop_xc/modules3/jwyDesktop/work_moment.html"
        };
    } else if(screenWidth == 375){  //5Pro单屏375
        var params = {
            "url": "file:///storage/emulated/10/.hiboard/com.xinghuo.jingxin/Unpack/com.page.index/" + LIGHT_APP_VERSION + "/jwyDesktop_xc/modules3/jwyDesktop/work_moment.html"
        };
    } else if(screenWidth == 366){  //7Pro单屏366
        var params = {
            "url": "file:///storage/emulated/0/.hiboard/com.xinghuo.jingxin/Unpack/com.page.index/" + LIGHT_APP_VERSION + "/jwyDesktop_xc/modules3/jwyDesktop/work_moment.html"
        };
    } else {
        var params = {
            "url": "file:///storage/emulated/10/.hiboard/com.xinghuo.jingxin/Unpack/com.page.index/" + LIGHT_APP_VERSION + "/jwyDesktop_xc/modules3/jwyDesktop/work_moment.html"
        };
    }
    Cordova.exec(onSuccess, onError, "Page", "loadUrlInNewPage", [params]);
    function onSuccess(res) {
        console.log(getTime() + res)
    }
    function onError(err) {
        console.log(getTime() + err)
    }
}
function loadWorkMomentDetail(id) {
    if(!isonline){
        app.hint('当前网络不可用')
        return;
    }
    app.loadWithUrl("announcement_detail.html", {announcementId: id,access_token:AnnouncementToken,detailType:"DONGTAI"});
}
function loadAnnounceDetail(id) {
    if(!isonline){
        app.hint('当前网络不可用')
        return;
    }
    app.loadWithUrl("announcement_detail.html", {announcementId: id,access_token:AnnouncementToken,detailType:"TONGZHI"});
}

function backFunc() {
    if (hasDialogOpen) {
        $("#" + dialogId).dialog("close");
        hasDialogOpen = false;
        return;
    }
    if (exitFlag) {
        app.back();
    } else {
        app.hint("再按一次退出程序");
        exitFlag = true;
        setTimeout("exitFlag = false;", 3000);
    }
}

//返回退出
//function backFunc() {
//    var isIndexPage = ui.Page.history.length <= 1 ? true : false;
//    if (isIndexPage) {
//        if (exitFlag) {
//            app.exit();
//        } else {
//            exitFlag = true;
//            app.hint("再次点击返回退出应用");
//            setTimeout("exitFlag = false;", 3000);
//        }
//    } else {
//        //单页后退，多页不处理
//        if (ui.settings.appType == 'single') {
//            ui.Page.back();
//        }
//    }
//}

//    function closeStatisticsSelectDialog() {
//        $("#dlg_statistics_select").dialog("close");
//        hasDialogOpen = false;
//    }

//    function openAppMore() {
//        var params = {
//            action: "xinghuo.mpaas.ui.AppManagerActivity",
//            names: ["userCode", "userId"],
//            values: [loginId, userId]
//        };
//
//        app.szgaplugin.startActivityForResult(params, function (res) {
//            //alert("sucess: " + res);
//            var result = eval('(' + res + ')');
//            if (result.status == "1") {
//                getAllApp("20190325141832479");
//            }
//        }, function (err) {
//            //alert("error: " + err);
//        });
//    }


//    function openDongtaiDetail(blogId) {
//        //alert(blogId);
//        var params = {
//            action: "xinghuo.mpaas.dynamic.ui.DynamicDefaultActivity1",
//            names: ["dynamicId"],
//            values: [blogId]
//        };
//
//        app.szgaplugin.startActivityForResult(params, function (res) {
//            //alert("success: " + res);
//        }, function (err) {
//            //alert("fail: " + err);
//        });
//    }


//    function initStatisticsSelectDialog() {
////            $("#dlg_statistics_select").dialog({
////                width: "80%",
////                autoOpen: false,
////                closeBtn: true
////			}).dialog("data", "_wrap").addClass("dialog-blue").addClass("dialog-btstyle").addClass("dialog-btstyle-button");
//        $("#dlg_statistics_select").dialog({
//            width: "80%",
//            autoOpen: false,
//            closeBtn: false
//        }).dialog("data", "_wrap").addClass("dialog-stat").addClass("dialog-btstyle").addClass("dialog-btstyle-button");
//    }
//    function openColumnSetting() {
//        var params = {
//            action: "com_xhinfo_deepcloud_ui_activity_ColumnSetActivity",
//            names: ["userCode"],
//            values: [loginId]
//        };
//
//        app.szgaplugin.startActivityForResult(params, function (res) {
//            //alert("success: " + res);
//            var result = eval('(' + res + ')');
//            if (result.status == "1") {
//                //app.refresh();
//                getProgramConfigRemote();
//            }
//        }, function (err) {
//            //alert("fail: " + err);
//        });
//    }

//    function openDisplaySetting() {
//        var params = {
//            action: "com_xhinfo_deepcloud_ui_acitvity_DisplaySetting",
//            names: ["userCode"],
//            values: [loginId]
//        };
//
//        app.szgaplugin.startActivityForResult(params, function (res) {
//            //alert("success: " + res);
//            var result = eval('(' + res + ')');
//            if (result.status == "1") {
//                //app.refresh();
//                getProgramConfigRemote();
//            }
//        }, function (err) {
//            //alert("fail: " + err);
//        });
//    }
//function getDeviceType() {
//    Cordova.exec(function (result) {
//        deviceType = result;
//        console.log(result)
//        if (deviceType == "SHT-AL09") {
//            document.getElementById("skin_style").href = "css/skin_SHT-AL09.css";
//        }
//    }, null, "ExtendApp", "getMobileInfo", []);
//}

//页面跳转load(),loadUrlNewPage();
//    function load(url, params, slideType, progress) {
//        if (typeof (url) == "undefined") {
//            app.alert("url is necessary!");
//            return;
//        }
//        params = params || {};
//        slideType = slideType || "left";
//        var obj = {
//            url: url,
//            params: params,
//            slideType: slideType,
//            progress: progress
//        };
//        loadUrlNewPage(obj);
//    }

//    function loadUrlNewPage(params) {
//        if (!params) {
//            app.alert("should be object like {url:'http://domain',params:{....}}");
//            return;
//        }
//        if (!params.url) {
//            app.alert("url is necessary!");
//            return;
//        }
//        var url = params.url;
//        if (url.startWith("http://") || url.startWith("https://") || url.startWith("file://") || url.startWith("/")) {
//            //to do
//        } else {
//            // 处理相对路径
//            var selfUrl = window.location.href;
//            var lio = selfUrl.lastIndexOf("/");
//            url = selfUrl.substring(0, lio) + "/" + url;
//            params.url = url;
//        }
//        // 如果是pc的话直接执行
//        if (app.utils.isPC()) {
//            window.location.href = url;
//            return;
//        }
//        Cordova.exec(null, null, "Page", "loadUrlInNewPage", [params]);
//    }

//    function openLAPreLoadList() {
//        var params = {
//            action: "xinghuo.mpaas.ui.MyAppPreloadedActivity",
//            names: ["appData"],
//            values: [JSON.stringify(userAppData)]
//        };
//
//        //alert(JSON.stringify(params));
//
//        app.szgaplugin.startActivityForResult(params, function (res) {
//            //alert("sucess: " + res);
//        }, function (err) {
//            //alert("error: " + err);
//        });
//    }

//function getDateTime(timestamp) {
//    var date = new Date(timestamp);
//    var year = date.getFullYear();
//    var month = date.getMonth() + 1;
//    if (month < 10) {
//        month = "0" + month;
//    }
//    var day = date.getDate();
//    var day = date.getDate();
//    if (day < 10) {
//        day = "0" + day;
//    }
//    var hour = date.getHours();
//    if (hour < 10) {
//        hour = "0" + hour;
//    }
//    var minute = date.getMinutes();
//    if (minute < 10) {
//        minute = "0" + minute;
//    }
//    var second = date.getSeconds();
//    if (second < 10) {
//        second = "0" + second;
//    }
//
//    return year + "-" + month + "-" + day + " " + hour + ":" + minute + ":" + second
//}
//
//function getDateTime2(timestamp) {
//    var ret = "";
//
//    var today = new Date();
//    var now = today.getTime();
//    var delta = (now - timestamp) / 1000;
//
//    if (delta <= 60 * 60) {
//        ret = delta / 60;
//        ret = parseInt(ret);
//        ret = ret + "分钟前";
//    } else if (delta <= 60 * 60 * 24) {
//        ret = delta / (60 * 60);
//        ret = parseInt(ret);
//        ret = ret + "小时前";
//    } else {
//        ret = delta / (60 * 60 * 24);
//        ret = parseInt(ret);
//        ret = ret + "天前";
//    }
//
//    return ret;
//}