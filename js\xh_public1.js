﻿/*
 * version:2.1
 * author:pa_dev
 * date:2018-1-24
 * */
//document.write("<script language=javascript src='vconsole.min.js'></script>");
var APP_NAME = "";
var APP_VERSION = "";

//每个应用的默认字体
var SERVER_CHARSET = "UTF-8";

//全局变量,端口是否转发,每个应用可以单独配置该变量
var isProxy = true;

//设定应用所有get和post请求，采用底层get和post（非品高）
var isUsingNativeGetAndPost = false;

var XH_DEBUG_STATUS = false;//是不是测试版，默认为正式版

//是否连接星火转发
var isUsingXhServer = true;
var xh_closeGps = false;

var XH_URL = "http://***********:9999/service/forward";
var XH_URL3 = "http://***********:9999/service/requestForward";//新的接口
//var XH_URL = "http://************:9999/service/forward";

/**
 * Created by jack on 2014/12/26.
 * Modify by wang<PERSON>，移动到全局
 */
//统一处理请求url
var door_url = "http://************:9027/supp/httpClient";
//var door_url = "http://***********:9000/supp/httpClient";


(function (window) {
    /*========================xinghuo=======================================*/
    //

    //因为要获取登陆人的信息，所以要把linkplugins.js中的函数引进来
    window.app.link = window.app.link || {};

    app.link.getLoginInfo = function (callback) {
        var successCallback = function (result) {
            callback(app.utils.toJSON(result));
        };
        Cordova.exec(successCallback, null, "LinkPlugin", "getLoginInfo", []);
    }
    //添加获取设备号方法  2014-12-19 yintaiyuan
    app.getDeviceId = function (callback) {
        Cordova.exec(callback, null, "ExtendApp", "getDeviceId", []);
    }

    //添加获取meid的方法  2014-12-24 yintaiyuan
    app.getMeid = function (callback) {
        Cordova.exec(callback, null, "ExtendApp", "getMeid", []);
    }

    //添加获取imsi的方法 2014-12-24 yintaiyuan
    app.getImsi = function (callback) {
        Cordova.exec(callback, null, "ExtendApp", "getImsi", []);
    }

    //
    app.getUrl = function (orgin_url) {
        if (isProxy == true) {
            return door_url;
        }
        else {
            return orgin_url;
        }
    }


    window.xh = window.xh || {};

    //文件下载，带上手机硬件参数
    xh.downloadFile3 = function (filePath, url, success, fail) {
        var fileTransfer = new FileTransfer();

        if (typeof (userLoginId) == "undefined" || typeof (deviceId) == "undefined" ||
            typeof (meid) == "undefined" || typeof (imsi) == "undefined" ||
            typeof (app_device) == "undefined") {
            getInfos();
        }

        url += "&APP_DEVICE=" + JSON.stringify(app_device);
        url += "&APP_NAME=" + APP_NAME;
        url += "&APP_VERSION=" + APP_VERSION;
        url += "&SERVER_CHARSET=" + SERVER_CHARSET;

        var uri = encodeURI(url);
        var successCallback = function (result) {
            //alert("ss :" + JSON.stringify(result));
            success(result);
        };
        var failCallback = function (result) {
            //alert("ff :" + JSON.stringify(result));
            //检测错误返回，进行统一提示。
            checkFailCallback(result);
            fail(result);
        };

        //alert("wangxi");
        fileTransfer.download(
            uri,
            filePath,
            successCallback,
            failCallback
        );
    }

    //文件下载，带上手机硬件参数  lxt修改新的接口，全部转向新的接口，只经过一次转发，不再使用door_url
    xh.downloadFile = function (filePath, url, success, fail) {
        var fileTransfer = new FileTransfer();

        if (typeof (userLoginId) == "undefined" || typeof (deviceId) == "undefined" ||
            typeof (meid) == "undefined" || typeof (imsi) == "undefined" ||
            typeof (app_device) == "undefined") {
            getInfos();
        }

        url += "&APP_DEVICE=" + JSON.stringify(app_device);
        url += "&APP_NAME=" + APP_NAME;
        url += "&APP_VERSION=" + APP_VERSION;
        url += "&SERVER_CHARSET=" + SERVER_CHARSET;

        //var uri=encodeURI(url);
        var temp = url.replace('?', '&');
        var newUri = getAppUrl(temp);
        var ret = XH_URL3 + newUri;
        var uri = encodeURI(ret);
        console.log("xh:"+JSON.stringify(uri));
        var successCallback = function (result) {
            //alert(JSON.stringify(result));
            console.info("success:"+JSON.stringify(result));
            success(result);
        };
        var failCallback = function (result) {
            //alert(JSON.stringify(result));
            console.error("error:"+JSON.stringify(result));
            //检测错误返回，进行统一提示。
            checkFailCallback(result);
            fail(result);
        };

        fileTransfer.download(
            uri,
            filePath,
            successCallback,
            failCallback
        );
    }

    function translateUrl(url) {

        var temp = url.replace('?', '&');
        var ret = XH_URL + "?forwardUrl=" + temp;
        return ret;
    }

    //文件上传，带上手机硬件参数
    xh.uploadFile3 = function (filePath, url, success, fail, options) {
        var fileTransfer = new FileTransfer();

        if (typeof (userLoginId) == "undefined" || typeof (deviceId) == "undefined" ||
            typeof (meid) == "undefined" || typeof (imsi) == "undefined" ||
            typeof (app_device) == "undefined") {
            getInfos();
        }

        url += "&APP_DEVICE=" + JSON.stringify(app_device);
        url += "&APP_NAME=" + APP_NAME;
        url += "&APP_VERSION=" + APP_VERSION;
        url += "&SERVER_CHARSET=" + SERVER_CHARSET;

//        var uri=encodeURI(url);

        var uri;
        if (isUsingXhServer) {
            //jmt地址转换ga地址
//            var header = url.substring(0, jmt_url.length);
//            if (header == jmt_url) {
//                var tmp = url.substring(jmt_url.length);
//                url = ga_url + tmp;
//            }

            var temp = translateUrl(url);
            uri = encodeURI(temp);
//           app.alert("uri : " + uri);
        } else {
            uri = encodeURI(url);
        }

        var successCallback = function (result) {
            //app.alert(JSON.stringify(result));

            if (isUsingXhServer) {
                var ret = eval("(" + result.response + ")");
                var flag = ret.success;
                var msg = ret.msg;
                var obj = ret.obj;
                if (typeof (flag) == 'undefined') {
                    //没有错误检查机制
                    success({"response": ret});
                    return;
                }

                var msgType = ret.msgType;
                showMsg(flag, msgType, msg);
                //if (!flag) {
                //    app.hint(msg);
                //}
                success({"response": obj});
            } else {
                success(result);
            }

//            success(result);
        };
        var failCallback = function (result) {
            //检测错误返回，进行统一提示。
            checkFailCallback(result);
            fail(result);
        };

        fileTransfer.upload(
            filePath,
            uri,
            successCallback,
            failCallback,
            options
        );
    }

    function getAppUrl(url) {
        if (url.indexOf("APP_URL") >= 0) {
            var temp = url.replace('?', '&');
            var newUrl = "?xhValidUrl1225=" + temp;
            return newUrl;
        } else {
            var temp = url.replace('?', '&');
            return "?APP_URL=" + url;
        }
    }


    //文件上传，带上手机硬件参数  lxt修改新的接口，全部转向新的接口，只经过一次转发，不再使用door_url
    xh.uploadFile = function (filePath, url, success, fail, options) {
        var fileTransfer = new FileTransfer();

        if (typeof (userLoginId) == "undefined" || typeof (deviceId) == "undefined" ||
            typeof (meid) == "undefined" || typeof (imsi) == "undefined" ||
            typeof (app_device) == "undefined") {
            getInfos();
        }

        url += "&APP_DEVICE=" + JSON.stringify(app_device);
        url += "&APP_NAME=" + APP_NAME;
        url += "&APP_VERSION=" + APP_VERSION;
        url += "&SERVER_CHARSET=" + SERVER_CHARSET;

        var uri;
        var temp = url.replace('?', '&');
        var newUri = getAppUrl(temp);
        var ret = XH_URL3 + newUri;
        //alert(ret);
        uri = encodeURI(ret);
        console.log("xh:"+JSON.stringify(uri));
        var successCallback = function (result) {

            var ret = eval("(" + result.response + ")");
            console.info("success:"+JSON.stringify(ret));
            var flag = ret.success;
            var msg = ret.msg;
            var obj = ret.obj;
            if (typeof (flag) == 'undefined') {
                //没有错误检查机制
                success({"response": ret});
                return;
            }

            var msgType = ret.msgType;
            showMsg(flag, msgType, msg);
            success({"response": obj});

        };
        var failCallback = function (result) {
            //检测错误返回，进行统一提示。
            console.error("error:"+JSON.stringify(result));
            checkFailCallback(result);
            fail(result);
        };

        fileTransfer.upload(
            filePath,
            uri,
            successCallback,
            failCallback,
            options
        );
    }

    xh.uploadFile2 = function (filePath, url, success, fail, options) {
        var fileTransfer = new FileTransfer();

        if (typeof (userLoginId) == "undefined" || typeof (deviceId) == "undefined" ||
            typeof (meid) == "undefined" || typeof (imsi) == "undefined" ||
            typeof (app_device) == "undefined") {
            getInfos();
        }

        url += "&APP_DEVICE=" + JSON.stringify(app_device);
        url += "&APP_NAME=" + APP_NAME;
        url += "&APP_VERSION=" + APP_VERSION;
        url += "&SERVER_CHARSET=" + SERVER_CHARSET;


        var uri = encodeURI(url);
        var successCallback = function (result) {
            success(result);
        };
        var failCallback = function (result) {
            //检测错误返回，进行统一提示。
            checkFailCallback(result);
            fail(result);
        };

        fileTransfer.upload(
            filePath,
            uri,
            successCallback,
            failCallback,
            options
        );
    }

    //post,get提交时带上手机硬件参数
    var userLoginId;
    var deviceId;
    var meid;
    var imsi;
    var app_device;

    function getInfos(geInfoAfter) {
//        alert("get infos");
        app.link.getLoginInfo(function (result) {
            //data.loginId = result.loginId;
            userLoginId = result.loginId;
            //获取设备号
            app.getDeviceId(function (result) {
                //data.deviceId = result;
                deviceId = result;
                //获取meid
                app.getMeid(function (result) {
                    meid = result;
                    //获取imsi
                    app.getImsi(function (result) {
                        imsi = result;
                        app_device = {
                            //"deviceUuid" : device.uuid,
                            //"deviceVersion" : device.version,
                            //"deviceName" : device.name,
                            //"platform" : device.platform,
                            //"model" : device.model,
                            "bingoDeviceId": deviceId,
                            "loginId": userLoginId,
                            "meid": meid,
                            "imsi": imsi
                        };
                        geInfoAfter && geInfoAfter();
                    });

                });
            });

        });

    }

    var jmt_url = "http://***********";
    var ga_url = "http://10.42.0.235";

    var MSG_TYPE_NORMAL = "NORMAL";
    var MSG_TYPE_WARN = "WARN";
    var MSG_TYPE_ERROR = "ERROR";

    //是否显示后台错误提示
    function showMsg(flag, level, msg) {
        if (!flag) {
            if (MSG_TYPE_WARN == level) {
                //alert 提示
                app.alert(msg, null, "警告");
            } else if (MSG_TYPE_ERROR == level) {
                app.hint("错误提示：" + msg);
            } else if (MSG_TYPE_NORMAL == level) {
                //不提示
            } else {
                //unknown type
                app.hint("未知错误：" + msg);
            }
        }
    }

    //2014-12-18 yintaiyuan为了解决timeout失效的问题，暂时改为调用$.ajax，设置超时为30秒
    //发送POST请求
    xh.post3 = function (url, data, success, fail, isCloud) {
        //当前登陆的LoginId(必须先引用linkplugins.js中的函数，如上app.link部分)

        if (typeof (userLoginId) == "undefined" || typeof (deviceId) == "undefined" ||
            typeof (meid) == "undefined" || typeof (imsi) == "undefined" ||
            typeof (app_device) == "undefined") {
            getInfos();
        }
        data.APP_DEVICE = JSON.stringify(app_device);
        data.APP_NAME = APP_NAME;
        data.APP_VERSION = APP_VERSION;
        data.SERVER_CHARSET = SERVER_CHARSET;

//        alert("xh:" + JSON.stringify(data)); //wangxi

        var timeout = data.timeout;
        if (typeof (timeout) == "undefined" || timeout == "") {
            timeout = 30000;
        }

        if (isUsingXhServer) {
//            var header = url.substring(0, jmt_url.length);
//            if (header == jmt_url) {
//                var tmp = url.substring(jmt_url.length);
//                url = ga_url + tmp;
//            }

            data.forwardUrl = url;
            url = XH_URL;
        }

        if ((typeof (isCloud) != "undefined" && isCloud == false) || isUsingNativeGetAndPost == true) {
            var successCallback = function (result) {
                if (isUsingXhServer) {
                    var ret = eval("(" + result + ")");
                    var flag = ret.success;
                    var msg = ret.msg;
                    var obj = ret.obj;

                    if (typeof (flag) == 'undefined') {
                        //没有错误检查机制
                        success({"returnValue": ret});
                        return;
                    }

                    var msgType = ret.msgType;
                    showMsg(flag, msgType, msg);
                    //if (!flag) {
                    //    app.hint(msg);
                    //}
                    success({"returnValue": obj});
                } else {
                    success({"returnValue": result});
                }

            };
            var failCallback = function (result) {
                checkFailCallback(result);
                fail({"returnValue": result});
            };
            $.ajax({
                url: url,
                data: data,
                async: app.utils.toJSON(data).async, // || true,
                timeout: timeout,
                type: "POST",
                success: successCallback,
                error: failCallback
            });
        }
        else {
            var successCallback = function (result) {
                if (isUsingXhServer) {
                    var ret = eval("(" + result.returnValue + ")");
                    //alert(JSON.stringify(ret));
                    var flag = ret.success;
                    var msg = ret.msg;
                    var obj = ret.obj;

                    if (typeof (flag) == 'undefined') {
                        //没有错误检查机制
//                    app.hint("flag is null.");
                        success({"returnValue": ret});
                        return;
                    }

                    var msgType = ret.msgType;
                    showMsg(flag, msgType, msg);
                    //if (!flag) {
                    //    app.hint(msg);
                    //}
                    success({"returnValue": obj});
                } else {
                    success(result);
                }
            };
            var failCallback = function (result) {
                //检测错误返回，进行统一提示。
                checkFailCallback(result);
                fail(result);
            };

            app.ajax({
                "url": url,
                "data": data,
                "timeout": timeout,
                "method": "POST",
                // application/json
                "contentType": "application/x-www-form-urlencoded",
                "success": successCallback,
                "fail": failCallback,
                "async": app.utils.toJSON(data).async
            });
        }
    }

    //2014-12-25 lxt修改新的接口，全部转向新的接口，只经过一次转发，不再使用door_url
    /*
     * post请求参数说明：
     * 1.APP_NAME 应用标识符
     * 2.APP_VERSION 应用版本号
     * 3.APP_URL 服务方接口地址
     * 4.timeout 超时时间
     * 5.APP_DEVICE 设备信息 标识json格式字符串，包含参数:deviceVersion:系统版本号,deviceName:机器名称,
     platform:平台(android/ios),model:机型,loginId:登录账号,meid,imsi
     * 6.SERVER_CHARSET 向服务方接口请求使用的编码 默认UTF-8
     * 7.PostOrGet 向服务方接口请求使用的方式（公安网）默认POST(暂时只支持GET/POST)
     * 8.REQUEST_TYPE 向服务方接口请求使用的方式（移动信息网）默认POST(暂时只支持GET/POST)
     * 9.headerParameter	向服务方接口请求携带的HTTP头	标准json格式字符串
     * 10.bodyEntity	需要直接放入requestBody的数据	常用于application/json的请求，与参数contentType配合使用
     * 11.contentType	向服务方接口请求使用的contentType	默认application/x-www-form-urlencoded
     * */
    //发送POST请求
    xh.post = function (url, data, success, fail, isCloud) {
        //当前登陆的LoginId(必须先引用linkplugins.js中的函数，如上app.link部分)
        if (typeof (userLoginId) == "undefined" || typeof (deviceId) == "undefined" ||
            typeof (meid) == "undefined" || typeof (imsi) == "undefined" ||
            typeof (app_device) == "undefined") {
            getInfos(geInfoAfter)
        } else {
            geInfoAfter();
        }
        function geInfoAfter() {
                data.APP_DEVICE = JSON.stringify(app_device);
                data.APP_NAME = APP_NAME;
                data.APP_VERSION = APP_VERSION;
                data.SERVER_CHARSET = SERVER_CHARSET;
                var timeout = data.timeout;
                if (typeof (timeout) == "undefined" || timeout == "") {
                    timeout = 30000;
                }

                if (data.APP_URL != undefined && data.APP_URL.length > 0) {
                    //如果有APP_URL，则不作处理
                } else {
                    data.APP_URL = url;//将url当作参数
                }
                url = XH_URL3;//所以接口都通过这个来转发

                //alert("xh:" + JSON.stringify(data)); //wangxi
                 console.log("xh:"+JSON.stringify(data));
                if ((typeof (isCloud) != "undefined" && isCloud == false) || isUsingNativeGetAndPost == true) {
                    var successCallback = function (result) {
                        var ret = eval("(" + result + ")");
                        console.info("success:"+JSON.stringify(ret));
                        var flag = ret.success;
                        var msg = ret.msg;
                        var obj = ret.obj;

                        if (typeof (flag) == 'undefined') {
                            //没有错误检查机制
                            success({"returnValue": ret});
                            return;
                        }

                        var msgType = ret.msgType;
                        showMsg(flag, msgType, msg);
                        //if (!flag) {
                        //    app.hint(msg);
                        //}
                        success({"returnValue": obj});

                    };
                    var failCallback = function (result) {
                        console.error("error:"+JSON.stringify(result));
                        checkFailCallback(result);
                        fail({"returnValue": result});
                    };
                    $.ajax({
                        url: url,
                        data: data,
                        async: app.utils.toJSON(data).async, // || true,
                        timeout: timeout,
                        type: "POST",
                        success: successCallback,
                        error: failCallback
                    });
                }
                else {
                    var successCallback = function (result) {
                        var ret = eval("(" + result.returnValue + ")");
                        console.info("success:"+JSON.stringify(ret));
                        //alert(JSON.stringify(ret));
                        var flag = ret.success;
                        var msg = ret.msg;
                        var obj = ret.obj;

                        if (typeof (flag) == 'undefined') {
                            //没有错误检查机制
//                    app.hint("flag is null.");
                            success({"returnValue": ret});
                            return;
                        }
                        if(typeof(obj) !="undefined"  && JSON.stringify(obj).indexOf("<!DOCTYPE") != -1 && JSON.stringify(obj).indexOf("登录页面") != -1){//这表示token有问题，需要重新获取token
                            app.alert("平台TOKEN服务验证出错，请联系运维人员！");
                        }
                        var msgType = ret.msgType;
                        showMsg(flag, msgType, msg);
                        //if (!flag) {
                        //    app.hint(msg);
                        //}
                        success({"returnValue": obj});
                    };
                    var failCallback = function (result) {
                        console.error("error:"+JSON.stringify(result));
                        //检测错误返回，进行统一提示。
                        checkFailCallback(result);
                        fail(result);
                    };

                    app.ajax({
                        "url": url,
                        "data": data,
                        "timeout": timeout,
                        "method": "POST",
                        // application/json
                        "contentType": "application/x-www-form-urlencoded",
                        "success": successCallback,
                        "fail": failCallback,
                        "async": app.utils.toJSON(data).async
                    });
                }
        }
    }

    /*
     * 错误反馈，统一对用户进行提示
     * */
    function checkFailCallback(result) {
//      alert(JSON.stringify(result));
        var errorCode = result.code;
        if (errorCode == 404) {
            app.hint("你所访问的接口不存在！");
        } else if (errorCode == 0) {
            ret = "" + result.returnValue;
            if (ret.indexOf("SocketTimeoutException") >= 0) {
                app.hint("网络连接超时！");
            } else if (ret.indexOf("ConnectException") >= 0) {
                app.hint("网络连接失败！");
            } else {
                //其他连接失败错误
                app.hint("其他网络错误！");
            }
        } else {
            var ret = eval("(" + result.returnValue + ")");
            if (ret.success == false) {
                // app.hint(ret.msg);
            } else {
                //未知错误
                app.hint("其他连接错误！");
            }
        }
    }

    //2014-12-18 yintaiyuan为了解决timeout失效的问题，暂时改为调用$.ajax，设置超时为30秒
    //发送GET请求
    xh.get3 = function (url, data, success, fail, isCloud) {
        if (typeof (userLoginId) == "undefined" || typeof (deviceId) == "undefined" ||
            typeof (meid) == "undefined" || typeof (imsi) == "undefined" ||
            typeof (app_device) == "undefined") {
            getInfos();
        }

        data.APP_DEVICE = JSON.stringify(app_device);
        data.APP_NAME = APP_NAME;
        data.APP_VERSION = APP_VERSION;
        data.SERVER_CHARSET = SERVER_CHARSET;
        data.PostOrGet = "GET";     //端口转发的时候生效

        var timeout = data.timeout;
        if (typeof (timeout) == "undefined" || timeout == "") {
            timeout = 30000;
        }

        if (isUsingXhServer) {
//            var header = url.substring(0, jmt_url.length);
//            if (header == jmt_url) {
//                var tmp = url.substring(jmt_url.length);
//                url = ga_url + tmp;
//            }

            //get请求，必须要用door_url转发，但是不能直接调用door_url，需要用9999接口转到door_url，外加PostOrGet参数
            //这个时候APP_URL一定要写，并且写对。
            //王曦，2013-11-16
            data.forwardUrl = door_url;//url;
            url = XH_URL;
        }
        //alert("url :" + url + ",params :" + JSON.stringify(data));

        if ((typeof (isCloud) != "undefined" && isCloud == false) || isUsingNativeGetAndPost == true) {
            var successCallback = function (result) {
                var ret = eval("(" + result + ")");
                var flag = ret.success;
                var msg = ret.msg;
                var obj = ret.obj;
                if (typeof (flag) == 'undefined') {
                    //没有错误检查机制
                    success({"returnValue": ret});
                    return;
                }

                var msgType = ret.msgType;
                showMsg(flag, msgType, msg);
                success({"returnValue": obj});

//                success({"returnValue":result});
            };
            var failCallback = function (result) {
                fail({"returnValue": result});
            };
            $.ajax({
                url: url,
                data: data,
                async: app.utils.toJSON(data).async || true,
                timeout: timeout,
                type: "GET",
                method: "GET",
                success: successCallback,
                error: failCallback
            });
        }
        else {
            var successCallback = function (result) {
                var ret = eval("(" + result.returnValue + ")");
                //alert(JSON.stringify(ret));
                var flag = ret.success;
                var msg = ret.msg;
                var obj = ret.obj;
//                app.hint(flag);
                if (typeof (flag) == 'undefined') {
                    //没有错误检查机制
//                    app.hint("flag is null.");
                    success({"returnValue": ret});
                    return;
                }

                var msgType = ret.msgType;
                showMsg(flag, msgType, msg);
                //if (!flag) {
                //    app.hint(msg);
                //}
                success({"returnValue": obj});

//                success(result);
            };
            var failCallback = function (result) {
                //检测错误返回，进行统一提示。
                checkFailCallback(result);
                fail(result);
            };

            app.ajax({
                "url": url,
                "data": data,
                "method": "GET",
                "type": "GET",
                "success": successCallback,
                "fail": failCallback,
                "async": app.utils.toJSON(data).async,
                "timeout": timeout
            });
        }
    }
    //2014-12-25 lxt修改新的接口，全部转向新的接口，只经过一次转发，不再使用door_url
    /**
     * 参数说明：
     *  * 1.PostOrGet 向服务方接口请求使用的方式（公安网）默认POST(暂时只支持GET/POST)
     * 2.REQUEST_TYPE 向服务方接口请求使用的方式（移动信息网）默认POST(暂时只支持GET/POST)
     */
        //发送GET请求
    xh.get = function (url, data, success, fail, isCloud) {
        data.PostOrGet = "GET";
        xh.post(url, data, success, fail, isCloud);
    }


    //一般不要使用，使用之后会没有日志记录。
    xh.get2 = function (url, data, success, fail, isCloud) {
        if (typeof (userLoginId) == "undefined" || typeof (deviceId) == "undefined" ||
            typeof (meid) == "undefined" || typeof (imsi) == "undefined" ||
            typeof (app_device) == "undefined") {
            getInfos();
        }


        data.APP_DEVICE = JSON.stringify(app_device);
        data.APP_NAME = APP_NAME;
        data.APP_VERSION = APP_VERSION;
        data.SERVER_CHARSET = SERVER_CHARSET;
        data.PostOrGet = "GET";     //端口转发的时候生效

        var timeout = data.timeout;
        if (typeof (timeout) == "undefined" || timeout == "") {
            timeout = 30000;
        }

        //alert("url :" + url + ",params :" + JSON.stringify(data));

        if ((typeof (isCloud) != "undefined" && isCloud == false) || isUsingNativeGetAndPost == true) {
            var successCallback = function (result) {

                success({"returnValue": result});
            };
            var failCallback = function (result) {
                fail({"returnValue": result});
            };
            $.ajax({
                url: url,
                data: data,
                async: app.utils.toJSON(data).async || true,
                timeout: timeout,
                type: "GET",
                method: "GET",
                success: successCallback,
                error: failCallback
            });
        }
        else {
            var successCallback = function (result) {

                success(result);
            };
            var failCallback = function (result) {
                //检测错误返回，进行统一提示。
                checkFailCallback(result);
                fail(result);
            };

            app.ajax({
                "url": url,
                "data": data,
                "method": "GET",
                "type": "GET",
                "success": successCallback,
                "fail": failCallback,
                "async": app.utils.toJSON(data).async,
                "timeout": timeout
            });
        }
    }

    //日期格式化
    //示例：var endDate = new Date().format('yyyy-M-d');
    Date.prototype.format = function (format) {
        var date = {
            "M+": this.getMonth() + 1,
            "d+": this.getDate(),
            "h+": this.getHours(),
            "m+": this.getMinutes(),
            "s+": this.getSeconds(),
            "q+": Math.floor((this.getMonth() + 3) / 3),
            "S+": this.getMilliseconds()
        };
        if (/(y+)/i.test(format)) {
            format = format.replace(RegExp.$1, (this.getFullYear() + '')
                .substr(4 - RegExp.$1.length));
        }
        for (var k in date) {
            if (new RegExp("(" + k + ")").test(format)) {
                format = format.replace(RegExp.$1,
                    RegExp.$1.length == 1 ? date[k] : ("00" + date[k])
                        .substr(("" + date[k]).length));
            }
        }
        return format;
    };

    //获取随机数
    xh.getRandomNum = function (Min, Max) {
        var Range = Max - Min;
        var Rand = Math.random();
        return (Min + Math.round(Rand * Range));
    }

    //获取用户信息，加入了调试模式，可以干预loginId和userName的值
    xh.getLoginInfo = function (callback) {
        var isDebug = localStorage.getItem("xh_debug");
        if (typeof (isDebug) != 'undefined' && isDebug == 'yes') {

            var loginId = localStorage.getItem("xh_user_id");
            var userName = localStorage.getItem("xh_user_name");
            var userId = localStorage.getItem("xh_uuid");
            var userType =localStorage.getItem("xh_user_type");
            var userCode =localStorage.getItem("xh_user_code");
            var deptId = localStorage.getItem("xh_dept_id");
            var deptCode =localStorage.getItem("xh_dept_code");
            var idCard =localStorage.getItem("xh_id_card");
            var ret = {
                userId: userId,
                orgId: deptId,
                /*orgName: deptName,*/
                orgCode:deptCode,
                loginId: loginId,
                userName: userName,
                userCode:userCode,
                userType:userType,
                idCard:idCard
            };
            var successCallback = function (result) {
                callback(ret);
            };
            app.link.getLoginInfo(successCallback);
        } else {
            app.link.getLoginInfo(callback);
        }
    }


    /**
     该接口用于调用二维码扫描
     @method app.barcode.scan
     @static
     @param success 成功回调方法
     @param fail 失败回调方法
     @example
     app.barcode.scan(function(result) {
				app.alert(result)
			}, function(result) {
				app.alert(result)
			});
     */
    xh.scan = function (success, fail) {
        Cordova.exec(success, fail, "BarcodeScanner", "scan", []);
    }

    /**
     该接口用于规范化日期选择控件的输出格式，将选择之后的日期格式规范为YYYY-MM-DD
     @param id: 控件标签在document结构中的element id
     callback: function(dateText) {
                    // TO DO
                    // 选择之后的回调函数，参数为选择的日期字符串YYYY-MM-DD
                }
     @example
     html:
     <div data-role="BTSelect" id="myDate"><span>请选择</span></div>
     js:
     $("#myDate").tap(function(){
                xh.selectDate("myDate");
            });
     */
    xh.selectDate = function (id, callback) {
        var def = null;
        var curr = $("#" + id).attr("value");
        if (curr != null) {
            var array = curr.split("-");
            var year = array[0];
            var month = array[1];
            var day = array[2];
            def = {year: year, month: month, day: day};
        }

        app.dateTimePicker.selectDate(function (result) {
            //alert(JSON.stringify(result));
            if (result.month < 10) {
                result.month = "0" + result.month;
            }
            if (result.day < 10) {
                result.day = "0" + result.day;
            }
            var full = result.year + "-" + result.month + "-" + result.day;
            $("#" + id).attr("value", full);
            $("#" + id + " span").html(full);

            if (callback != undefined && callback != null) {
                callback(full);
            }
        }, def, 'yyyy MM dd');
    }

    /*
     该接口仅处理日期选择后的格式化输出，不作界面处理
     */
    xh.selectDate2 = function (date, callback) {
        var def = null;
        if (date != null && date != "") {
            var array = date.split("-");
            var year = array[0];
            var month = array[1];
            var day = array[2];
            def = {year: year, month: month, day: day};
        }

        app.dateTimePicker.selectDate(function (result) {
            if (result.month < 10) {
                result.month = "0" + result.month;
            }
            if (result.day < 10) {
                result.day = "0" + result.day;
            }
            var full = result.year + "-" + result.month + "-" + result.day;

            if (callback != undefined && callback != null) {
                callback(full, result.year, result.month, result.day);
            }
        }, def, 'yyyy MM dd');
    }

    /**
     该接口用于规范化日期选择控件的输出格式，将选择之后的日期格式规范为hh:mm
     @param id 控件标签在document结构中的element id
     callback: function(timeText) {
                    // TO DO
                    // 选择之后的回调函数，参数为选择的日期字符串hh:mm
                }
     @example
     html:
     <div data-role="BTSelect" id="myTime"><span>请选择</span></div>
     js:
     $("#myTime").tap(function(){
            xh.selectTime("myTime");
        });
     */
    xh.selectTime = function (id, callback) {
        var def = null;
        var curr = $("#" + id).attr("value");
        if (curr != null) {
            var array = curr.split(":");
            var hour = array[0];
            var minute = array[1];
            def = {hour: hour, minute: minute};
        }
        app.dateTimePicker.selectTime(function (result) {
            //alert(JSON.stringify(result));
            if (result.hour < 10) {
                result.hour = "0" + result.hour;
            }
            if (result.minute < 10) {
                result.minute = "0" + result.minute;
            }

            var full = result.hour + ":" + result.minute;
            $("#" + id).attr("value", full);
            $("#" + id + " span").html(full);

            if (callback != undefined && callback != null) {
                callback(full);
            }
        }, def, 'hh mm');
    }

    /*
     该接口仅用于处理时间选择后的格式化输出，不作任何界面处理
     */
    xh.selectTime2 = function (time, callback) {
        var def = null;
        if (time != null && time != "") {
            var array = time.split(":");
            var hour = array[0];
            var minute = array[1];
            def = {hour: hour, minute: minute};
        }

        app.dateTimePicker.selectTime(function (result) {
            if (result.hour < 10) {
                result.hour = "0" + result.hour;
            }
            if (result.minute < 10) {
                result.minute = "0" + result.minute;
            }

            var full = result.hour + ":" + result.minute;

            if (callback != undefined && callback != null) {
                callback(full, result.hour, result.minute);
            }
        }, def, 'hh mm');
    }

    /**
     比较两个日期的先后顺序
     @param startDate 日期信息1，格式为YYYY-MM-DD
     @param endDate 日期信息2，格式为YYYY-MM-DD
     @return -1: 日期1早于日期2
     0: 日期1和日期2为同一天
     1: 日期1晚于日期2
     @example
     xh.compareDate("1999-01-02", "1991-01-01") = 1
     */
    xh.compareDate = function (startDate, endDate) {
        if (startDate == endDate) {
            return 0;
        }

        var arr1 = startDate.split("-");
        var arr2 = endDate.split("-");
        if (arr1[0] > arr2[0]) {
            //app.hint(endText + "不能早于" + startText);
            return 1;
        }

        if (arr1[0] < arr2[0]) {
            return -1;
        }

        if (arr1[0] == arr2[0] && arr1[1] > arr2[1]) {
            //app.hint(endText + "不能早于" + startText);
            return 1;
        }

        if (arr1[0] == arr2[0] && arr1[1] < arr2[1]) {
            return -1;
        }

        if (arr1[0] == arr2[0] && arr1[1] == arr2[1] && arr1[2] > arr2[2]) {
            //app.hint(endText + "不能早于" + startText);
            return 1;
        }

        return -1;
    }

    /**
     比较两个时间的先后顺序
     @param startTime 时间信息1，格式为hh:mm
     @param endTime 时间信息2，格式为hh:mm
     @return -1: 时间1早于时间2
     0: 时间1和时间2相等
     1: 时间1晚于时间2
     @example
     xh.compareTime("18:00", "18:01") = -1
     */
    xh.compareTime = function (startTime, endTime) {
        if (startTime == endTime) {
            return 0;
        }

        var arr1 = startTime.split(":");
        var arr2 = endTime.split(":");
        if (arr1[0] > arr2[0]) {
            //app.hint(endText + "不能早于" + startText);
            return 1;
        }

        if (arr1[0] == arr2[0] && arr1[1] >= arr2[1]) {
            //app.hint(endText + "不能早于" + startText);
            return 1;
        }

        return -1;
    }

    //发送POST请求,不转发，一般不要使用
    xh.post2 = function (url, data, success, fail, isCloud) {
        //当前登陆的LoginId(必须先引用linkplugins.js中的函数，如上app.link部分)

        if (typeof (userLoginId) == "undefined" || typeof (deviceId) == "undefined" ||
            typeof (meid) == "undefined" || typeof (imsi) == "undefined" ||
            typeof (app_device) == "undefined") {
            getInfos();
        }

        data.APP_DEVICE = JSON.stringify(app_device);
        data.APP_NAME = APP_NAME;
        data.APP_VERSION = APP_VERSION;
        data.SERVER_CHARSET = SERVER_CHARSET;

//        app.hint("post2");

        var timeout = data.timeout;
        if (typeof (timeout) == "undefined" || timeout == "") {
            timeout = 30000;
        }

        if ((typeof (isCloud) != "undefined" && isCloud == false) || isUsingNativeGetAndPost == true) {
            var successCallback = function (result) {
                success({"returnValue": result});
            };
            var failCallback = function (result) {
                fail({"returnValue": result});
            };
            $.ajax({
                url: url,
                data: data,
                async: app.utils.toJSON(data).async, // || true,
                timeout: timeout,
                type: "POST",
                success: successCallback,
                error: failCallback
            });
        }
        else {
            var successCallback = function (result) {
                success(result);
            };
            var failCallback = function (result) {
                //检测错误返回，进行统一提示。
                checkFailCallback(result);
                fail(result);
            };

            app.ajax({
                "url": url,
                "data": data,
                "timeout": timeout,
                "method": "POST",
                // application/json
                "contentType": "application/x-www-form-urlencoded",
                "success": successCallback,
                "fail": failCallback,
                "async": app.utils.toJSON(data).async
            });
        }
    }

    //调试的信息用xh.alert代替，isXhDebug = false则所有显示信息不显示。
    var isXhDebug = false;
    xh.alert = function (message, callback, title, buttonName) {

        if (!isXhDebug) {
            //调试模式关闭，不显示alert
            return;
        }
        app.alert(message, callback, title, buttonName);
    }

    //专网http://***********:9123/sso/user/slogin
    //xh.getToken = function (loginId, callback) {
    //    //callback(loginId);
    //    var xh_token_user_id = localStorage.getItem("xh_token_user_id");
    //    var xh_token_time = localStorage.getItem("xh_token_time");
    //    var xh_token_real_token = localStorage.getItem("xh_token_real_token");
    //    //是不是同一个人在调用这个token、
    //    if (loginId == xh_token_user_id && xh_token_real_token != undefined && xh_token_real_token.length > 0) {
    //        //是不是两个小时内调用
    //        if (xh_token_time != undefined) {
    //            var now_time = new Date();
    //            var token_time = new Date(xh_token_time);
    //            var s = (now_time.getTime() - token_time.getTime());//毫秒
    //            s = (s / (1000));
    //            if (s < 50 * 60) {
    //                callback({"returnValue": xh_token_real_token});
    //                return;
    //            }
    //        }
    //    }
    //    var params = {"userCode": loginId};
    //    xh.post("http://172.19.255.175/sso/user/slogin", params, function (res) {
    //        localStorage.setItem("xh_token_user_id", loginId);
    //        localStorage.setItem("xh_token_time", new Date());
    //        localStorage.setItem("xh_token_real_token", res.returnValue);
    //        callback(res);
    //    });
    //
    //    /*xh.post("http://172.29.3.76:9238/szga/xinghuo-apass-sso/user/slogin",params,function(res){
    //     alert(123)
    //     alert(JSON.stringify(res));
    //     localStorage.setItem("xh_token_user_id",loginId);
    //     localStorage.setItem("xh_token_time",new Date());
    //     localStorage.setItem("xh_token_real_token",res.returnValue);
    //     callback(res);
    //     });*/
    //};

    xh.getToken = function (loginId, callback) {

        var params = {"userCode":loginId};
        xh.post("http://172.19.255.175/sso/user/slogin",params,function(res){
            //localStorage.setItem("xh_token_user_id",loginId);
            //localStorage.setItem("xh_token_time",new Date());
            //localStorage.setItem("xh_token_real_token",res.returnValue);
            //res.returnValue = "111111";
            if(res !=  "undefined"){
                var token = res.returnValue  ;
                if(!checkIsRealToken(token)){
                    //alert("平台TOKEN服务获取失败，请联系运维人员!");
                    res.returnValue = "gettokenerror!";
                    callback(res);
                }
            }else{
                //alert("平台TOKEN服务获取失败，请联系运维人员!");
                res.returnValue = "gettokenerror!";
                callback(res);
            }
            //res.returnValue = "";
            callback(res);
        },function(error){
            callback(error);
            //alert(error);
        });
    }
    function checkIsRealToken(nubmer)
    {
        if("" ==nubmer){
            return false;
        }
        var re =  /^[0-9a-zA-Z]*$/g;  //判断字符串是否为数字和字母组合     //判断正整数 /^[1-9]+[0-9]*]*$/
        if (!re.test(nubmer))
        {
            return false;
        }else{
            return true;
        }
    }

    /* -------------------------------------东莞项目----------------------------------------*/

    /*
     添加人：吴日文
     修改时间：2018-1-02
     注释：东莞项目专网转发地址
     */
    var door_url_dg = "http://192.168.2.56:18080/supp/httpClient";

    /*
     添加人：吴日文
     修改时间：2018-1-02
     注释：文件上传，带上手机硬件参数,东莞项目文件上传方法
     */
    xh.uploadFile_dg = function (filePath, url, success, fail, options) {
        var fileTransfer = new FileTransfer();
        if (typeof (userLoginId) == "undefined" || typeof (deviceId) == "undefined" ||
            typeof (meid) == "undefined" || typeof (imsi) == "undefined" ||
            typeof (app_device) == "undefined") {
            getInfos();
        }

        url += "&APP_DEVICE=" + JSON.stringify(app_device);
        url += "&APP_NAME=" + APP_NAME;
        url += "&APP_VERSION=" + APP_VERSION;
        url += "&SERVER_CHARSET=" + SERVER_CHARSET;

//        var uri=encodeURI(url);

        var uri;
        uri = encodeURI(url); //地址转码

        var successCallback = function (result) {
            //app.alert(JSON.stringify(result));
            if (isUsingXhServer) {
                var ret = eval("(" + result.response + ")");
                var flag = ret.success;
                var msg = ret.msg;
                var obj = ret.obj;
                if (typeof (flag) == 'undefined') {
                    //没有错误检查机制
                    success({"response": ret});
                    return;
                }

                if (!flag) {
                    // app.hint(msg);
                }
                success({"response": obj});
            } else {
                success(result);
            }

//            success(result);
        };
        var failCallback = function (result) {
            //alert(JSON.stringify(result));
            //检测错误返回，进行统一提示。
            checkFailCallback(result);
            fail(result);
        };
        fileTransfer.upload(
            filePath,
            uri,
            successCallback,
            failCallback,
            options
        );
    }

    /*
     添加人：吴日文
     修改时间：2018-1-02
     注释：文件下载，带上手机硬件参数,东莞项目文件下载方法
     */
    xh.downloadFile_dg = function (filePath, url, success, fail) {
        var fileTransfer = new FileTransfer();

        if (typeof (userLoginId) == "undefined" || typeof (deviceId) == "undefined" ||
            typeof (meid) == "undefined" || typeof (imsi) == "undefined" ||
            typeof (app_device) == "undefined") {
            getInfos();
        }

        url += "&APP_DEVICE=" + JSON.stringify(app_device);
        url += "&APP_NAME=" + APP_NAME;
        url += "&APP_VERSION=" + APP_VERSION;
        url += "&SERVER_CHARSET=" + SERVER_CHARSET;

        var uri = encodeURI(url);

        var successCallback = function (result) {
            success(result);
        };
        var failCallback = function (result) {
            //检测错误返回，进行统一提示。
            checkFailCallback(result);
            fail(result);
        };

        fileTransfer.download(
            uri,
            filePath,
            successCallback,
            failCallback
        );
    }

    /*
     添加人：吴日文
     修改时间：2018-1-02
     注释：东莞项目专网是否转发方法
     */
    app.getUrl_dg = function (orgin_url) {
        if (isProxy == true) {
            return door_url_dg;
        } else {
            return orgin_url;
        }
    }

    //发送POST请求,不转发
    xh.post_dg = function (url, data, success, fail, isCloud) {
        //当前登陆的LoginId(必须先引用linkplugins.js中的函数，如上app.link部分)

        if (typeof (userLoginId) == "undefined" || typeof (deviceId) == "undefined" ||
            typeof (meid) == "undefined" || typeof (imsi) == "undefined" ||
            typeof (app_device) == "undefined") {
            getInfos();
        }

        data.APP_DEVICE = JSON.stringify(app_device);
        data.APP_NAME = APP_NAME;
        data.APP_VERSION = APP_VERSION;
        data.SERVER_CHARSET = SERVER_CHARSET;

//        app.hint("post2");

        var timeout = data.timeout;
        if (typeof (timeout) == "undefined" || timeout == "") {
            timeout = 30000;
        }

        if ((typeof (isCloud) != "undefined" && isCloud == false) || isUsingNativeGetAndPost == true) {
            var successCallback = function (result) {
                success({"returnValue": result});
            };
            var failCallback = function (result) {
                fail({"returnValue": result});
            };
            $.ajax({
                url: url,
                data: data,
                async: app.utils.toJSON(data).async, // || true,
                timeout: timeout,
                type: "POST",
                success: successCallback,
                error: failCallback
            });
        }
        else {
            var successCallback = function (result) {
                success(result);
            };
            var failCallback = function (result) {
                //检测错误返回，进行统一提示。
                checkFailCallback(result);
                fail(result);
            };

            app.ajax({
                "url": url,
                "data": data,
                "timeout": timeout,
                "method": "POST",
                // application/json
                "contentType": "application/x-www-form-urlencoded",
                "success": successCallback,
                "fail": failCallback,
                "async": app.utils.toJSON(data).async
            });
        }
    }
    /*
     * 切换正式版和测试版
     * status status为false为正式版，status为true为测试版
     * */
    xh.changeStatus = function (status) {
        if (true == status || false == status) {
            localStorage.setItem("XH_DEBUG_STATUS", status);
            XH_DEBUG_STATUS = localStorage.getItem("XH_DEBUG_STATUS");
            if (XH_DEBUG_STATUS == "true") {
                return true;
            } else {
                return false;
            }
            ;
        }
    }
    /*
     * 获取真实的url地址
     * */
    xh.getRealUrl = function (index) {
        XH_DEBUG_STATUS = localStorage.getItem("XH_DEBUG_STATUS");
        if (XH_DEBUG_STATUS == "true") {
            return url_test[index];
        } else {
            return url_formal[index];
        }
    }
    /*
     *判断是否为测试版 true为测试版
     * */
    xh.isDebugStatus = function () {
        XH_DEBUG_STATUS = localStorage.getItem("XH_DEBUG_STATUS");
        if (XH_DEBUG_STATUS == "true") {
            return true;
        } else {
            return false;
        }
    }

    /* -------------------------------------工具方法----------------------------------------*/
    /*
     *校验手机号码
     * */
    xh.checkPhoneNum = function (phoneNo) {
        if (phoneNo.length != 11) {
            app.hint("手机号码不足11位！");
            return false;
        }

        var myreg = /^1[3|4|5|6|7|8|9][0-9]\d{4,8}$/;
        if (!myreg.test(phoneNo)) {
            app.hint('无效的手机号码！');
            return false;
        }

        return true;
    }
    /*
     *校验身份证号码
     * */
    xh.checkIdcard = function (idNo) {
        if (typeof (idNo) == 'undefined' || idNo == "") {
            app.hint("请先查询被查人员身份证号码!");
            return false;
        }
        if (idNo.length != 15 && idNo.length != 18) {
            app.hint("身份证号应为15或18位数字，请正确输入后再进行查询");
            return false;
        }
        //var aCity={11:"北京",12:"天津",13:"河北",14:"山西",15:"内蒙古",21:"辽宁",22:"吉林",23:"黑龙江",31:"上海",
        //    32:"江苏",33:"浙江",34:"安徽",35:"福建",36:"江西",37:"山东",41:"河南",42:"湖北",43:"湖南",44:"广东",
        //    45:"广西",46:"海南",50:"重庆",51:"四川",52:"贵州",53:"云南",54:"西藏",61:"陕西",62:"甘肃",63:"青海",
        //    64:"宁夏",65:"新疆",71:"台湾",81:"香港",82:"澳门",91:"国外"}
        var reg = /^[1-9]{1}[0-9]{14}$|^[1-9]{1}[0-9]{16}([0-9]|[xX])$/;
        if (reg.test(idNo) == false) {
            app.hint("您输入的身份证格式有误，请核查后再输入");
            return false;
        }
        return true;
    }

    /**
     *开启定位
     */
    xh.startLocation = function () {
        app.szgaplugin.startLocation(function (res) {
            //alert(JSON.stringify(res))
            //if(res==1){
            //    alert("定位服务已经开启!");
            //}
        }, function (err) {
            alert(JSON.stringify(err));
        });
    }
    /**
     *获取定位信息
     */
    xh.getLocation = function (success, fail) {
        app.szgaplugin.getLocationInfo(function (res) {
            if (res != "") {
                //var start = new Date().getTime();
                xh.post("http://***********:9999/service/baiduLoc", {
                        cardNo: res,
                        APP_URL: "http://***********:9999/service/baiduLoc",
                        URL_TYPE: "JMT"
                    },
                    function (res) {
                        //alert("success after " +  " ms: " + res.returnValue);
                        //success(res.returnValue);
                        var data = eval("(" + res.returnValue + ")");
                        data = eval("(" + data.Location + ")");
                        var addr = getNormalAddress(data.content.addr);
                        data.content.addr = addr;
                        success(data);
                        xh_closeGps = data.closeGps;
                        //var point = data.content.point; //x:经度，y:纬度
                        //var accuracy = data.content.radius;
                        //var longitude = point.x;
                        //var latitude = point.y;
                        //alert(addr + "   "+ longitude + "   " + latitude);
                    },
                    function (res) {
                        //var end = new Date().getTime();
                        fail(res.returnValue);
                    });
            } else {
                success("获取信息失败!");
            }
        });
        //去除地址的后三位多余部分
        function getNormalAddress(mAddr) {
            if (typeof(mAddr) != "undefined") {
                var ret = "";
                var list = mAddr.split(",");
                if (list == "" || list.length == 0) {
                    return "";
                }

                for (var i = 0; i < list.length - 3; i++) {
                    ret += list[i];
                }
                return ret;
            } else {
                return mAddr;
            }
        }
    }
    /**
     * 关闭定位
     */
    xh.closeLocation = function () {
        if (!xh_closeGps) {
            return;
        }
        var params = {
            "closeWifi": true,
            "isEnable": true
        };
        app.szgaplugin.stopLocation(params, function (res) {
            alert(JSON.stringify(res));
        });

    }
//----------------------------------------------光明---------------------------------------------- --
    /**
     * 获取服务器地址
     * @param type   0:获取移动网地址, 1:获取公安网地址
     * @param callback
     */
    xh.getGatewayAddr = function (type, callback) {
        var onSuccess = function (res) {
             // res = "http://192.168.10.33:60080/ga/"; //江西
            callback(res);
        }
        var onError = function (err) {
            alert(JSON.stringify(err));
        }
        Cordova.exec(onSuccess, onError, "XhPaasPlugin", "getGatewayAddr", [type]);
    }
    /**
     * get请求
     * @param url
     * @param data
     * @param success
     * @param fail
     */
    xh.get_gm = function (url, data, success, fail) {
        //alert(JSON.stringify(url))
        var timeout = data.timeout;
        if (typeof (timeout) == "undefined" || timeout == "") {
            timeout = 30000;
        }
        var successCallback = function (result) {
            //alert(JSON.stringify(result))
            success({"returnValue": result})
        };
        var failCallback = function (result) {
            //alert(JSON.stringify("失败"))
            //alert(JSON.stringify(result))
            checkFailCallback(result);
            fail({"returnValue": result});
        };
        $.ajax({
            url: url,
            data: data,
            async: app.utils.toJSON(data).async, // || true,
            timeout: timeout,
            type: "GET",
            success: successCallback,
            error: failCallback
        });
    }
    /**
     * post请求
     * @param url
     * @param data
     * @param success
     * @param fail
     * @param before 设置了请求头
     * @param complete
     */
    xh.post_gm = function (url, data, success, fail, before, complete) {
        var timeout = data.timeout || 30 * 1000;
        var content_type = data.contentType || "application/json";
        var successCallback = function (result) {
            success({"returnValue": result});
        };
        var failCallback = function (result) {
            fail({"returnValue": result});
        };
        $.ajax({
            url: url,
            data: JSON.stringify(data),
            async: app.utils.toJSON(data).async, // || true,
            timeout: timeout,
            type: "POST",
            success: successCallback,
            error: failCallback,
            beforeSend: function (xhr) {
                before(xhr);
            },
            complete: complete,
            contentType: content_type
        });
    }

    /**
     * post请求--社区警务
     * @param url
     * @param data
     * @param success
     * @param fail
     * @param headers 设置了请求头
     * @param complete
     */
    xh.post_sqjw = function(url, data, success, fail, before, complete) {
        // alert(JSON.stringify(data));
        // alert(url);
        // alert(JSON.stringify(before));
        var timeout = data.timeout || 30 * 1000;
        var content_type = data.contentType || "application/json";
        var successCallback = function(result) {
            success({"returnValue": result.returnValue});
        };
        var failCallback = function(result) {
            fail({"returnValue":result.returnValue});
        };
        app.ajax({
            url : url,
            data : JSON.stringify(data),
            async : app.utils.toJSON(data).async, // || true,
            timeout : timeout,
            type : "POST",
            success : successCallback,
            error : failCallback,
            headers:before,
            complete : complete,
            contentType : content_type
        });
    }

    /**
     * 上传附件
     * @param filePath
     * @param url
     * @param success
     * @param fail
     * @param options
     */
    xh.uploadFile_gm = function (filePath, url, success, fail, options) {

        var fileTransfer = new FileTransfer();
        var successCallback = function (result) {
            success(result);
        };
        var failCallback = function (result) {
            //检测错误返回，进行统一提示。
            checkFailCallback(result);
            fail(result);
        };
        fileTransfer.upload(
            filePath,
            url,
            successCallback,
            failCallback,
            options
        );
    }

    /**
     * 下载文件
     * @param filePath
     * @param url
     * @param success
     * @param fail
     */
    xh.downloadFile_gm = function (filePath, url, success, fail, _boolean, option) {
        var fileTransfer = new FileTransfer();
        var uri = encodeURI(url);
        var successCallback = function (result) {
            success(result);
        };
        var failCallback = function (result) {
            //检测错误返回，进行统一提示。
            fail(result);
        };
        fileTransfer.download(
            uri,
            filePath,
            successCallback,
            failCallback,
            _boolean,
            option
        );
    }

    /**
     * 获取用户名和警号的水印
     */
    xh.getWaterMark =function (){
        var watermark = function (settings) {
            //默认设置
            var defaultSettings = {
                watermark_txt0: "111",
                watermark_txt1: "222",
                watermark_txt2: "33",//根据本业务需求  有三行 换行显示  创建三个节点
                watermark_x: 20,//水印起始位置x轴坐标
                watermark_y: 70,//水印起始位置Y轴坐标
                watermark_rows: 20,//水印行数
                watermark_cols: 2,//水印列数
                watermark_x_space: 70,//水印x轴间隔
                watermark_y_space: 70,//水印y轴间隔
                watermark_color: '#aaa',//水印字体颜色
                watermark_alpha: 0.2,//水印透明度
                watermark_fontsize: '20px',//水印字体大小
                watermark_font: '微软雅黑',//水印字体
                watermark_width: 220,//水印宽度
                watermark_height: 80,//水印长度
                watermark_angle: 25//水印倾斜度数
            };
            //采用配置项替换默认值，作用类似jquery.extend
            if (arguments.length === 1 && typeof arguments[0] === "object") {
                var src = arguments[0] || {};
                for (key in src) {
                    if (src[key] && defaultSettings[key] && src[key] === defaultSettings[key])
                        continue;
                    else if (src[key])
                        defaultSettings[key] = src[key];
                }
            }
            var oTemp = document.createDocumentFragment();
            //获取页面最大宽度
            var page_width = Math.max(document.body.scrollWidth, document.body.clientWidth);
            var cutWidth = page_width * 0.0150;
            var page_width = page_width - cutWidth;
            //获取页面最大高度
            var page_height = Math.max(document.body.scrollHeight, document.body.clientHeight) + 450;
            // var page_height = document.body.scrollHeight+document.body.scrollTop;
            //如果将水印列数设置为0，或水印列数设置过大，超过页面最大宽度，则重新计算水印列数和水印x轴间隔
            if (defaultSettings.watermark_cols == 0 || (parseInt(defaultSettings.watermark_x + defaultSettings.watermark_width * defaultSettings.watermark_cols + defaultSettings.watermark_x_space * (defaultSettings.watermark_cols - 1)) > page_width)) {
                defaultSettings.watermark_cols = parseInt((page_width - defaultSettings.watermark_x + defaultSettings.watermark_x_space) / (defaultSettings.watermark_width + defaultSettings.watermark_x_space));
                defaultSettings.watermark_x_space = parseInt((page_width - defaultSettings.watermark_x - defaultSettings.watermark_width * defaultSettings.watermark_cols) / (defaultSettings.watermark_cols - 1));
            }
            //如果将水印行数设置为0，或水印行数设置过大，超过页面最大长度，则重新计算水印行数和水印y轴间隔
            if (defaultSettings.watermark_rows == 0 || (parseInt(defaultSettings.watermark_y + defaultSettings.watermark_height * defaultSettings.watermark_rows + defaultSettings.watermark_y_space * (defaultSettings.watermark_rows - 1)) > page_height)) {
                defaultSettings.watermark_rows = parseInt((defaultSettings.watermark_y_space + page_height - defaultSettings.watermark_y) / (defaultSettings.watermark_height + defaultSettings.watermark_y_space));
                defaultSettings.watermark_y_space = parseInt(((page_height - defaultSettings.watermark_y) - defaultSettings.watermark_height * defaultSettings.watermark_rows) / (defaultSettings.watermark_rows - 1));
            }
            var x;
            var y;
            for (var i = 0; i < defaultSettings.watermark_rows; i++) {
                y = defaultSettings.watermark_y + (defaultSettings.watermark_y_space + defaultSettings.watermark_height) * i;
                for (var j = 0; j < defaultSettings.watermark_cols; j++) {
                    x = defaultSettings.watermark_x + (defaultSettings.watermark_width + defaultSettings.watermark_x_space) * j;

                    var mask_div = document.createElement('div');
                    mask_div.id = 'mask_div' + i + j;
                    mask_div.className = 'mask_div';
                    ///三个节点
                    var span0 = document.createElement('div');
                    span0.appendChild(document.createTextNode(defaultSettings.watermark_txt0));
                    var span1 = document.createElement('div');
                    span1.appendChild(document.createTextNode(defaultSettings.watermark_txt1));
                    var span2 = document.createElement('div');
                    span2.appendChild(document.createTextNode(defaultSettings.watermark_txt2));
                    mask_div.appendChild(span0);
                    mask_div.appendChild(span1);
                    mask_div.appendChild(span2);
                    //设置水印div倾斜显示
                    mask_div.style.webkitTransform = "rotate(-" + defaultSettings.watermark_angle + "deg)";
                    mask_div.style.MozTransform = "rotate(-" + defaultSettings.watermark_angle + "deg)";
                    mask_div.style.msTransform = "rotate(-" + defaultSettings.watermark_angle + "deg)";
                    mask_div.style.OTransform = "rotate(-" + defaultSettings.watermark_angle + "deg)";
                    mask_div.style.transform = "rotate(-" + defaultSettings.watermark_angle + "deg)";
                    mask_div.style.visibility = "";
                    mask_div.style.position = "absolute";
                    mask_div.style.left = x + 'px';
                    mask_div.style.top = y + 'px';
                    mask_div.style.overflow = "hidden";
                    mask_div.style.zIndex = "9999";
                    mask_div.style.pointerEvents = 'none';//pointer-events:none  让水印不遮挡页面的点击事件
                    //mask_div.style.border="solid #eee 1px";
                    mask_div.style.opacity = defaultSettings.watermark_alpha;
                    mask_div.style.fontSize = defaultSettings.watermark_fontsize;
                    mask_div.style.fontFamily = defaultSettings.watermark_font;
                    mask_div.style.color = defaultSettings.watermark_color;
                    mask_div.style.textAlign = "center";
                    mask_div.style.width = defaultSettings.watermark_width + 'px';
                    mask_div.style.height = defaultSettings.watermark_height + 'px';
                    mask_div.style.display = "block";
                    oTemp.appendChild(mask_div);
                }
                ;
            }
            ;
            document.body.appendChild(oTemp);
        };
        xh.getLoginInfo(function(result){
            var userName = result.userName?result.userName :"";
            var loginId = result.loginId?result.loginId:"";
            watermark({
                watermark_txt0: userName,
                watermark_txt1: loginId
            })
        });
    }

})(window);