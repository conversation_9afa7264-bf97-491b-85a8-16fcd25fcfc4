<!DOCTYPE HTML>
<html lang="en-US">
<head>
    <meta http-equiv="Content-Type" content="text/html;charset=UTF-8" />
    <meta name="format-detection" content="telephone=no" />
    <meta name="viewport" content="width=480,user-scalable=no" />
    <script src=../../js/vconsole.min.js></script>
    <!--CSS-->
    <link rel="stylesheet" href="../../frame3/css/bingotouch.css" />
    <!-- 应用样式 -->
    <link rel="stylesheet" href="../../frame3/css/app.css" />


    <!-- frame 3.0 JS-->
    <script src="../../frame3/js/cordova.js"></script>
    <script src="../../frame3/js/zepto.js"></script>
    <script src="../../frame3/js/iscroll.js"></script>
    <script src="../../frame3/js/baiduTemplate.js"></script>
    <script src="../../frame3/js/bingotouch.js"></script>
    <script src="../../frame3/js/require.js"></script>

    <!-- 应用脚本 -->
    <script src="../../frame3/js/plugin/linkplugins.js"></script>
    <script src="../../frame3/js/app/app.js"></script>

    <!--old js-->
    <script src="../../js/xh_public.js"></script>
    <script src="../../js/SzgaPlugin.js"></script>

    <!--引用url定义文件-->
    <script src="url.js" type="text/javascript"></script>
    <style type="text/css">

    </style>
    <title></title>

    <style type="text/css">
        body {
            height: auto;
            overflow: scroll;
        }
        
        
        
    </style>
    <script type="text/javascript">
        var userInfo;
        var deptCode;
        var terminalId;
        var access_token;
        var exitFlag = true;
        var appDetail;
        app.page.onReady = function() {

        };

        app.page.onLoad = function() {
            document.addEventListener("deviceready", function() {
                document.addEventListener("backbutton", function() {
                    app.back();
                }, false);
            }, false);


            shuiyin();
            app.getPageParams(function(result){
                appDetail = result.app_detail;

                console.log("detail:::" + JSON.stringify(appDetail));
                xh.getLoginInfo(function (result) {
                    userInfo = result;
                    app.link.getUserInfo(function (res) {
                        deptCode = res.deptCode;
                        //alert(JSON.stringify(res));

                        app.getMeid(function(result){
                            terminalId = result;

                            //ui.showMask("请稍候...");
                            xh.getToken(userInfo.loginId,function(res) {
                                access_token = res.returnValue;
                                renderDetail();
                            })
                        });
                    }, function (res) {
                        console.log(res);
                    }, userInfo.userId);
                });
            });
            
        };

       
        app.page.onError = function(e) {
            console.error(JSON.stringify(e));
            //alert(JSON.stringify(e));
        };
        

        function renderDetail() {
            var bt = baidu.template;
            
            var html = bt("detail", appDetail);

            $("#app_detail").html(html);
            $("#app_detail").uiwidget();


            var bottomHtml = bt("button", appDetail);

            $("#bottom_button").html(bottomHtml);
            $("#bottom_button").uiwidget();
        }

        function confirmUninstallApp(appId) {
            ui.confirm("提示", "是否卸载App?",function() {
                        
                uninstallApp(appId);   
            }, function() {
                        
            });
        }
        function uninstallApp(appId) {


            ui.showMask("正在加载...");
            console.log(userInfo);
            xh.post_jw(mpass_url + "/xinghuo-apaas-appservice/appservice/mobile/app/installOrUninstallApp",{
                userCode: userInfo.userCode,
                accessToken: access_token,
                appIds: appId,
                oprFlag: 0,
                contentType: "application/json"
            },function (res) {
                ui.hideMask();
                console.log(res);
                removeItem(appId);//保存下载时的版本信息
                app.hint("卸载成功");
                appDetail.isInstall = 0;
                renderDetail();
            },function (e) {
                ui.hideMask();
                console.log(e);
            },function (header) {
                header.setRequestHeader("accessToken",access_token);
                header.setRequestHeader("requestType","app");
                header.setRequestHeader("applyID",applyId);
                header.setRequestHeader("secretKey",secretKey);
                header.setRequestHeader("userId",userInfo.userId);
                header.setRequestHeader("userCode",userInfo.userCode);
                header.setRequestHeader("terminalId",terminalId);
            })
        }

        function downloadApp(params) {
            console.log(userInfo);
            ui.showMask();
            xh.post_jw(mpass_url + "/xinghuo-apaas-appservice/appservice/mobile/app/installOrUninstallApp",{
                userCode: userInfo.userCode,
                accessToken: access_token,
                appIds: params.appId,
                oprFlag: 1,
                contentType: "application/json"
            },function (res) {
                ui.hideMask();
                console.log(res);
                appDetail.isInstall = 1;
                renderDetail();
                setLocalItem(params.appId,params.version);
                if (params.appUrl.includes("http")) {
                    var url = params.appUrl +
                            "?imid=" + userInfo.userCode +
                            "&accessToken=" + access_token;
                    console.log(url);
                    loadWebView(params.appUrl);
                } else {
                        

                    if (DEBUG) {
                        console.log("打开应用参数：" +JSON.stringify(params));
                    }
                        
                    openH5(params);
                }
            },function (e) {
                ui.hideMask();
                console.log(e);
            },function (header) {
                header.setRequestHeader("accessToken",access_token);
                header.setRequestHeader("requestType","app");
                header.setRequestHeader("applyID",applyId);
                header.setRequestHeader("secretKey",secretKey);
                header.setRequestHeader("userId",userInfo.userId);
                header.setRequestHeader("userCode",userInfo.userCode);
                header.setRequestHeader("terminalId",terminalId);
            })
        }

        function openApp(appId, entry, appName,md5,enclosure,version,appUrl,isInstalled,activityName,ip) {
            if (exitFlag) {
                exitFlag = false;
                setTimeout("exitFlag = true;", 1000);
                /*var params = {
                    appId: appId,
                    entry: entry,
                    query: query,
                    appName: appName
                };

                newPage(params);*/

                var params = {
                    appId: appId,
                    enclosure: enclosure,
                    md5: md5,
                    appUrl: appUrl,
                    version: version,
                    appEntry: entry,
                    activityName: activityName,
                    ip: mpass_url
                };      
                

                console.log("isInstall = " + isInstalled);
                console.log( JSON.stringify(params));

                var localVersion = getLocalItem(appId);
                if (DEBUG) {
                    console.log("localVersion = " + localVersion);
                    console.log("shouldDownload = " + (localVersion != version || isInstalled != 1 || localVersion == null || localVersion == 'undefined'));

                }
                if (localVersion != version || isInstalled != 1 || localVersion == null || localVersion == 'undefined') {
                    if (DEBUG) {
                        console.log("appUrl:" + appUrl);
                        console.log(params);
                    }
                    if (appUrl.includes("http")) { 
                        downloadApp(params);
                    } else {
                        //
                        downloadZip(params,function(e){

                            downloadApp(params);
                        });
                    }
                } else {

                    if (appUrl.includes("http")) {
                        var url = appUrl +
                            "?imid=" + userInfo.userCode +
                            "&accessToken=" + access_token;
                        console.log(url);
                        loadWebView(url);
                    } else {
                        

                        if (DEBUG) {
                            console.log("打开应用参数：" +JSON.stringify(params));
                        }
                        
                        openH5(params);
                    }
                }
            }
        }
        function loadWebView(url, params) {

        
            var successCallback = function (res) {
                console.log(res);
            };
            var failureCallback = function (res) {
                console.log(res);
            };
            Cordova.exec(successCallback, failureCallback, "Interactive", "loadWebView", [url, params]);
        }


        function downloadZip(params, callback) {
            var callback = callback || function () {};
            var successCallback = function (result) {
                console.log(result);
                
            
                callback(result);
            };

            var failureCallback = function (result) {
                console.log(result);
                
            };
            console.log(params);

            Cordova.exec(successCallback, failureCallback, "Interactive", "downloadZip", [params]);
        }

        function newPage(params) {
            params = params || {};

            console.log(params);

            console.log([params.appId, params.entry, params.query, params.appName]);

            Cordova.exec(null, null, "Page", "newPage", [
                params.appId,
                params.entry,
                params.query,
                params.appName
            ]);
        }
        /**
         * desc openH5
         * @param {*} 打开h5应用参数(appId,enclosure,md5,version,appEntry,activityName,ip)
         * @param {*} 可传空  
         * @param {*} callback 
         */
        function openH5(param1, param2, callback) {
          var callback = callback || function () {};
          var successCallback = function (res) {
            callback(res);
          };
          Cordova.exec(successCallback, null, "Interactive", "openH5", [param1, param2]);
        }
        
        function onInput (event) {
            //alert ("The new content: " + event.target.value);
            $("#appList").empty();
            var key_value = event.target.value;

            if(key_value != "" && key_value != null){
                var apps = [];
                for (var i=0;i<appList.length;i++) {
                    var appName = appList[i].appName;

                    if (appName.includes(key_value)){
                        apps.push(appList[i]);
                    }
                }
                if (apps.length > 0) {
                    renderApps(apps);
                } else {
                    $("#appList").empty();
                }
            } else {
                $("#appList").empty();
            }
        }
        
        function getLocalItem(key) {

            if (!window.localStorage) {
                alert("不支持local storage");
                return null;
            } else {
                var storage = window.localStorage;

                var value = storage.getItem(key);
            
            
                return value;
            }

            
        }

        function setLocalItem(key, value) {
            if (!window.localStorage) {
                alert("不支持local storage");
                return;
            } 
            window.localStorage.setItem(key, value);
        }

        function removeItem(key) {
            window.localStorage.removeItem(key);
        }


       

    </script>
    
    <script type="text/html" id="detail">
        <div style="display:flex;flex-direction: column;">
            <div class="container-fluid">
                <div class="row-fluid">
                    <div class="span3">
                        <img src="<%=appIcon%>" style="width: 100px;height: 100px;" />
                    </div>
                    <div class="span9" style="line-height: 0.7;">
                        <h2><%=appName%></h2>
                        <p>
                            版本：<span class="normal-text"><%=version%></span>
                        </p>
                        <p>发布日期：<span class="normal-text"><%=updateTime%></span></p>
                    </div>
                </div>
                <div style="height: 1px;background-color: gainsboro;"></div>
            </div>
            <div class="container-fluid">
                <div class="row-box">
                    <div class="span1" style="line-height: 1.2">
                        <h2>介绍</h2>
                        <p class="normal-text"><%=appIntro%></p>
                    </div>
                </div>

                <div class="row-box">
                    <div class="span1" style="line-height: 1.2">
                        <h2>应用描述</h2>
                        <p class="normal-text"><%=describe%></p>
                    </div>
                </div>

                <div class="row-box" style="margin-top: 10px;">
                    <div class="span1" style="line-height: 1.2">
                        <span style="font-weight: bold;">开发单位：</span>
                        <span class="normal-text"><%=createSupplier%></span>
                    </div>
                </div>
                <div style="height: 1px;background-color: gainsboro;margin: 0px 0;"></div>
            </div>
            
                
        </div>
    </script>
    <script type="text/html" id="button">
        <div style="display:flex;justify-content:space-around;align-items:flex-end;">
            <%if(isInstall==1){%>

            <div data-role="BTButton" data-theme="d" style="text-align: center;width:150px"
                onclick="confirmUninstallApp('<%=appId%>')">
                卸载
            </div>

            <div data-role="BTButton" data-theme="b" style="text-align: center;width:150px"
                onclick="openApp('<%=appId%>','<%=appEntry%>','<%=appName%>','<%=md5%>','<%=enclosure%>','<%=version%>','<%=appUrl%>','<%=isInstall%>','<%=activityName%>','<%=ip%>')">
                打开
            </div>
            <%}else{%>
            <div data-role="BTButton" data-theme="c" style="text-align: center;width:200px"
                onclick="openApp('<%=appId%>','<%=appEntry%>','<%=appName%>','<%=md5%>','<%=enclosure%>','<%=version%>','<%=appUrl%>','<%=isInstall%>','<%=activityName%>','<%=ip%>')">
                下载
            </div>
            <%}%>
        </div>   
    </script>
</head>
<body class="desktop">
<div id="section_container">
    <section id="app_search_section" class="active">
        <!--Header-->
        <div class="header" data-fixed="top">
            <div class="title row-box" style="border: none;">
                <div class="box-left" style="background:  #d93a49">
                    <div data-role="BTButton" data-type="image" onclick="app.back()">
                    <img src="../../css/images/icons/navicon/icon-back.png" alt=""/>
                    </div>
                </div>
                <div class="span1" style="background:  #d93a49">
                    <h1>应用详情</h1>
                </div>
                <div class="box-right" style="background: #d93a49">
                    
                    <div data-role="BTButton" data-type="image" onclick="app.refresh();">
                        <img src="../../css/images/icons/navicon/icon-refresh.png" alt=""/>
                    </div>
                </div>
                
            </div>

            
        </div>
        <!--Content-->
        <div class="content iscroll-wrapper" style="background:#FFFFFF;">
            
            <div id="app_detail"></div>
            
        </div>
        <div class="footer" data-fixed="bottom" style="padding:15px;">

            <div id="bottom_button"></div>

        </div>
        
    </section>
</div>
</body>
</html>