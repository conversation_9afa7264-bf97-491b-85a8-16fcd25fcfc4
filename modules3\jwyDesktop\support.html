﻿<!DOCTYPE html>
<html lang="en-US">
<head>
    <meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/>
    <meta name="format-detection" content="telephone=no"/>
    <meta name="viewport" content="width=480,user-scalable=no"/>
    <!--CSS-->
    <link rel="stylesheet" href="../../frame3/css/bingotouch.css"/>
    <link rel="stylesheet" href="../../frame3/css/app.css"/>

    <!--JS-->
    <script src="../../frame3/js/cordova.js"></script>
    <script src="../../frame3/js/zepto.js"></script>
    <script src="../../frame3/js/iscroll.js"></script>
    <script src="../../frame3/js/baiduTemplate.js"></script>
    <script src="../../frame3/js/bingotouch.js"></script>
    <!--<script src="../../frame3/js/require.js"></script>-->
    <script src="../../frame3/js/plugin/linkplugins.js"></script>
    <script src="../../frame3/js/app/app.js"></script>

    <link rel="stylesheet" type="text/css" href="css/normalize.css" />
    <link rel="stylesheet" type="text/css" href="fonts/font-awesome-4.3.0/css/font-awesome.min.css" />
    <link rel="stylesheet" type="text/css" href="css/default.css" />
    <link rel="stylesheet" type="text/css" href="css/component.css" />
    <script src="js/modernizr-custom.js"></script>

    <script src="js/classie.js"></script>
    <script>
        // http://stackoverflow.com/a/11381730/989439
        function mobilecheck() {
            var check = false;
            (function(a){if(/(android|ipad|playbook|silk|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows (ce|phone)|xda|xiino/i.test(a)||/1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s\-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|\-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw\-(n|u)|c55\/|capi|ccwa|cdm\-|cell|chtm|cldc|cmd\-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc\-s|devi|dica|dmob|do(c|p)o|ds(12|\-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(\-|_)|g1 u|g560|gene|gf\-5|g\-mo|go(\.w|od)|gr(ad|un)|haie|hcit|hd\-(m|p|t)|hei\-|hi(pt|ta)|hp( i|ip)|hs\-c|ht(c(\-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i\-(20|go|ma)|i230|iac( |\-|\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\/)|klon|kpt |kwc\-|kyo(c|k)|le(no|xi)|lg( g|\/(k|l|u)|50|54|\-[a-w])|libw|lynx|m1\-w|m3ga|m50\/|ma(te|ui|xo)|mc(01|21|ca)|m\-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(\-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)\-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|\-([1-8]|c))|phil|pire|pl(ay|uc)|pn\-2|po(ck|rt|se)|prox|psio|pt\-g|qa\-a|qc(07|12|21|32|60|\-[2-7]|i\-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h\-|oo|p\-)|sdk\/|se(c(\-|0|1)|47|mc|nd|ri)|sgh\-|shar|sie(\-|m)|sk\-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h\-|v\-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl\-|tdg\-|tel(i|m)|tim\-|t\-mo|to(pl|sh)|ts(70|m\-|m3|m5)|tx\-9|up(\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|\-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(\-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas\-|your|zeto|zte\-/i.test(a.substr(0,4)))check = true})(navigator.userAgent||navigator.vendor||window.opera);
            return check;
        }

        var clickeventtype = mobilecheck() ? 'touchstart' : 'click';

        (function() {
            var support = { animations : Modernizr.cssanimations },
                animEndEventNames = { 'WebkitAnimation' : 'webkitAnimationEnd', 'OAnimation' : 'oAnimationEnd', 'msAnimation' : 'MSAnimationEnd', 'animation' : 'animationend' },
                animEndEventName = animEndEventNames[ Modernizr.prefixed( 'animation' ) ],
                onEndAnimation = function( el, callback ) {
                    var onEndCallbackFn = function( ev ) {
                        if( support.animations ) {
                            if(ev.target != this) return;
                            this.removeEventListener( animEndEventName, onEndCallbackFn);
                        }
                        if(callback && typeof callback === 'function') {callback.call();}
                    };
                    if( support.animations ) {
                        el.addEventListener(animEndEventName, onEndCallbackFn);
                    }
                    else {
                        onEndCallbackFn();
                    }
                };

            [].slice.call(document.querySelectorAll('.button--sonar')).forEach(function(el) {
                el.addEventListener(clickeventtype, function(ev) {
                    if( el.getAttribute('data-state') !== 'locked' ) {
                        classie.add(el, 'button--active');
                        onEndAnimation(el, function() {
                            classie.remove(el, 'button--active');
                        });
                    }
                });
            });
        })();
    </script>

    <title>BingoTouch</title>

    <!--引用url定义文件-->
    <script src="url.js" type="text/javascript"></script>

    <script type="text/javascript">
        app.page.onReady = function() {

        }

        app.page.onLoad = function() {

        }
    </script>

    <script src="js/dynamics.min.js"></script>
    <script src="js/main.js"></script>
    <script>
        (function() {

            var support = { animations : Modernizr.cssanimations },
                animEndEventNames = { 'WebkitAnimation' : 'webkitAnimationEnd', 'OAnimation' : 'oAnimationEnd', 'msAnimation' : 'MSAnimationEnd', 'animation' : 'animationend' },
                animEndEventName = animEndEventNames[ Modernizr.prefixed( 'animation' ) ],
                onEndAnimation = function( el, callback ) {
                    var onEndCallbackFn = function( ev ) {
                        if( support.animations ) {
                            if(ev.target != this) return;
                            this.removeEventListener( animEndEventName, onEndCallbackFn);
                        }
                        if(callback && typeof callback === 'function') {callback.call();}
                    };
                    if( support.animations ) {
                        el.addEventListener(animEndEventName, onEndCallbackFn);
                    }
                    else {
                        onEndCallbackFn();
                    }
                };

            function nextSibling(el) {
                var nextSibling = el.nextSibling;
                while(nextSibling && nextSibling.nodeType != 1) {
                    nextSibling = nextSibling.nextSibling
                }
                return nextSibling;
            }

            var krisna = new Stack(document.getElementById('stack_krisna'));

            // controls the click ring effect on the button
            var buttonClickCallback = function(bttn) {
                var bttn = bttn || this;
                bttn.setAttribute('data-state', 'unlocked');
            };

            document.querySelector('.button--accept[data-stack = stack_krisna]').addEventListener(clickeventtype, function() { krisna.accept(buttonClickCallback.bind(this)); });
            document.querySelector('.button--reject[data-stack = stack_krisna]').addEventListener(clickeventtype, function() { krisna.reject(buttonClickCallback.bind(this)); });

            [].slice.call(document.querySelectorAll('.button--sonar')).forEach(function(bttn) {
                bttn.addEventListener(clickeventtype, function() {
                    bttn.setAttribute('data-state', 'locked');
                });
            });

            [].slice.call(document.querySelectorAll('.button--material')).forEach(function(bttn) {
                var radialAction = nextSibling(bttn.parentNode);

                bttn.addEventListener(clickeventtype, function(ev) {
                    var boxOffset = radialAction.parentNode.getBoundingClientRect(),
                        offset = bttn.getBoundingClientRect();

                    radialAction.style.left = Number(offset.left - boxOffset.left) + 'px';
                    radialAction.style.top = Number(offset.top - boxOffset.top) + 'px';

                    classie.add(radialAction, classie.has(bttn, 'button--reject') ? 'material-circle--reject' : 'material-circle--accept');
                    classie.add(radialAction, 'material-circle--active');
                    onEndAnimation(radialAction, function() {
                        classie.remove(radialAction, classie.has(bttn, 'button--reject') ? 'material-circle--reject' : 'material-circle--accept');
                        classie.remove(radialAction, 'material-circle--active');
                    });
                });
            });
        })();
    </script>

    <style type="text/css">

    </style>
</head>
<body>
<div id="section_container">
    <section id="index_section" class="active">
        <div data-role="page">
            <!--Header-->
            <div class="header">

                <div class="title row-box">
                    <div class="box-left">
                        <div data-role="BTButton" data-type="image" onClick="app.back();">
                            <img src="../../css/images/icons/icon-back.png" alt=""/>
                        </div>
                    </div>
                    <div class="span1">
                        <h1>帮助文档</h1>
                    </div>
                    <div class="box-right">
                        <div data-role="BTButton" data-type="image" onclick="app.refresh();">
                            <img src="../../css/images/icons/navicon/icon-refresh.png" alt=""/>
                        </div>
                    </div>
                </div>

            </div>
            <!--Content-->

            <div class="container">
                <div class="content color-2">
                    <h2 class="content__title">Krisna</h2>
                    <ul id="stack_krisna" class="stack stack--krisna">
                        <li class="stack__item"><img src="img/3.png" alt="Tree 3" /></li>
                        <li class="stack__item"><img src="img/4.png" alt="Tree 4" /></li>
                        <li class="stack__item"><img src="img/5.png" alt="Tree 5" /></li>
                        <li class="stack__item"><img src="img/6.png" alt="Tree 6" /></li>
                        <li class="stack__item"><img src="img/1.png" alt="Tree 1" /></li>
                        <li class="stack__item"><img src="img/2.png" alt="Tree 2" /></li>
                    </ul>
                    <div class="controls">
                        <button class="button button--sonar button--reject" data-stack="stack_krisna"><i class="fa fa-times"></i><span class="text-hidden">Reject</span></button>
                        <button class="button button--sonar button--accept" data-stack="stack_krisna"><i class="fa fa-check"></i><span class="text-hidden">Accept</span></button>
                    </div>
                </div>
            </div>

            <!--Footer-->
            <div class="footer" data-fixed="bottom">

            </div>
        </div>
    </section>
</div>

</body>
</html>
