﻿<!DOCTYPE html>
<html lang="en-US">
<head>
    <meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/>
    <meta name="format-detection" content="telephone=no"/>
    <meta name="viewport" content="width=device-width,initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0,user-scalable=no"/>
    <!--CSS-->
    <link rel="stylesheet" href="../../frame3/css/bingotouch.css"/>
    <link rel="stylesheet" href="../../frame3/css/app.css"/>

    <!--JS-->
    <script src="../../frame3/js/cordova.js"></script>
    <script src="../../frame3/js/zepto.js"></script>
    <script src="../../frame3/js/iscroll.js"></script>
    <script src="../../frame3/js/baiduTemplate.js"></script>
    <script src="../../frame3/js/bingotouch.js"></script>
    <script src="../../frame3/js/require.js"></script>
    <script src="../../frame3/js/plugin/linkplugins.js"></script>
    <script src="../../frame3/js/app/app.js"></script>
    <script src="../../js/xh_public.js"></script>
    <script src="../../js/xh_iscroll.js"></script>

    <script src="../../js/SzgaPlugin.js"></script>

    <title>BingoTouch</title>

    <!--引用url定义文件-->
    <script src="url.js" type="text/javascript"></script>

    <script type="text/javascript">

        //Dom结构加载完成后执行
        app.page.onReady = function () {
            //to do
        }

        //页面所有元素加载完成后执行
        app.page.onLoad = function () {
            document.addEventListener("deviceready", function () {
                document.addEventListener("backbutton", function () {
                    app.back();
                }, false);
            }, false);
        }

        //捕获全局js错误
        app.page.onError = function (msg, url, line) {
            //alert(msg);
        }


    </script>

    <style type="text/css">
        .font-size-22 {
            font-size: 22px;
        }
        .font-size-18 {
            font-size: 18px;
        }
        .font-color-c8c8c8 {
            color: #c8c8c8;
        }
    </style>
</head>
<body class="desktop">
    <div id="section_container">
        <section id="index_section" class="active">
            <div data-role="page">
                <!--Header-->
                <div class="header">
                    <div class="title row-box" style="background: #d93a49 !important;border-color: #d93a49 !important;text-shadow: none;">
                        <div class="box-left" style="background:  #d93a49">
                            <div data-role="BTButton" data-type="image" onClick="app.back();">
                                <img src="../../css/images/icons/icon-back.png" alt=""/>
                            </div>
                        </div>
                        <div class="span1" style="background:  #d93a49">
                            <h1>关于应用</h1>
                        </div>
                        <div class="box-right" style="background:  #d93a49">
                            <div data-role="BTButton" data-type="image" onclick="app.refresh();">
                                <img src="../../css/images/icons/navicon/icon-refresh.png" alt=""/>
                            </div>
                        </div>
                    </div>
                </div>
                <!--Content-->
                <div class="content iscroll-wrapper">
                    <div>
                        <div class="container-fluid">
                            <div class="row-fluid">
                                <div class="span3">
                                    <img src="img1/mylogin.png" style="width: 100px;height: 100px;" />
                                </div>
                                <div class="span9" style="line-height: 0.7;">
                                    <h2>移动纪检云首页</h2>
                                    <p>
                                        版本：<span class="normal-text">V1.01.20211127</span>
                                    </p>
                                    <p>发布日期：<span class="normal-text">2021-11-27</span></p>
                                </div>
                            </div>
                            <div style="height: 1px;background-color: gainsboro;"></div>
                        </div>
                        <div class="container-fluid">
                            <div class="row-box">
                                <div class="span1" style="line-height: 1.2">
                                    <h2>介绍</h2>
                                    <p class="normal-text">移动纪检云首页信创。</p>
                                </div>
                            </div>
                            <!--<div class="row-box" style="margin-top: 10px;">-->
                                <!--<div class="span1" style="line-height: 1.2">-->
                                    <!--<span style="font-weight: bold;">开发单位：</span>-->
                                    <!--<span class="normal-text">深圳星火电子工程公司</span>-->
                                <!--</div>-->
                            <!--</div>-->
                            <div style="height: 1px;background-color: gainsboro;margin: 0px 0;"></div>
                        </div>
                        <div class="container-fluid">
                            <!--<div class="row-box">-->
                                <!--<div class="span1" style="line-height: 1.0">-->
                                    <!--<h2>当前版本</h2>-->
                                    <!--<p class="normal-text">版本：v1.40.20200619</p>-->
                                    <!--<p class="normal-text">发布日期：2020-06-19</p>-->
                                    <!--<p class="normal-text">底部增加logo</p>-->
                                    <!--<p class="normal-text">应用名居中修复</p>-->
                                    <!--<p>&nbsp;</p>-->
                                <!--</div>-->
                            <!--</div>-->
                            <!--<div class="row-box">-->
                                <!--<div class="span1" style="line-height: 1.0">-->
                                    <!--<h2>当前版本</h2>-->
                                    <!--<p class="normal-text">版本：v1.38.20100119</p>-->
                                    <!--<p class="normal-text">发布日期：2020-01-19</p>-->
                                    <!--<p class="normal-text">待阅公文红点不显示具体条数</p>-->
                                    <!--<p>&nbsp;</p>-->
                                <!--</div>-->
                            <!--</div>-->
                            <!--<div style="height: 1px;background-color: gainsboro;margin-top: 0px;"></div>-->
                        </div>
                        <div class="container-fluid" >
                            <div class="row-box">
                                <!--<div class="span1" style="line-height: 1.0">-->
                                    <!--<h2>历史版本</h2>-->
                                    <!--<p class="normal-text">版本：v1.37.20191210</p>-->
                                    <!--<p class="normal-text">发布日期：2019-12-10</p>-->
                                    <!--<p class="normal-text">常用电话隐藏</p>-->
                                    <!--<p>&nbsp;</p>-->
                                    <!--<p class="normal-text">版本：v1.36.20191113</p>-->
                                    <!--<p class="normal-text">发布日期：2019-11-13</p>-->
                                    <!--<p class="normal-text">测试首页版本是否更新问题</p>-->
                                    <!--<p class="normal-text">首页使用排名无记录问题修复</p>-->
                                    <!--<p>&nbsp;</p>-->
                                    <!--<p class="normal-text">版本：v1.35.20191017</p>-->
                                    <!--<p class="normal-text">发布日期：2019-10-17</p>-->
                                    <!--<p class="normal-text">首页红点待办事项无法打开问题修复</p>-->
                                    <!--<p>&nbsp;</p>-->
                                    <!--<p class="normal-text">版本：v1.34.20191016</p>-->
                                    <!--<p class="normal-text">发布日期：2019-10-16</p>-->
                                    <!--<p class="normal-text">测试应用无法下载问题 打开应用appId更改为appCode</p>-->
                                    <!--<p>&nbsp;</p>-->
                                    <!--<p class="normal-text">版本：v1.33.20191010</p>-->
                                    <!--<p class="normal-text">发布日期：2019-10-10</p>-->
                                    <!--<p class="normal-text">纪委2.0首页发布。</p>-->
                                    <!--<p>&nbsp;</p>-->
                                <!--</div>-->
                            </div>
                        </div>
                    </div>
                </div>
                <!--Footer-->
                <div class="footer" data-fixed="bottom">

                </div>
            </div>
        </section>
    </div>
</body>
</html>
