var ui={settings:{appType:"single",transitionType:"slide",showPageLoading:!0,showPageLoadingText:"加载中...",showPageLoadingIcon:"icon-spinner5",init:function(){}},hasSidebarOpen:!1,hasPopupOpen:!1,isPageSwitching:!1,launch:function(){$.noop=function(){},ui.Utils.setViewPort(),ui.init(),"single"==ui.settings.appType&&ui.Page.init(),ui.settings.init(),document.addEventListener("doubleTap",function(a){a.preventDefault()},!1)},run:function(a){$.extend(this.settings,a),$.each(ui.pages,function(a,b){var c="#"+a;$("body").delegate(c,"pageReady",function(a,c){b.pageReady&&b.pageReady.call(b,a,c)}),$("body").delegate(c,"pageLoad",function(a,c){b.pageLoad&&b.pageLoad.call(b,a,c)}),$("body").delegate(c,"pageBack",function(a,c){b.pageBack&&b.pageBack.call(b,a,c)})}),this.launch()},pages:{},addPageEvent:function(a,b){ui.pages[a]=new b}},GC=function(){var a={},b=function(a){var c,d,e,f=a.attributes;if(f)for(c=f.length-1;c>=0;c-=1)e=f[c].name,"function"==typeof a[e]&&(a[e]=null);if(f=a.childNodes)for(d=f.length,c=0;d>c;c+=1)b(a.childNodes[c])},c=function(b,c,d){var e=$(d).attr("id")||"body";a[e]||(a[e]={}),a[e][c]=b.concat(a[e][c]||[])},d=function(b,c){return a[b]?a[b][c]:[]},e=function(b){if(a[b]){for(var c in a[b])for(var d=c.length-1;d>=0;d-=1){var e=c[d];e.destroyMe&&e.destroyMe(),e=null}$("#"+b).unbind("tap"),a[b]=null}};return{purge:b,push:c,get:d,destroy:e,refs:a}}();!function(window){window.app=window.app||{},window.app.page=window.app.page||{},app.page.onReady=function(){},app.page.onLoad=function(){},app.page.onError=function(){},window.app.utils=window.utils||{},app.utils.toJSON=function(param){return"string"==typeof param?eval("("+param+")"):param},app.utils.isPC=function(){for(var a=navigator.userAgent,b=new Array("Android","iPhone","SymbianOS","Windows Phone","iPad","iPod"),c=!0,d=0;d<b.length;d++)if(a.indexOf(b[d])>0){c=!1;break}return c},app.ajax=function(a){a=a||{},a.data=app.utils.toJSON(a.data),a.method=a.method||"GET","undefined"==typeof a.async&&(a.async=!0),a.contentType=a.contentType||"",a.headers=a.headers||{},a.timeout=a.timeout||1e4;var b=function(b){a.success(app.utils.toJSON(b))},c=function(b){a.fail(app.utils.toJSON(b))};Cordova.exec(b,c,"HttpRequest","ajax",[a.url,a.data,a.method,a.async,a.contentType,a.headers,a.timeout])},app.ajaxWSDL=function(a){a.data=a.data||{},a.async=a.async||!0,a.timeout=a.timeout||1e4,Cordova.exec(a.success,a.fail,"HttpRequest","ajaxWSDL",[a.method,a.data,a.namespace,a.endpoint,a.async,a.timeout])},app.post=function(a,b,c,d){app.ajax({url:a,data:b,method:"POST",contentType:"application/x-www-form-urlencoded",success:c,fail:d})},app.get=function(a,b,c,d){app.ajax({url:a,data:b,method:"GET",success:c,fail:d})},app.exit=function(){Cordova.exec(null,null,"ExtendApp","exit",[])},app.isExist=function(a,b){return"undefined"==typeof a?void app.alert("appId is necessary!"):(b=b||function(a){app.alert(a)},void Cordova.exec(b,null,"ExtendApp","isExistApp",[a]))},app.run=function(a,b){("undefined"==typeof b||""==b)&&(b={}),Cordova.exec(null,null,"ExtendApp","runApp",[a,b])},app.openFile=function(a,b,c,d){a=a||"",b=b||"",c=c||function(){},d=d||function(){app.hint("没有找到合适的程序打开该文件")},Cordova.exec(c,d,"ExtendApp","openFile",[a,b])},app.getAppDirectoryEntry=function(a){var b=function(b){a(app.utils.toJSON(b))};Cordova.exec(b,null,"ExtendApp","getAppDirectoryEntry",[])},app.load=function(a){if(!a)return void app.alert("should be object like {url:'http://domain',params:{....}}");if(!a.url)return void app.alert("url is necessary!");var b=a.url;if(b.startWith("http://")||b.startWith("https://")||b.startWith("file://")||b.startWith("/"));else{var c=window.location.href,d=c.lastIndexOf("/");b=c.substring(0,d)+"/"+b,a.url=b}return app.utils.isPC()?void(window.location.href=b):void Cordova.exec(null,null,"Page","loadUrl",[a])},app.loadWithUrl=function(a,b,c,d){if("undefined"==typeof a)return void app.alert("url is necessary!");b=b||{},c=c||"left";var e={url:a,params:b,slideType:c,progress:d};app.load(e)},app.rotation=function(a){Cordova.exec(null,null,"RotationPlugin","setRotation",[a])},app.getPageParams=function(a){var b=function(b){a(app.utils.toJSON(b))};Cordova.exec(b,null,"Page","getPageParams",[])},app.back=function(a){"undefined"==typeof a&&(a=""),$.isFunction(a)&&(a="("+a.toString()+")()"),Cordova.exec(null,null,"Page","back",[a])},app.refresh=function(){Cordova.exec(null,null,"Page","refresh",[])},app.getCurrentUri=function(a){Cordova.exec(a,null,"Page","getCurrentUri",[])},app.getLocation=function(a,b){var c=function(b){a(app.utils.toJSON(b))};Cordova.exec(c,b,"LocationPlugin","location",[])},app.getInfo=function(a){var b=function(b){a(app.utils.toJSON(b))};Cordova.exec(b,null,"ExtendApp","getInfo",[])},app.getSize=function(a){var b=function(b){a(app.utils.toJSON(b))};Cordova.exec(b,null,"ExtendApp","getSize",[])},app.alert=function(a,b,c,d){b=b||function(){},c=c||"提示",d=d||"确定","object"==typeof a&&(a=JSON.stringify(a)),navigator.notification.alert(a,b,c,d)},app.confirm=function(a,b,c,d){b=b||function(){},c=c||"提示",d=d||"确认,取消",navigator.notification.confirm(a,b,c,d)},app.hint=function(a,b){a=a||"Hello BingoTouch!",b=b||"bottom",Cordova.exec(null,null,"ExtendApp","hint",[a,b])},app.vibrate=function(a){navigator.notification.vibrate(a)},app.install=function(a,b,c){b=b||function(a){app.hint(a)},c=c||function(a){app.hint(a)},"android"==window.devicePlatform?Cordova.exec(b,c,"ExtendApp","install",[a]):"iOS"==window.devicePlatform&&app.alert("iOS platform do not support this api!")},app.setGlobalVariable=function(a,b){Cordova.exec(null,null,"ExtendApp","setVariable",[a,b])},app.getGlobalVariable=function(a,b){Cordova.exec(b,null,"ExtendApp","getVariable",[a])},app.getSimInfo=function(a){Cordova.exec(a,null,"ExtendApp","getSimInfo",[])},app.openContactSelector=function(a){Cordova.exec(a,null,"ContractEx","getContracts",[])},window.app.setting=window.app.setting||{},app.setting.set=function(a,b){return"undefined"==typeof a||"undefined"==typeof b?void app.alert("name and value is necessary!"):void Cordova.exec(null,null,"Setting","set",[a,b])},app.setting.get=function(a,b,c){return""==a||"undefined"==typeof a?void app.alert("name is necessary!"):(b=b||"",c=c||function(a){app.alert(a)},void Cordova.exec(c,null,"Setting","get",[a,b]))},app.setting.remove=function(a){return"undefined"==typeof a?void app.alert("name is necessary!"):void Cordova.exec(null,null,"Setting","remove",[a])},app.setting.clear=function(){Cordova.exec(null,null,"Setting","clear",[])},app.setting.getAll=function(a){var b=function(b){a(app.utils.toJSON(b))};Cordova.exec(b,null,"Setting","load",[])},window.app.progress=window.app.progress||{},app.progress.start=function(a,b){a=a||"",b=b||"",Cordova.exec(null,null,"ExtendApp","progressStart",[a,b])},app.progress.stop=function(){Cordova.exec(null,null,"ExtendApp","progressStop",[])},window.app.dateTimePicker=window.app.dateTimePicker||{},app.dateTimePicker.selectDate=function(a,b,c){var d=new Date;b=b||{year:d.getFullYear(),month:d.getMonth()+1,day:d.getDate()},c=c||"yyyy-MM-dd";var e=function(b){a(app.utils.toJSON(b))};"android"==window.devicePlatform?Cordova.exec(e,null,"DateTimePicker","date",[b,c]):"iOS"==window.devicePlatform&&app.dateTimePicker.wheelSelectDate(a,b,c)},app.dateTimePicker.selectTime=function(a,b,c,d){var e=new Date;b=b||{hour:e.getHours(),minute:e.getMinutes()},c=c||"hh:mm",d=d||!0;var f=function(b){a(app.utils.toJSON(b))};"android"==window.devicePlatform?Cordova.exec(f,null,"DateTimePicker","time",[b,c,d]):"iOS"==window.devicePlatform&&app.dateTimePicker.wheelSelectTime(a,b,c,d)},app.dateTimePicker.wheelSelectDate=function(a,b,c,d,e){var f=new Date;b=b||{year:f.getFullYear(),month:f.getMonth()+1,day:f.getDate()},c=c||"yyyy-MM-dd",d=d||{minYear:2e3,maxYear:2046};var g=function(b){a(app.utils.toJSON(b))};b.month=b.month-1,e=e||!1,e&&"iOS"==window.devicePlatform?(b.month=b.month+1,Cordova.exec(g,null,"WheelSelectPluginFormat","date",[b,c,d])):Cordova.exec(g,null,"WheelSelectPlugin","date",[b,c,d])},app.dateTimePicker.wheelSelectTime=function(a,b,c,d){var e=new Date;b=b||{hour:e.getHours(),minute:e.getMinutes()},c=c||"hh:mm",d=d||!0;var f=function(b){a(app.utils.toJSON(b))};Cordova.exec(f,null,"WheelSelectPlugin","time",[b,c,d])},window.app.wheelSelect=window.app.wheelSelect||{},app.wheelSelect.oneSelect=function(a,b,c,d,e){a=a||[],d=d||"提示",e=e||{sureBtn:"确定",cancelBtn:"取消"},c=c||"";var f=function(a){b(app.utils.toJSON(a))};Cordova.exec(f,null,"WheelSelectPlugin","oneSelect",[a,c,d,e])},window.app.phone=window.app.phone||{},app.phone.sendSMS=function(a,b){Cordova.exec(null,null,"PhonePlugin","sms",[a,b])},app.phone.dial=function(a){Cordova.exec(null,null,"PhonePlugin","dial",[a])},window.app.sso=window.app.sso||{},app.sso.login=function(a,b,c){a=a||{},a.credential_type=a.credential_type||"password",a.get_spec_secret="y"==a.get_spec_secret?!0:!1,a.get_service_ticket="y"==a.get_service_ticket?!0:!1;var d=function(a){b(app.utils.toJSON(a))},e=function(a){c(app.utils.toJSON(a))};Cordova.exec(d,e,"SSOPlugin","login",[a])},app.sso.logout=function(a,b){a=a||function(){},b=b||function(){},Cordova.exec(a,b,"SSOPlugin","logout",[])},app.sso.isLogined=function(a){Cordova.exec(a,null,"SSOPlugin","isLogined",[])},window.app.database=window.app.database||{},app.database.open=function(a,b,c){return""==a||"undefined"==typeof a?(app.alert("name is necessary!"),null):window.openDatabase(a,b,a,c)},app.database.executeNonQuery=function(a,b,c,d){c=c||function(){},d=d||function(a){app.alert(a)},a.transaction(function(a){if("string"==typeof b)a.executeSql(b);else if($.isArray(b))for(var c=0;c<b.length;c++)a.executeSql(b[c])},d,c)},app.database.executeQuery=function(a,b,c,d){c=c||function(){},d=d||function(a){app.alert(a)},a.transaction(function(a){a.executeSql(b,[],c,d)},d)},window.app.barcode=window.app.barcode||{},app.barcode.scan=function(a,b){Cordova.exec(a,b,"BarcodeScanner","scan",[])},window.app.notification=window.app.notification||{},app.notification.notify=function(a){a=a||{},a.title=a.title||"",a.body=a.body||"",a.isAutoDisappear=a.isAutoDisappear||!0,a.disappearTime=a.disappearTime||5e3,a.clickAction=a.clickAction||"",a.clickActionParams=a.clickActionParams||{},Cordova.exec(null,null,"LocalNotification","notify",[a.title,a.body,a.isAutoDisappear,a.disappearTime,a.clickAction,a.clickActionParams])},window.app.shareplugin=window.app.shareplugin||{},app.shareplugin.share=function(a){Cordova.exec(null,null,"ShareSDKPlugin","share",[a.title,a.titleUrl,a.text,a.url,a.comment,a.siteName,a.siteUrl])},window.app.timetask=window.app.timetask||{},app.timetask.start=function(a){if(a=a||{},a.id=a.id||"",a.taskAction=a.taskAction||"",a.maxCount=a.maxCount||1e4,a.loopTime=a.loopTime||1e3,a.isImmediate=a.isImmediate||!1,""==a.id)return void app.alert("任务id不能为空!");if(""==a.taskAction)return void app.alert("任务动作不能为空!");var b=function(b){a.callback(app.utils.toJSON(b))};Cordova.exec(b,null,"TimeTask","taskStart",[a])},app.timetask.stop=function(a,b){if(a=a||"",""==a)return void app.alert("任务id不能为空!");var c=function(a){b(app.utils.toJSON(a))};Cordova.exec(c,null,"TimeTask","taskStop",[a])}}(window),$(function(){window.onerror=function(a,b,c){app.page.onError(a,b,c)},app.page.onReady()}),window.onload=function(){document.addEventListener("deviceready",function(){app.page.onLoad()},!1)}(),ui.Utils={setViewPort:function(){var a="",b=navigator.userAgent.toLowerCase();if(a=/android (\d+\.\d+)/.test(b)?"width=device-width,initial-scale=1,user-scalable=no,target-densitydpi =240":"width=device-width,initial-scale=0.666666,user-scalable=no",$("meta[name='viewport']").length>0)$("meta[name='viewport']").attr("content",a);else{var c=document.createElement("meta");c.name="viewport",c.content=a;var d=document.getElementsByTagName("head")[0];d.appendChild(c)}},parseHash:function(a){var b,c,d={},e=a.split("?");if(b=e[0],e.length>1){var f,g;c=e[1],f=c.split("&");for(var h=0;h<f.length;h++)f[h]&&(g=f[h].split("="),d[g[0]]=g[1])}return{hash:a,tag:b,query:c,param:d}},HexToRgb:function(a){var b=/^\#?[0-9a-fA-Z]{6}$/;if(!b.test(a))return window.alert("输入错误的hex颜色值");a=a.replace("#","");for(var c=a.match(/../g),d=0;3>d;d++)c[d]=parseInt(c[d],16);return c},RgbToHex:function(a,b,c){var d=/^\d{1,3}$/;if(!d.test(a)||!d.test(b)||!d.test(c))return window.alert("输入错误的rgb颜色值");for(var e=[a.toString(16),b.toString(16),c.toString(16)],f=0;3>f;f++)1==e[f].length&&(e[f]="0"+e[f]);return"#"+e.join("")},getDarkColor:function(a,b){var c=/^\#?[0-9a-fA-Z]{6}$/;if(!c.test(a))return window.alert("输入错误的hex颜色值");for(var d=ui.Utils.HexToRgb(a),e=0;3>e;e++)d[e]=Math.floor(d[e]*(1-b));return ui.Utils.RgbToHex(d[0],d[1],d[2])},getLightColor:function(a,b){var c=/^\#?[0-9a-fA-Z]{6}$/;if(!c.test(a))return window.alert("输入错误的hex颜色值");for(var d=ui.Utils.HexToRgb(a),e=0;3>e;e++)d[e]=Math.floor((255-d[e])*b+d[e]);return ui.Utils.RgbToHex(d[0],d[1],d[2])},getWebSafeColor:function(a){var b=/^\#?[0-9a-fA-Z]{6}$/;if(!b.test(a))return window.alert("输入错误的hex颜色值");for(var c=ui.Utils.HexToRgb(a),d=0;3>d;d++){var e=51*Math.floor(c[d]/51),f=51*Math.ceil(c[d]/51);c[d]=Math.abs(e-c[d])<=Math.abs(f-c[d])?e:f}return ui.Utils.RgbToHex(c[0],c[1],c[2])},getUrlQueryString:function(a){var b=location.search.match(new RegExp("[?&]"+a+"=([^&]*)(&?)","i"));return b?b[1]:b},getDefaultDatePickerValue:function(a,b,c){var d=[],e={};return"date"==a||"wheeldate"==a?(d=b.split("-"),c?(e.year=c.indexOf("y")>-1?d.shift():2016,e.month=c.indexOf("m")>-1?d.shift():1,e.day=c.indexOf("d")>-1?d.shift():1):(e.year=d[0],e.month=d[1],e.day=d[2])):(d=b.split(":"),e.hour=d[0],e.minute=d[1]),e},getFormatDatePickerValue:function(a,b,c){if(!c)return b.full;c=c.toLowerCase();var d=[];return"date"==a||"wheeldate"==a?(c.indexOf("y")>-1&&d.push(b.year),c.indexOf("m")>-1&&d.push(b.month),c.indexOf("d")>-1&&d.push(b.day),d.join("-")):void 0}},String.prototype.startWith=function(a){var b=new RegExp("^"+a);return b.test(this)},String.prototype.endWith=function(a){var b=new RegExp(a+"$");return b.test(this)},String.prototype.getQueryString=function(a){var b,c=new RegExp("(^|&|\\?)"+a+"=([^&]*)(&|$)");return(b=this.match(c))?(unescape(b[2])||"").split("#")[0]:null},$.fn.toJSON=function(a){var b=$(this),c={};return $("[data-role='BTCheck']",b).each(function(){var b=$(this),d=b.attr("name"),e=b.btcheck("val")[0];null!=d&&null!=e&&(c[d]=c[d]||[],c[d].push(a?e.value:e))}),$("[data-role='BTRadio']",b).each(function(){var b=$(this),d=b.attr("name"),e=b.btradio("val");null!=d&&null!=e&&(c[d]=a?e.value:e)}),$("[data-role='BTSwitch']",b).each(function(){var a=$(this),b=a.attr("name"),d=a.btswitch("val");null!=b&&(c[b]=d)}),$("[data-role='BTSelect']",b).each(function(){var b,d=$(this),e=d.attr("type"),f=d.attr("name");b="date"==e||"time"==e?d.btdatepicker("val"):d.btselect("val"),null!=f&&(c[f]=a?b.value:b)}),$('.dropdown > div[data-role="BTButton"]',b).each(function(){var a=$(this),b=a.attr("name"),d=a.btdropdown("val");null!=b&&(c[b]=d)}),$("input",b).each(function(){var a=$(this),b=a.attr("type"),d=a.attr("name");"text"==b||"password"==b?c[d]=a.attr("value"):"checkbox"==b?a.attr("checked")&&(c[d]=c[d]||[],c[d].push(a.attr("value"))):"radio"==b&&a.attr("checked")&&(c[d]=a.attr("value"))}),$("textarea",b).each(function(){var a=$(this);c[a.attr("name")]=a.attr("value")}),$("select",b).each(function(){var a=$(this);c[a.attr("name")]=a.attr("value")}),c},function(a){a.form={},a.list={},$.bt_jqprefix="bt",$.bt_staticprefix="ui",$.bt_options={val:function(a){return $(this).attr("value")||a}},$.bt=function(a,b){$.fn[$.bt_jqprefix+a]=function(a){for(var c=[],d=1;d<arguments.length;d++)c.push(arguments[d]);var e=b[a];return e.apply(this,c)}},a._off=function(a,b,c){return a=a||"div[data-role="+c+"]",$(a).unbind(b||"tap"),a},window.st=null,a.bindTouchStart=function(a){$(a).bind("touchstart",function(){$(this).addClass("btn-active")})},a.bindTouchEnd=function(a){$(a).bind("touchend",function(){$(this).removeClass("btn-active")})},$.uiwidget={mark:"data-role",map:{},dependMap:{},funcs:[],register:function(){var a=arguments[0],b=null,c=null;return 1==arguments.length?(b=arguments[0],void $.uiwidget.funcs.push(b)):(2==arguments.length?b=arguments[1]:3==arguments.length&&(b=arguments[2],c=arguments[1]),$.uiwidget.map[a]=b,void($.uiwidget.dependMap[a]=c))},init:function(a,b){function c(a){var b=$.uiwidget.dependMap[a];b&&$(b).each(function(a,b){c(b)}),!e[a]&&d.push(a),e[a]=!0}var d=[],e={};for(var f in $.uiwidget.map)c(f);a=a||{},a.before&&a.before(b);$(d).each(function(a,c){if($.uiwidget.map[c]){var d=$("["+$.uiwidget.mark+"^='"+c+"'],["+$.uiwidget.mark+"*=',"+c+"']",b);d.length>0&&$.uiwidget.map[c](d,b)}}),$($.uiwidget.funcs).each(function(a,c){c&&c(b)}),a.after&&a.after(b),e=null,d=null}},$.fn.uiwidget=function(){$.uiwidget.init({},$(this))},$.pageLoad={before:[],after:[]},$.pageLoad.register=function(a,b){$.pageLoad[a].push(b)},a.init=function(){$(document.body).uiwidget()}}(window.ui),function(undefined){$.bt("button",{off:function(a){return ui._off(this,a,"BTButton")}}),$.uiwidget.register("BTButton",function(selector){selector.each(function(){var status=$(this).attr("data-status"),url=$(this).attr("data-url"),urlArg=$(this).attr("data-urlarg"),mousedown=$(this).attr("mousedown"),mouseup=$(this).attr("mouseup"),disabled=$(this).attr("disabled"),icon=$(this).attr("data-icon"),badges=$(this).attr("data-badges");1!=status&&($(this).wrapInner('<span class="btn-text"/>'),null!=icon&&$(this).append(icon.indexOf("/")>-1?'<span class="icon"><img src="'+icon+'" alt=""/></span>':'<span class="icon '+icon+'"></span>')),badges!=undefined?$(this).find("span.badges").length?$(this).find("span.badges").html(badges):$(this).append('<span class="badges">'+badges+"</span>"):$(this).find("span.badges").remove(),$(this).attr("data-status",1),url!=undefined&&""!=url&&(urlArg!=undefined&&""!=urlArg?$(this).bind("tap",function(e){var args=eval("("+urlArg+")");ui.load({url:url,params:args.params||args,slideType:args.slideType,progress:args.progress}),e.stopPropagation()}):$(this).bind("tap",function(a){ui.load({url:url,slideType:"left"}),a.stopPropagation()})),(null==mousedown||"false"!=mousedown)&&ui.bindTouchStart(this),(null==mouseup||"false"!=mouseup)&&ui.bindTouchEnd(this)})})}(),$.uiwidget.register(function(a){$('div[data-role="BTSwitchs"] >[data-role=BTButton]',a).btbutton("off","touchstart touchend"),$('div[data-role="BTSwitchs"]>[data-role=BTButton]',a).bind("tap",function(a){$(this).addClass("btn-active").siblings().removeClass("btn-active"),a.stopPropagation()})}),$.bt("check",{val:function(a,b){var c=arguments.length;if(c>0){var d=b?b(this,a):!0;if(null!=a){if(1==d){var e,f=this,g="object"==typeof a?!0:!1;g?e=a:(e=[],e.push(a)),$(f).each(function(a,b){$(b).removeClass("BTCheck_ON").addClass("BTCheck_OFF")});for(var h=0;h<e.length;h++)$(f).each(function(a,b){$(b).attr("value")==e[h]&&$(b).removeClass("BTCheck_OFF").addClass("BTCheck_ON")})}}else{var i=$(this).hasClass("BTCheck_ON");1==d&&(i||$(this).removeClass("BTCheck_OFF").addClass("BTCheck_ON"),i&&$(this).removeClass("BTCheck_ON").addClass("BTCheck_OFF"))}return $(this)}var j=[];return $(this).each(function(a,b){var c=$(b);if(c.hasClass("BTCheck_ON")){var d={};d.value=c.attr("value"),d.label=c.text(),j.push(d)}}),j}}),$.uiwidget.register("BTCheck",function(selector){selector.each(function(index,item){var callback=$(this).attr("callback");callback=callback?eval("("+callback+")"):function(){return!0},$(this).unbind().bind("tap",function(a){$(this).btcheck("val",null,callback),a.stopPropagation()})})}),$.bt("checkbox",{off:function(a){return ui._off(this,a,"BTCheckbox")}}),$.uiwidget.register(function(a){$(".collapse-header > [data-role=BTButton]",a).bind("tap",function(){$(this).toggleClass("btn-active"),$(this).find(".icon").toggleClass("icon-minus"),$(this).parent().next().toggle()}),$(".collapse.show",a).find(".collapse-header [data-role=BTButton]").addClass("btn-active").find(".icon").toggleClass("icon-minus")}),$.bt("select",{val:function(a,b){var c=arguments.length;if(c>0){var d=this,e=b?b(d,a):!0;if(e){var f="object"==typeof a?a:$.parseJSON(a),g=f.key||f.value,h=f.label||f.value;$(d).attr("value",g).find("span").text(h)}return $(d)}var i=$(this),j={};return j.value=void 0==i.attr("value")?"":i.attr("value"),j.label=void 0==i.text()?"":i.text(),j},off:function(a){return ui._off(this,a,"BTSelect")}}),$.uiwidget.register(function(target){$('div[data-role=BTSelect],div[data-role=BTButton][type="select"]',target).each(function(){var attrType=$(this).attr("type");if(!attrType||"select"==attrType){var self=this,data=eval($(this).attr("data")),value=$(this).attr("value"),title=$(this).attr("title"),callback=$(this).attr("callback");if(callback=callback?eval("("+callback+")"):function(){return!0},data){if(null!=data[0].label)for(var i=0;i<data.length;i++)data[i].key=data[i].value,data[i].value=data[i].label;for(var selected=null,i=0;i<data.length;i++)value==data[i].key&&(selected=data[i]);void 0==value||""==value||$(self).btselect("val",selected,callback),$(this).unbind("tap").bind("tap",function(){void 0==value||""==value?app.wheelSelect.oneSelect(data,function(a){$(self).btselect("val",a,callback),value=a.value},data[0].key,title):app.wheelSelect.oneSelect(data,function(a){$(self).btselect("val",a,callback),value=a.value},value,title)})}}}),$('div[data-role="BTSelect"]',target).bind("touchstart",function(){$(this).addClass("btn-active")}).bind("touchend",function(){$(this).removeClass("btn-active")})}),$.bt("datepicker",{val:function(a,b){var c=arguments.length;if(c>0){var d=this,e=b?b(d,a):!0;return e&&$(d).attr("value",a).find("span").text(a),$(d)}var f=$(this);return f.attr("value")}}),$.uiwidget.register(function(target){var obj="div[data-role='BTSelect'][type='date'],div[data-role='BTSelect'][type='wheeldate'],div[data-role='BTSelect'][type='time'],div[data-role='BTSelect'][type='wheeltime'],div[data-role='BTButton'][type='date'],div[data-role='BTButton'][type='wheeldate'],div[data-role='BTButton'][type='time'],div[data-role='BTButton'][type='wheeltime']";$(obj,target).each(function(){var callback=$(this).attr("callback");callback=callback?eval("("+callback+")"):function(){return!0};var value=$(this).attr("data");void 0!=value&&""!=value&&$(this).btdatepicker("val",value,callback),$(this).unbind("tap").bind("tap",function(a){function b(a,b,d,e){if(void 0==a||""==a)app.dateTimePicker[b](function(a){var b=ui.Utils.getFormatDatePickerValue(d,a,e);$(c).btdatepicker("val",b,callback)},null,e);else{var f=ui.Utils.getDefaultDatePickerValue(d,a,e);app.dateTimePicker[b](function(a){var b=ui.Utils.getFormatDatePickerValue(d,a,e);$(c).btdatepicker("val",b,callback)},f,e)}}var c=this,d=$(this).attr("type"),e=$(this).attr("value"),f=$(this).attr("format"),g=null;"date"==d?g="selectDate":"wheeldate"==d?g="wheelSelectDate":"time"==d?g="selectTime":"wheeltime"==d&&(g="wheelSelectTime"),b(e,g,d,f),a.stopPropagation()})}),$('div[data-role="BTDate"]',target).bind("touchstart",function(){$(this).addClass("btn-active")}).bind("touchend",function(){$(this).removeClass("btn-active")})}),$.bt("dropdown",{val:function(a,b){var c=arguments.length;if(c>0){var d=this,e=b?b(d,a):!0;return e&&$(d).find(".btn-text").text(a),$(d)}return $(this).find(".btn-text").text()},off:function(a){var b=this;return $(b||".dropdown > div[data-role=BTButton]").unbind(a||"tap"),b}}),$.uiwidget.register(function(target){$('.dropdown > div[data-role="BTButton"]',target).append('<span class="angle"></span>'),$('.dropdown > div[data-role="BTButton"]',target).unbind().bind("tap",function(a){$(this).toggleClass("btn-active"),$(this).find(".angle").toggle(),$(this).next().toggle(),a.stopPropagation()}),$('.dropdown > ul div[data-role="BTButton"]',target).bind("tap",function(e){var callback=$(this).closest(".dropdown").attr("callback");callback=callback?eval("("+callback+")"):function(){return!0};var obj=$(this).parents("ul").prev(),value=$(this).find(".btn-text").text();$(obj).btdropdown("val",value,callback),$(".dropdown ul").hide(),$(".angle").hide(),obj.removeClass("btn-active"),e.stopPropagation()})}),$.uiwidget.register(function(a){$(".list-view li > [data-role=BTButton]",a).bind("tap",function(){$(this).parent().siblings().find("[data-role=BTButton]").removeClass("btn-active")}),$(".list-collapse > li > [data-role=BTButton]",a).unbind().bind("tap",function(){$(this).toggleClass("btn-active").parent().siblings().find("[data-role=BTButton]").removeClass("btn-active")}),$(".list-collapse.list-view-head > li:first-child > [data-role=BTButton]",a).unbind(),$('.list-collapse[data-multiple="true"] > li > [data-role=BTButton]',a).unbind().bind("tap",function(){$(this).toggleClass("btn-active")})}),$.uiwidget.register(function(a){var b=$(".navbar > ul > li >[data-role=BTButton]",a);b.btbutton("off","touchstart touchend"),$(".navbar > ul > li ,.navbar table tr td ",a).bind("tap",function(a){$(this).siblings().find("[data-role=BTButton]").removeClass("btn-active"),$(this).find("[data-role=BTButton]").addClass("btn-active"),a.stopPropagation()}),$(".navbar .sonmenu",a).length>0&&($(".navbar > ul",a).each(function(){$(this).find("ul").hide().prev().append('<span class="angle"></span>'),$(this).find(".angle").hide()}),$('.navbar > ul > li >[data-role="BTButton"]',a).bind("tap",function(a){$(this).parent().siblings().find('[data-role="BTButton"]').removeClass("btn-active"),$(this).addClass("btn-active"),$(".angle").hide(),$("ul.sonmenu").hide(),$(this).find(".angle").show(),$(this).next().show(),a.stopPropagation()}),$('.sonmenu [data-role="BTButton"]',a).bind("tap",function(a){$(".sonmenu").hide().prev().find(".angle").hide(),a.stopPropagation()})),$("body").tap(function(){$(".sonmenu").hide().prev().find(".angle").hide()})}),$.bt("radio",{val:function(a,b){var c=arguments.length;if(c>0){var d=this,e=b?b(d,a):!0;return 1==e&&$(d).each(function(b,c){if($(c).attr("value")==a){var d=$(c).attr("name");$(c).hasClass("BTCheck_ON")||($('div[data-role="BTRadio"][name="'+d+'"]').removeClass("BTCheck_ON"),$(c).addClass("BTCheck_ON"))}}),$(d)}var f=null;return $(this).each(function(a,b){var c=$(b);$(b).hasClass("BTCheck_ON")&&(f={},f.value=c.attr("value"),f.label=c.text())}),f},off:function(a){return ui._off(this,a,"BTRadio")}}),$.uiwidget.register("BTRadio",function(selector){selector.unbind().bind("tap",function(e){var callback=$(this).attr("callback");callback=callback?eval("("+callback+")"):function(){return!0},$(this).btradio("val",$(this).attr("value"),callback),e.stopPropagation()})}),$.bt("switch",{val:function(a,b){var c=arguments.length;if(c>0){var d=this,e=$(d).hasClass("BTCheck_ON"),f=b?b(d,a):!0;return 1==f&&(null!=a?a?$(d).removeClass("BTCheck_OFF").addClass("BTCheck_ON"):e&&$(d).removeClass("BTCheck_ON").addClass("BTCheck_OFF"):e?e&&$(d).removeClass("BTCheck_ON").addClass("BTCheck_OFF"):$(d).removeClass("BTCheck_OFF").addClass("BTCheck_ON")),$(d)}return $(this).hasClass("BTCheck_ON")?!0:!1},off:function(a){return ui._off(this,a,"BTSwitch")}}),$.uiwidget.register("BTSwitch",function(selector){selector.unbind().bind("tap",function(e){var callback=$(this).attr("callback");callback=callback?eval("("+callback+")"):function(){return!0},$(this).btswitch("val",null,callback),e.stopPropagation()})}),$.uiwidget.register(function(a){var b=$(".iscroll-wrapper",a);if(b.length)$(".iscroll-wrapper",a).each(function(){ui.IScroll.init(this)});else{var c=$(".content.formcontent",a);if(c.length)if("android"==window.devicePlatform){var d=$(window).height()-$(".header",a).height()-$(".footer",a).height();c.height(d).css("overflow-y","auto")}else ui.IScroll.init(this)}}),ui.Transition=function(a){var b,c,d,e,f,g={slide:[["slideLeftOut","slideLeftIn"],["slideRightOut","slideRightIn"]],cover:[["","slideLeftIn"],["slideRightOut",""]],slideUp:[["","slideUpIn"],["slideDownOut",""]],slideDown:[["","slideDownIn"],["slideUpOut",""]],popup:[["","scaleIn"],["scaleOut",""]],flip:[["slideLeftOut","flipOut"],["slideRightOut","flipIn"]],none:[["",""],["",""]]},h=function(){var a=e[0]||"empty",b=e[1]||"empty";c.addClass("anim "+a),d.bind("webkitAnimationEnd",i).addClass("anim animating "+b)},i=function(){c.off("webkitAnimationEnd"),d.off("webkitAnimationEnd"),c.attr("class",""),d.attr("class","active"),f&&f(),ui.isPageSwitching=!1,c=d=null},j=function(i,j,k,l,m){a(":focus").trigger("blur"),b=k,c=a(i),d=a(j);var n=b?c.attr("data-transition"):d.attr("data-transition");n=n||ui.settings.transitionType,e=b?g[n][1]:g[n][0],l||(e=[]),f=m,h()},k=function(a,b,c,d,e){return g[a]?void console.error("该转场动画已经存在，请检查你自定义的动画名称(名称不能重复)"):void(g[a]=[[b,c],[d,e]])};return{run:j,add:k}}(Zepto),ui.Page=function(){function a(b){var c=b.prev("aside");c.length>0&&(a(c),c.remove())}var b=[],c={},d={},e={onReady:null,onLoad:null,onBack:null},f=function(){var a=$("#section_container section").first(),b="#"+a.attr("id");o({tag:b},!0),app.section={onReady:$.noop,onLoad:$.noop,onBack:$.noop},app.getSectionParams=function(){return d.params}},g=function(){return ui.isPageSwitching?!1:(ui.isPageSwitching=!0,!0)},h=function(a){if("single"==ui.settings.appType){if(!g())return;if(delete a.progress,delete a.slideType,ui.hasMenuOpen)return void ui.Menu.hide(200,function(){k(a)});k(a)}else app.load(a)},i=function(a){if("single"==ui.settings.appType&&b.length>1){var e=c[b[0].tag].onBack&&c[b[0].tag].onBack();if(e===!1)return;if(ui.hasMenuOpen||!g())return;var f=b.shift();if(a<b.length){var h=b.length-1-a;if(h>0)for(;h;){var i=b.shift();GC.destroy(i.tag.replace("#","")),r(i.tag),h--}}n(f.tag,b[0].tag,!0,!0,function(){GC.destroy(f.tag.replace("#","")),r(f.tag),v(b[0].tag)}),delete c[f.tag],d=b[0]}else app.back()},j=function(){if("single"==ui.settings.appType&&b.length>1){if(!g())return;var a=b.shift();GC.destroy(a.tag.replace("#","")),l(a,!1)}else app.refresh()},k=function(a){var b=/^https?:\/\//.test(a.url);b?(window.open(a.url,"_blank","location=yes"),ui.isPageSwitching=!1):l(a,!0)},l=function(a,d){var e=b[0];a.tag="#"+m(a.url),e.tag!==a.tag&&q(a,function(){u(e.tag),n(e.tag,a.tag,!1,d),o(a),c[a.tag].onLoad&&c[a.tag].onLoad(a.params)})},m=function(a){var b=a.lastIndexOf("."),c=a.lastIndexOf("/");return b>-1?a.substring(c+1,b):a.substring(c+1)},n=function(a,b,c,d,e){ui.Transition.run(a,b,c,d,e)},o=function(a){b.unshift(a)},p=function(a){c[a.tag]||(c[a.tag]={}),e.onReady!=app.section.onReady&&(c[a.tag].onReady=e.onReady=app.section.onReady),e.onLoad!=app.section.onLoad&&(c[a.tag].onLoad=e.onLoad=app.section.onLoad),e.onBack!=app.section.onBack&&(c[a.tag].onBack=e.onBack=app.section.onBack)},q=function(a,b){var e=a.url,f=a.params;ui.settings.showPageLoading&&("undefined"==typeof Cordova?ui.showMask(ui.settings.showPageLoadingText,ui.settings.showPageLoadingIcon):app.progress.start("",ui.settings.showPageLoadingText)),s(e,f,function(e){$(a.tag).remove();var f=$(e);$("#section_container").append(f),f.filter("section").addClass("active"),p(a),d=a,c[a.tag].onReady&&c[a.tag].onReady(a.params),f.uiwidget(),ui.settings.showPageLoading&&("undefined"==typeof Cordova?ui.hideMask():app.progress.stop()),b(),f=null})},r=function(b){a($(b)),$(b).remove(),$("#section_container>*").filter("script,style").remove()},s=function(a,b,c){return $.ajax({url:a+"?t="+(new Date).valueOf(),timeout:2e4,data:{},success:function(a){a?c&&c(a):t({status:4})},error:function(a){t(a)}})},t=function(a){ui.isPageSwitching=!1;var b,c=(a.status+"").substring(0,1);switch(c){case"4":b="页面不存在";break;case"5":b="服务器错误";break;case"3":b="页面已重定向"}ui.settings.showPageLoading?"undefined"==typeof Cordova?($("#bingotouch_popup").find("i,p").remove(),$("#bingotouch_popup").prepend('<i class="icon icon-cancel"></i><p>'+b+"</p>")):(app.progress.stop(),ui.showMask(b,"icon-cancel")):ui.showMask(b,"icon-cancel")},u=function(a){for(var b=GC.get(a.replace("#",""),"iScroll"),c=b.length-1;c>=0;c-=1)b[c].animating=!0},v=function(a){for(var b=GC.get(a.replace("#",""),"iScroll"),c=b.length-1;c>=0;c-=1)b[c].animating=!1};return{history:b,init:f,load:h,refresh:j,back:i}}(),ui.IScroll=function(){function a(a){a.addClass("iscroll-wrapper-clz");var b=a.next(".footer"),c=a.closest("section").find(".footer"),d=a.prev(".header"),e=a.closest("section").find(".header"),f=b.length?b:c,g=d.length?d:e;
f.length&&a.css("bottom",f.height()+"px");var h=a.attr("top")||g.height()||e.height();a.css("top",parseFloat(h)+"px")}var b={scrollBar:!1,enablePullDown:!1,enablePullUp:!1,loadingLabel:"加载中...",flipLabel:"释放刷新",pullDownlabel:"下拉刷新",pullUplabel:"上拉显示更多",pullDownAction:function(){},pullUpAction:function(){}},c=function(c,d){function e(){h&&h.removeClass("flip"),i&&i.removeClass("flip"),n.refresh(),l=!1}var f=$.extend({},b,d),g=$(c);if(g.length){a(g);var h,i,j=g.children(),k=pullUpOffset=0,l=!1;if(f.enablePullDown){var m=j.css("padding-top")||"";m=m.replace("px",""),h=$('<div class="pullDown"><span class="pullDownIcon"></span><span class="pullDownLabel">下拉刷新</span></div>').prependTo(j),k=(h.height()||51)-m,h.hide()}f.enablePullUp&&(i=$('<div class="pullUp"><span class="pullUpIcon"></span><span class="pullUpLabel">上拉显示更多</span></div>').appendTo(j),pullUpOffset=i.height()||51,i.hide());var n=new iScroll(g[0],{hScrollbar:f.scrollBar,vScrollbar:f.scrollBar,checkDOMChanges:!(f.enablePullDown||f.enablePullUp),useTransition:!1,topOffset:k,bounce:f.enablePullDown||f.enablePullUp,onBeforeScrollStart:function(a){for(var b=a.target;1!=b.nodeType;)b=b.parentNode;"SELECT"!=b.tagName&&"INPUT"!=b.tagName&&"TEXTAREA"!=b.tagName&&a.preventDefault()},onRefresh:function(){f.enablePullDown&&h.hasClass("loading")?(h.removeClass("loading"),h.find(".pullDownLabel").html(f.pullDownlabel)):f.enablePullUp&&i.hasClass("loading")&&(i.removeClass("loading"),i.find(".pullUpLabel").html(f.pullUplabel))},onScrollMove:function(){l||(window._keyboardIsShow&&(this.wrapper.scrollTop=0,$(this.scroller).height(window._currentIscrollHeight+1)),f.enablePullDown&&h.show(),f.enablePullUp&&i.show(),f.enablePullDown&&this.y>5&&!h.hasClass("flip")?(h.addClass("flip"),h.find(".pullDownLabel").html(f.flipLabel),this.minScrollY=0):f.enablePullDown&&this.y<5&&h.hasClass("flip")?(h.show().removeClass("flip"),h.find(".pullDownLabel").html(f.pullDownlabel),this.minScrollY=-k):f.enablePullUp&&this.y<this.maxScrollY-5&&!i.hasClass("flip")?(i.addClass("flip"),i.find(".pullUpLabel").html(f.flipLabel),this.maxScrollY=this.maxScrollY):f.enablePullUp&&this.y>this.maxScrollY+5&&i.hasClass("flip")&&(i.show().removeClass("flip"),i.find(".pullUpLabel").html(f.pullUplabel)))},onScrollEnd:function(){l||(f.enablePullDown&&h.hasClass("flip")?(h.addClass("loading"),h.find(".pullDownLabel").html(f.loadingLabel),l=!0,f.pullDownAction(e)):f.enablePullUp&&i.hasClass("flip")&&(i.addClass("loading"),i.find(".pullUpLabel").html(f.loadingLabel),l=!0,f.pullUpAction(e)))}});return f.enablePullDown&&h.show(),f.enablePullUp&&i.show(),n.destroyMe=function(){f=null,h=null,i=null,k=null,pullUpOffset=null,l=null,n.destroy()},GC&&GC.push([n],"iScroll",g.closest("section")),n}return null},d=function(a){return new iScroll($(a)[0],{zoom:!0,checkDOMChanges:!0,useTransition:!1,hScrollbar:!0,vScrollbar:!0})};return{init:c,zoom:d}}(),$(function(){"android"==window.devicePlatform&&document.addEventListener("deviceready",function(){var a=null,b=null;window._keyboardIsShow=!1,document.body.addEventListener("focus",function(b){a=b.target||b.srcElement},!0),document.addEventListener("hidekeyboard",function(){b&&($(b.scroller).height("auto"),b.wrapper.scrollTop=0),a&&a.blur(),_keyboardIsShow=!1},!1),document.addEventListener("showkeyboard",function(){_keyboardIsShow=!0},!1),window.addEventListener("resize",function(){if(a&&_keyboardIsShow){a.scrollIntoView(!1);for(var c=$("section.active").attr("id")||"body",d=GC.get(c,"iScroll"),e=d.length-1;e>=0;e-=1)if((window._currentIscrollHeight=$(d[e].scroller).height())>0){b=d[e];break}}})},!1)}),function(a){ui.anim=function(b,c){for(var d,e,f,g=arguments.length,h=2;g>h;h++){var i=arguments[h],j=a.type(i);"number"==j?d=i:"string"==j?e=i:"function"==j?f=i:null}a(b).animate(c,d||250,e||"ease",f)},ui.showMask=function(a,b){ui.Popup.loading(a,b)},ui.hideMask=function(){ui.Popup.close(!0)},ui.alert=function(a,b){ui.Popup.alert(a,b)},ui.confirm=function(a,b,c,d,e,f){ui.Popup.confirm(a,b,c,d,e,f)},ui.popup=function(a){ui.Popup.show(a)},ui.closePopup=function(){ui.Popup.close()},ui.popover=function(a,b,c,d){ui.Popup.popover(a,b,c,d)},ui.refresh=function(){ui.Page.refresh()},ui.load=function(a){ui.Page.load(a)},ui.back=function(a){ui.Page.back(a)},ui.Tab=function(b){a(b.container).tab(b)}}(Zepto),ui.Popup=function(a){var b,c,d,e,f={top:{top:0,left:0,right:0},"top-second":{top:"70px",left:0,right:0},center:{top:"50%",left:"5%",right:"5%","border-radius":"3px"},bottom:{bottom:0,left:0,right:0},"bottom-second":{bottom:"72px",left:0,right:0}},g={top:["slideDownIn","slideUpOut"],bottom:["slideUpIn","slideDownOut"],defaultAnim:["bounceIn","bounceOut"]},h={alert:'<div class="popup-title">{title}</div><div class="popup-content">{content}</div><div id="popup_btn_container"><a data-target="closePopup" data-icon="checkmark">{ok}</a></div>',confirm:'<div class="popup-title">{title}</div><div class="popup-content">{content}</div><div id="popup_btn_container"><a class="cancel" data-icon="close">{cancel}</a><a data-icon="checkmark">{ok}</a></div>',loading:'<i class="icon {icon}"></i><p>{title}</p>',loadingOnlyIcon:'<i class="icon iconOnly {icon}"></i>'},i=function(){a("body").append('<div id="bingotouch_popup"></div><div id="bingotouch_popup_mask"></div>'),c=a("#bingotouch_popup_mask"),b=a("#bingotouch_popup"),l()},j=function(h){var i={height:void 0,width:void 0,opacity:.3,url:null,tplId:null,tplData:null,html:"",pos:"center",clickMask2Close:!0,showCloseBtn:!0,arrowDirection:void 0,animation:!0,timingFunc:"linear",duration:200,onShow:void 0};a.extend(i,h),e=i.clickMask2Close,c.css("opacity",i.opacity),b.attr({style:"","class":""}),i.width&&b.width(i.width),i.height&&b.height(i.height);var j=a.type(i.pos);if("object"==j)b.css(i.pos),d=g.defaultAnim;else{if("string"!=j)return void console.error("错误的参数！");if(f[i.pos]){b.css(f[i.pos]);var k=i.pos.indexOf("top")>-1?"top":i.pos.indexOf("bottom")>-1?"bottom":"defaultAnim";d=g[k]}else b.addClass(i.pos),d=g.defaultAnim}c.show();var l;if(i.html?l=i.html:i.url||i.tplId,i.showCloseBtn&&(l+='<div id="tag_close_popup" data-target="closePopup" class="icon icon-cancel"></div>'),i.arrowDirection&&(b.addClass("arrow "+i.arrowDirection),b.css("padding","8px"),("top"==i.arrowDirection||"bottom"==i.arrowDirection)&&(d=g[i.arrowDirection])),b.html(l).show(),i.onShow&&i.onShow.call(b),"center"==i.pos){var m=b.height();b.css("margin-top","-"+m/2+"px")}i.animation&&ui.anim(b,d[0],i.duration,i.timingFunc),ui.hasPopupOpen=!0},k=function(a){c.hide(),d&&!a?ui.anim(b,d[1],200,function(){b.hide().empty(),ui.hasPopupOpen=!1}):(b.hide().empty(),ui.hasPopupOpen=!1)},l=function(){c.on("tap",function(){e&&k()}).on("touchmove",function(a){a.preventDefault()}),b.on("tap",'[data-target="closePopup"]',function(){k()}).on("touchmove",function(a){a.preventDefault()})},m=function(a,b,c){var d=h.alert.replace("{title}",a).replace("{content}",b).replace("{ok}",c||"确定");j({html:d,pos:"center",clickMask2Close:!1,showCloseBtn:!1})},n=function(b,c,d,e,f,g){var i=h.confirm.replace("{title}",b).replace("{content}",c).replace("{cancel}",g||"取消").replace("{ok}",f||"确定");j({html:i,pos:"center",clickMask2Close:!1,showCloseBtn:!1}),a('#popup_btn_container [data-icon="checkmark"]').tap(function(){k(),d.call(this)}),a('#popup_btn_container [data-icon="close"]').tap(function(){k(),e.call(this)})},o=function(a,b,c,d){j({html:a,pos:b,showCloseBtn:!1,arrowDirection:c,onShow:d})},p=function(a,b){var c;c=null==a?h.loadingOnlyIcon.replace("{icon}",b||"icon-spinner5"):h.loading.replace("{title}",a).replace("{icon}",b||"icon-spinner5"),j({html:c,pos:"loading",opacity:.1,duration:10,clickMask2Close:!1})},q=function(b){var c='<div class="actionsheet">';a.each(b,function(a,b){c+='<div data-role="BTButton" style="background-color:'+b.color+'" data-status="1"><span class="btn-text">'+b.text+"</span></div>"}),c+='<div data-role="BTButton" data-theme="d" data-status="1"><span class="btn-text">取消</span></div>',c+="</div>",j({html:c,pos:"bottom",showCloseBtn:!1,onShow:function(){a(this).find('[data-role="BTButton"]').each(function(c,d){a(d).on("tap",function(){b[c]&&b[c].handler&&b[c].handler.call(d),k()})})}})};return a(function(){i()}),{show:j,close:k,alert:m,confirm:n,popover:o,loading:p,actionsheet:q}}(Zepto),ui.Skin=function(){var a="BINGOTOUCH-SKIN",b={BGCOLOR:"@selector { background-color : @bgColor !important;}",BGCOLORACTIVE:"@selector.btn-active { background-color : @bgColorActive !important;}",COLOR:"@selector { color : @color !important; }",BORDERCOLOR:"@selector {  border-color: @borderColor !important;}"},c=[".header .title",".footer .navbar [data-role='BTButton']"],d=[".header .title",".footer .navbar [data-role='BTButton']",".footer .sonmenu",".footer .angle"],e=function(b){var e={colorSelector:[],bgColorSelector:[],borderColorSelector:[],iconFontColorSelector:[],color:"#FFFFFF",bgColor:"#278cca",bgColorActive:null,borderColor:null,iconFontColor:"#333333",appendElement:"head"};$.extend(e,b),e.colorSelector=e.colorSelector.concat(c),e.bgColorSelector=e.bgColorSelector.concat(d),e.borderColorSelector=e.borderColorSelector.concat(d),e.borderColor||(e.borderColor=ui.Utils.getDarkColor(e.bgColor,.3)),e.bgColorActive||(e.bgColorActive=ui.Utils.getDarkColor(e.bgColor,.2)),$("#"+a).remove();var g="<style id='"+a+"'>";g+=f(e.colorSelector,"COLOR",{color:e.color}),g+=f(e.iconFontColorSelector,"COLOR",{color:e.iconFontColor}),g+=f(e.bgColorSelector,"BGCOLOR",{bgColor:e.bgColor}),g+=f(e.bgColorSelector,"BGCOLORACTIVE",{bgColorActive:e.bgColorActive}),g+=f(e.borderColorSelector,"BORDERCOLOR",{borderColor:e.borderColor}),g+="</style>",$(e.appendElement).append(g)},f=function(a,c,d){var e="";return $.each(a,function(a,f){var g=b[c].replace("@selector",f);for(var h in d)g=g.replace("@"+h,d[h]);e+=g}),e};return{init:e}}(),ui.Menu=function(a){var b=function(b){var d,f,g=200;b.duration&&(g=b.duration),d=a(b.sectionId?"#"+b.sectionId:"section"),a("#section_container_mask").remove(),f=a('<div id="section_container_mask"></div>').appendTo("#section_container"),f.on("tap",e),d.attr("MenuDuration",g).find("[data-aside]").each(function(){var b=a(this).attr("data-aside");a(this).off("tap").on("tap",function(){c("#"+b)})})},c=function(a){ui.hasMenuOpen?ui.Menu.hide():ui.Menu.show(a)},d=function(b){a("#section_container_mask").show();var c=a(b).addClass("active"),d=a("section.active"),e=1*d.attr("MenuDuration"),f=c.data("transition"),g=c.data("position")||"left",h=c.width(),h="scale"==f?h-.1*a(window).width():h,i="left"==g?h+"px":"-"+h+"px",j=function(){ui.hasMenuOpen=!0,"reveal"==f&&c.css("z-index",30)};if("overlay"==f)ui.anim(c,{translateX:"0%"},e,j);else if("reveal"==f)ui.anim(d,{translateX:i},e,j);else{ui.anim(c,{translateX:"0%"},e);var k={translateX:i};"scale"==f&&a.extend(k,{scale:.8}),ui.anim(d,k,e,j)}},e=function(b,c){if(ui.hasMenuOpen){var d=a("#section_container aside.active"),e=a("section.active"),b="number"==typeof b?b:1*e.attr("MenuDuration"),f=d.data("transition"),g=d.data("position")||"left",h="left"==g?"-100%":"100%",i=function(){d.removeClass("active"),ui.hasMenuOpen=!1,c&&c()};"overlay"==f?ui.anim(d,{translateX:h},b,i):"reveal"==f?(d.css("z-index",10),ui.anim(e,{translateX:"0"},b,i)):(ui.anim(d,{translateX:h},b),ui.anim(e,{translateX:"0"},b,i)),a("#section_container_mask").hide()}};return{init:b,show:d,hide:e}}(Zepto),ui.SwipeListview=function(){function a(a,c){var d=$(a),e=d.height();if(c.rightBtn.length>0&&0==d.find(".swipeRight").length){var f=b(c.rightBtn,"swipeRight",e);d.append(f)}if(c.leftBtn.length>0&&0==d.find(".swipeLeft").length){var f=b(c.leftBtn,"swipeLeft",e);d.prepend(f)}}function b(a,b,c){var d='<div class="swipe '+b+'">';return $.each(a,function(a,b){d+='<span i="'+a+'" style="background:'+b.color+";width:"+(b.width||100)+"px;height:"+c+'px;">'+b.title+"</span>"}),d+="</div>"}function c(a){var b=0;return $.each(a,function(a,c){b+=1*(c.width||100)}),b}function d(a,b,c,d){var f=e(a);switch(f){case"rightBtnShow":"right"==b&&(ui.anim($(a).find("[data-role='BTButton']"),{translateX:"0"},d),ui.anim($(a).find(".swipeRight"),{translateX:"100%"},d));break;case"normal":"right"==b?(ui.anim($(a).find(".swipeLeft"),{translateX:"0%"},d),ui.anim($(a).find("[data-role='BTButton']"),{translateX:c},d)):(ui.anim($(a).find("[data-role='BTButton']"),{translateX:c},d),ui.anim($(a).find(".swipeRight"),{translateX:"0%"},d));break;case"leftBtnShow":"left"==b&&(ui.anim($(a).find(".swipeLeft"),{translateX:"-100%"},d),ui.anim($(a).find("[data-role='BTButton']"),{translateX:"0"},d))}}function e(a){var b=$(a).find("[data-role='BTButton']").offset().left;return 0>b?"rightBtnShow":b>40?"leftBtnShow":"normal"}var f={duration:100,leftBtn:[],rightBtn:[]},g=function(b,e){var g=$(b);if(0!=g.length){var h=$.extend({},f,e);g.closest("ul").addClass("swipeListview"),g.delegate("li","tap",function(){i(this,h.duration)});var j=-1*c(h.rightBtn)+"px";g.delegate("li","swipeLeft",function(b){a(this,h),d(this,"left",j,h.duration),b.preventDefault()});var k=c(h.leftBtn)+"px";g.delegate("li","swipeRight",function(b){a(this,h),d(this,"right",k,h.duration),b.preventDefault()}),g.delegate(".swipe>span","tap",function(){var a=$(this).parent().hasClass("swipeLeft"),b=$(this).attr("i"),c=$(this).parent().siblings("[data-role='BTButton']")[0];return a?h.leftBtn[b].onTap(c):h.rightBtn[b].onTap(c),!1})}},h=function(a,b){$(a).trigger("swipe"+(b||"Left"))},i=function(a,b){b=b||f.duration,ui.anim($(a).find(".swipeRight"),{translateX:"100%"},b),ui.anim($(a).find("[data-role='BTButton']"),{translateX:"0px"},b),ui.anim($(a).find(".swipeLeft"),{translateX:"-100%"},b)};return{init:g,show:h,hide:i}}(),function($){$.validationConfig={allRules:{required:{executor:"_required"},pattern:{executor:"_customRegex"},func:{executor:"_funcCall"},length:{executor:"_length"},range:{executor:"_range"},equalToField:{executor:"_confirm",alertText:"输入值与相关信息不相符"},url:{regex:/^(http|https|ftp):\/\/(([A-Z0-9][A-Z0-9_-]*)(\.[A-Z0-9][A-Z0-9_-]*)+)(:(\d+))?\/?/i,executor:"_customRegex",alertText:"网址输入不正确"},qq:{regex:/^[1-9][0-9]{4,}$/,executor:"_customRegex",alertText:"QQ号码输入不正确"},telephone:{regex:/^(\(0\d{2,3}\)|0\d{2,3}-)?[1-9]\d{6,7}(\-\d{1,4})?$/,executor:"_customRegex",alertText:"电话号码输入不正确"},mobile:{regex:/^1[3|5|8]\d{9}$/,executor:"_customRegex",alertText:"手机号码输入不正确"},email:{regex:/^[a-zA-Z0-9_\.\-]+\@([a-zA-Z0-9\-]+\.)+[a-zA-Z0-9]{2,4}$/,executor:"_customRegex",alertText:"邮箱地址输入不正确"},date:{regex:/^[0-9]{4}\-[0-9]{1,2}\-[0-9]{1,2}$/,executor:"_customRegex",alertText:"日期输入格式不正确（YYYY-MM-DD）"},identity:{regex:/^([0-9]{17}[0-9X]{1})|([0-9]{15})$/,executor:"_customRegex",alertText:"身份证输入不正确"},money:{regex:/^[0-9]+(.[0-9]{2})?$/,executor:"_customRegex",alertText:"金额格式输入不正确"},integer:{regex:/^\d+$/,executor:"_customRegex",alertText:"输入值必须是正整数"},"double":{regex:/^[0-9]+(.[0-9]+)?$/,executor:"_customRegex",alertText:"输入值必须是数值"},digit:{regex:/^[0-9]+$/,executor:"_customRegex",alertText:"只能输入数字"},noSpecialCaracters:{regex:/^[0-9a-zA-Z]+$/,executor:"_customRegex",alertText:"不允许输入字母和数字之外的特殊字符"},letter:{regex:/^[a-zA-Z]+$/,executor:"_customRegex",alertText:"只允许输入英文"},chinese:{regex:/^[\u0391-\uFFE5]+$/,executor:"_customRegex",alertText:"只允许输入中文"}},addRules:function(a){$.extend($.validationConfig.allRules,a)}};var validtorAttr="[data-validator]",validtorAttrValue="data-validator";$.fn.validation=function(a){function b(a,b){$.validation.loadValidation(a,b,c)}a=a||{},a.promptType||(a.promptType="inline");var c=$.extend({validationEventTriggers:"focusout",isPromptAll:!0,autoValidate:!1,buildPrompt:$.validatePrompt[a.promptType].build,closePrompt:$.validatePrompt[a.promptType].close},a),d=$(this).data("isInitClosePromptEvent");d||($(this).data("isInitClosePromptEvent",!0),$(this).find("input"+validtorAttr+",textarea"+validtorAttr).each(function(){$(this).bind("focus",function(){c.closePrompt(this)})}));var e=$(this).data("isInitAutoValidate");return!c.autoValidate||e?$.validation.validate(this,c):($(this).data("isInitAutoValidate",!0),$(this).find("input"+validtorAttr+",textarea"+validtorAttr).each(function(){var a=$(this).attr("event")||c.validationEventTriggers;$(this).bind(a,function(){b(this,a)})}),$(this).find(validtorAttr+"[data-role=BTCheck],"+validtorAttr+"[data-role=BTRadio]").each(function(){$(this).bind("tap",function(){b(this,"tap")})}),void 0)},$.validation={loadValidation:function(a,b,c){var d=new Array,e=$(a).attr(validtorAttrValue);if(!e)return!0;var f=e.match(/\[.+\]/g);return f&&$.each(f,function(a){e=e.replace(this,"##"+a)}),e=e.split(","),$.each(e,function(){var a=this.split("##");d.push(a&&2==a.length?{name:a[0],options:f[a[1]].replace(/^\[|\]$/g,"").split(",")}:{name:a[0],options:[]})}),$.validation.validateCall(a,d,b,c)},validateCall:function(caller,rules,eventType,settings){function _required(a,b){var c=$(a).attr("type"),d=$(a)[0].tagName,e=$(a).attr("data-role");"text"==c||"password"==c||"TEXTAREA"==d||"hidden"==c?$.trim($(a).val())||(isError=!0,promptText+=_buildPromptText("该输入项必填",b.options[0])):("BTRadio"==e||"BTCheck"==e)&&(callerName=$(a).attr("name"),0==$("[name='"+callerName+"'].BTCheck_ON").size()&&(isError=!0,promptText+=1==$("[name='"+callerName+"']").size()?_buildPromptText("该选项为必选项",b.options[0]):_buildPromptText("必须选择一个选项",b.options[0])))}function _customRegex(caller,rule){if(_isValueEmpty(caller))return!1;var customRule=rule.name,pattern;pattern="pattern"==customRule?{regex:rule.options[0],alertText:"输入内容不规范"}:$.validationConfig.allRules[customRule],pattern.regex="string"==typeof pattern.regex?new RegExp(pattern.regex):eval(pattern.regex),pattern.regex.test($.trim($(caller).val()))||(isError=!0,promptText+=_buildPromptText(pattern.alertText))}function _funcCall(a,b,c){var d=b.options[0],e=window[d];if("function"==typeof e){var f=e(a,c);f.isError&&(isError=!0,promptText+=_buildPromptText(f.errorInfo))}}function _confirm(a,b){var c=b.options[0],d=$("[name='"+c+"']")[0]||$("#"+c)[0];$(a).val()!=$(d).val()?(isError=!0,promptText+=_buildPromptText($.validationConfig.allRules[b.name].alertText,b.options[1])):settings.closePrompt(a)}function _length(a,b){if(_isValueEmpty(a))return!1;var c=b.options[0],d=b.options[1],e=$.trim($(a).val()).length;if(c&&c>e||d&&e>d){isError=!0;var f="";c&&(f="至少"+c),d&&(f+="最多"+d),promptText+=_buildPromptText("已输入"+Math.ceil(e)+"字符，请输入"+f+"字符",b.options[2])}}function _range(a,b){var c=b.options[0],d=b.options[1],e=($(a).attr("type"),$(a).attr("data-role"));if("BTRadio"==e||"BTCheck"==e){var f=$("[name='"+$(a).attr("name")+"'].BTCheck_ON").size();if(c>f||f>d){isError=!0;var g=[];c&&g.push(c),d&&g.push(d),promptText+=_buildPromptText("必须选择"+g.join("到")+"个选项",b.options[2])}}else{if(_isValueEmpty(a))return!1;var h=parseFloat($.trim($(a).val()))||0;(c>h||h>d)&&(isError=!0,promptText+=_buildPromptText("输入的值必须在"+c+"到"+d+"之间",b.options[2]))}}function _buildPromptText(a,b){return b?b:a}function _isValueEmpty(a){return!($(a).val()&&$.trim($(a).val()).length>0)}var promptText="",callerName=$(caller).attr("name"),errorInfo=$(caller).attr("errorInfo"),isError=!1;return $.each(rules,function(i,v){var validator=$.validationConfig.allRules[this.name];return validator&&eval(validator.executor+"(caller,this,eventType)"),isError?!1:void 0}),1==isError?(promptText=errorInfo||promptText,settings.buildPrompt(caller,promptText)):settings.closePrompt(caller),isError},validate:function(a,b){var c=!1,d="";return $(a).find(validtorAttr).each(function(){var a=$.validation.loadValidation(this,null,b);return a&&""==d&&(c=!0,d=$(this).attr("errorInfo")),b.isPromptAll||!a}),{isError:c,errorInfo:d}}},$.validatePrompt={inline:{build:function(a,b){var c='<label class="errorInfo">'+b+"</label>";$(a).siblings(".errorInfo").remove(),$(a).parent().append(c)},close:function(a){$(a).siblings(".errorInfo").remove()}},bubble:{build:function(a,b){var c='<label class="errorInfo bubble"><div class="error-inner">'+b+'</div><span class="ag"></span></label>',d=$(a).parent().offset(),e=$(a).attr("data-role"),f=$(c).css({"max-width":d.width,top:void 0==e?d.height/2:d.height});$(a).siblings(".errorInfo").remove(),f.appendTo($(a).parent()).css("left",d.width/2-f.width()/2)},close:function(a){$(a).siblings(".errorInfo").remove()}}}}(Zepto),function(a){a.extend(a,{contains:function(a,b){return a.compareDocumentPosition?!!(16&a.compareDocumentPosition(b)):a!==b&&a.contains(b)}})}(Zepto),function(a,b){a.extend(a,{toString:function(a){return Object.prototype.toString.call(a)},slice:function(a,b){return Array.prototype.slice.call(a,b||0)},later:function(a,b,c,d,e){return window["set"+(c?"Interval":"Timeout")](function(){a.apply(d,e)},b||0)},parseTpl:function(a,b){var c="var __p=[],print=function(){__p.push.apply(__p,arguments);};with(obj||{}){__p.push('"+a.replace(/\\/g,"\\\\").replace(/'/g,"\\'").replace(/<%=([\s\S]+?)%>/g,function(a,b){return"',"+b.replace(/\\'/g,"'")+",'"}).replace(/<%([\s\S]+?)%>/g,function(a,b){return"');"+b.replace(/\\'/g,"'").replace(/[\r\n\t]/g," ")+"__p.push('"}).replace(/\r/g,"\\r").replace(/\n/g,"\\n").replace(/\t/g,"\\t")+"');}return __p.join('');",d=new Function("obj",c);return b?d(b):d},throttle:function(c,d,e){function f(){function a(){h=Date.now(),d.apply(i,k)}function f(){g=b}var i=this,j=Date.now()-h,k=arguments;e&&!g&&a(),g&&clearTimeout(g),e===b&&j>c?a():g=setTimeout(e?f:a,e===b?c-j:c)}var g,h=0;return"function"!=typeof d&&(e=d,d=c,c=250),f._zid=d._zid=d._zid||a.proxy(d)._zid,f},debounce:function(c,d,e){return d===b?a.throttle(250,c,!1):a.throttle(c,d,e===b?!1:e!==!1)}}),a.each("String Boolean RegExp Number Date Object Null Undefined".split(" "),function(d,e){var f;if(!("is"+e in a)){switch(e){case"Null":f=function(a){return null===a};break;case"Undefined":f=function(a){return a===b};break;default:f=function(a){return new RegExp(e+"]","i").test(c(a))}}a["is"+e]=f}});var c=a.toString}(Zepto),function(a){var b=navigator.userAgent,c=navigator.appVersion,d=a.browser;a.extend(d,{qq:/qq/i.test(b),uc:/UC/i.test(b)||/UC/i.test(c)}),d.uc=d.uc||!d.qq&&!d.chrome&&!d.firefox&&!/safari/i.test(b);try{d.version=d.uc?c.match(/UC(?:Browser)?\/([\d.]+)/)[1]:d.qq?b.match(/MQQBrowser\/([\d.]+)/)[1]:d.version}catch(e){}a.support=a.extend(a.support||{},{orientation:!(d.uc||parseFloat(a.os.version)<5&&(d.qq||d.chrome))&&!(a.os.android&&parseFloat(a.os.version)>3)&&"orientation"in window&&"onorientationchange"in window,touch:"ontouchend"in document,cssTransitions:"WebKitTransitionEvent"in window,has3d:"WebKitCSSMatrix"in window&&"m11"in new WebKitCSSMatrix})}(Zepto),function(a){function b(){a(window).on("scroll",a.debounce(80,function(){a(document).trigger("scrollStop")},!1))}function c(){a(window).off("scroll"),b()}a(document).ready(function(){var b,c,d=function(){var a=document.documentElement;return a.clientWidth/Math.max(a.clientHeight,320)<1.1?"portrait":"landscape"},e=d(),f=function(){c=20,clearInterval(b),b=a.later(function(){var f=d();e!==f?(e=f,clearInterval(b),a(window).trigger("ortchange")):--c&&clearInterval(b)},50,!0)};a(window).bind(a.support.orientation?"orientationchange":"resize",a.debounce(f))}),b(),a(window).on("pageshow",function(b){b.persisted&&a(document).off("touchstart",c).one("touchstart",c)})}(Zepto),function(a,b){function c(){return h++}function d(b,c){var d={};return Object.create?d=Object.create(b):d.__proto__=b,a.extend(d,c||{})}function e(b,c){return c&&(f(b,c),a.extend(b.prototype,c)),a.extend(b,{plugins:[],register:function(b){return a.isObject(b)?void a.extend(this.prototype,b):void this.plugins.push(b)}})}function f(b,c){var e,f=c.inherit||l,g=f.prototype;return e=b.prototype=d(g,{$factory:b,$super:function(b){var c=g[b];return a.isFunction(c)?c.apply(this,a.slice(arguments,1)):c}}),e._data=a.extend({},g._data,c._data),delete c._data,b}function g(c){a.fn[c]=function(d){var e,f,g=a.slice(arguments,1);return a.each(this,function(h,i){return f=k(i,c)||a.ui[c](i,a.extend(a.isPlainObject(d)?d:{},{setup:!0})),e=a.isString(d)&&a.isFunction(f[d])?f[d].apply(f,g):b,e!==b&&e!==f||"this"===d&&(e=f)?!1:void(e=b)}),e!==b?e:this}}var h=1,i=function(){},j="<%=name%>-<%=id%>",k=function(){var b={},c=0,d="GMUWidget"+ +new Date;return function(e,f,g){var h=e[d]||(e[d]=++c),i=b[h]||(b[h]={});return!a.isUndefined(g)&&(i[f]=g),a.isNull(g)&&delete i[f],i[f]}}();a.ui=a.ui||{version:"2.0.3",guid:c,define:function(b,f,h){h&&(f.inherit=h);var i=a.ui[b]=e(function(e,f){var g=d(i.prototype,{_id:a.parseTpl(j,{name:b,id:c()})});return g._createWidget.call(g,e,f,i.plugins),g},f);return g(b,i)},isWidget:function(c,d){return c instanceof(d===b?l:a.ui[d]||i)}};var l=function(){};a.extend(l.prototype,{_data:{status:!0},data:function(b,c){var d=this._data;return a.isObject(b)?a.extend(d,b):a.isUndefined(c)?d[b]:d[b]=c},_createWidget:function(c,d,e){a.isObject(c)&&(d=c||{},c=b);var f=a.extend({},this._data,d);a.extend(this,{_el:c?a(c):b,_data:f});var g=this;a.each(e,function(b,c){var d=c.apply(g);if(d&&a.isPlainObject(d)){var e=g._data.disablePlugin;(!e||a.isString(e)&&!~e.indexOf())&&(delete d.pluginName,a.each(d,function(b,c){var d;g[b]=(d=g[b])&&a.isFunction(c)?function(){return g[b+"Org"]=d,c.apply(g,arguments)}:c}))}}),f.setup?this._setup(c&&c.getAttribute("data-mode")):this._create(),this._init();var g=this,h=this.trigger("init").root();h.on("tap",function(a){(a.bubblesList||(a.bubblesList=[])).push(g)}),k(h[0],g._id.split("-")[0],g)},_create:function(){},_setup:function(){},root:function(a){return this._el=a||this._el},id:function(a){return this._id=a||this._id},destroy:function(){var b,c=this;b=this.trigger("destroy").off().root(),b.find("*").off(),k(b[0],c._id.split("-")[0],null),b.off().remove(),this.__proto__=null,a.each(this,function(a){delete c[a]})},on:function(b,c){return this.root().on(b,a.proxy(c,this)),this},off:function(a,b){return this.root().off(a,b),this},trigger:function(b,c){b=a.isString(b)?a.Event(b):b;var d,e=this.data(b.type);return e&&a.isFunction(e)&&(b.data=c,d=e.apply(this,[b].concat(c)),d===!1||b.defaultPrevented)?this:(this.root().trigger(b,c),this)}})}(Zepto),function(a){a.ui.define("slider",{_data:{viewNum:1,imgInit:2,itemRender:null,imgZoom:!1,loop:!0,stopPropagation:!1,springBackDis:15,autoPlay:!0,autoPlayTime:4e3,animationTime:400,showArr:!1,showDot:!0,slide:null,slideend:null,index:0,_stepLength:1,_direction:1},_create:function(){var b,c=this,d=0,e=[],f=c.data("content");c._initConfig(),(c.root()||c.root(a("<div></div>"))).addClass("ui-slider").appendTo(c.data("container")||(c.root().parent().length?"":document.body)).html('<div class="ui-slider-wheel"><div class="ui-slider-group">'+function(){if(c.data("itemRender"))for(var a=c.data("itemRender");b=a.call(c,d++);)e.push('<div class="ui-slider-item">'+b+"</div>");else for(;b=f[d++];)e.push('<div class="ui-slider-item"><a href="'+b.href+'"><img lazyload="'+b.pic+'"/></a>'+(b.title?"<p>"+b.title+"</p>":"")+"</div>");return e.push(c.data("loop")?'</div><div class="ui-slider-group">'+e.join("")+"</div></div>":"</div></div>"),e.join("")}()),c._addDots()},_setup:function(b){var c=this,d=c.root().addClass("ui-slider");if(c._initConfig(),b)c.data("loop")&&a(".ui-slider-wheel",d).append(a(".ui-slider-group",d).clone());else{var e=d.children(),f=a('<div class="ui-slider-group"></div>').append(e.addClass("ui-slider-item"));d.empty().append(a('<div class="ui-slider-wheel"></div>').append(f).append(c.data("loop")?f.clone():"")),c._addDots()}},_init:function(){var b=this,c=(b.data("index"),b.root()),d=a.proxy(b._eventHandler,b);b._setWidth(),a(b.data("wheel")).on("touchstart touchmove touchend touchcancel webkitTransitionEnd",d),a(window).on("ortchange",d),a(".ui-slider-pre",c).on("tap",function(){b.pre()}),a(".ui-slider-next",c).on("tap",function(){b.next()}),b.on("destroy",function(){clearTimeout(b.data("play")),a(window).off("ortchange",d)}),b.data("autoPlay")&&b._setTimeout()},_initConfig:function(){var a=this._data;a.viewNum>1&&(a.loop=!1,a.showDot=!1,a.imgInit=a.viewNum+1)},_addDots:function(){var b=this,c=b.root(),d=a(".ui-slider-item",c).length/(b.data("loop")?2:1),e=[];if(b.data("showDot")){for(e.push('<p class="ui-slider-dots">');d--;)e.push("<b></b>");e.push("</p>")}b.data("showArr")&&e.push('<span class="ui-slider-pre"><b></b></span><span class="ui-slider-next"><b></b></span>'),c.append(e.join(""))},_setWidth:function(){var b,c,d=this,e=d._data,f=d.root(),g=Math.ceil(f.width()/e.viewNum),h=f.height(),i=e.loop,j=a(".ui-slider-item",f).toArray(),k=j.length,l=a(".ui-slider-wheel",f).width(g*k)[0],m=a(".ui-slider-dots b",f).toArray(),n=a("img",f).toArray(),o=n.concat(),p={},q=e.imgInit||k;for(e.showDot&&(m[0].className="ui-slider-dot-select"),e.imgZoom&&a(o).on("load",function(){var a=this.height,b=this.width,c=Math.min(a,h),d=Math.min(b,g);this.style.cssText+=a/h>b/g?"height:"+c+"px;width:"+c/a*b+"px;":"height:"+d/b*a+"px;width:"+d+"px",this.onload=null}),b=0;k>b;b++)j[b].style.cssText+="width:"+g+"px;position:absolute;-webkit-transform:translate3d("+b*g+"px,0,0);z-index:"+(900-b),p[b]=i&&b>k/2-1?b-k/2:b,q>b&&(c=o.shift(),c&&(c.src=c.getAttribute("lazyload")),e.loop&&(c=n[b+k/2],c&&(c.src=c.getAttribute("lazyload"))));return d.data({root:f[0],wheel:l,items:j,lazyImgs:o,allImgs:n,length:k,width:g,height:h,dots:m,dotIndex:p,dot:m[0]}),d},_eventHandler:function(a){var b=this;switch(a.type){case"touchmove":b._touchMove(a);break;case"touchstart":b._touchStart(a);break;case"touchcancel":case"touchend":b._touchEnd();break;case"webkitTransitionEnd":b._transitionEnd();break;case"ortchange":b._resize.call(b)}},_touchStart:function(a){var b=this;b.data({pageX:a.touches[0].pageX,pageY:a.touches[0].pageY,S:!1,T:!1,X:0}),b.data("wheel").style.webkitTransitionDuration="0ms"},_touchMove:function(a){var b=this._data,c=b.X=a.touches[0].pageX-b.pageX;if(!b.T){var d=b.index,e=b.length,f=Math.abs(c)<Math.abs(a.touches[0].pageY-b.pageY);b.loop&&(b.index=d>0&&e-1>d?d:d===e-1&&0>c?e/2-1:0===d&&c>0?e/2:d),f||clearTimeout(b.play),b.T=!0,b.S=f}b.S||(b.stopPropagation&&a.stopPropagation(),a.preventDefault(),b.wheel.style.webkitTransform="translate3d("+(c-b.index*b.width)+"px,0,0)")},_touchEnd:function(){var a=this,b=a._data;if(!b.S){var c=b.springBackDis,d=b.X<=-c?Math.ceil(-b.X/b.width):b.X>c?-Math.ceil(b.X/b.width):0;b._stepLength=Math.abs(d),a._slide(b.index+d)}},_slide:function(b,c){var d=this,e=d._data,f=e.length,g=f-e.viewNum+1;return b>-1&&g>b?d._move(b):b>=g?e.loop?(e.wheel.style.cssText+="-webkit-transition:0ms;-webkit-transform:translate3d(-"+(f/2-1)*e.width+"px,0,0);",e._direction=1,a.later(function(){d._move(f/2)},20)):(d._move(g-(c?2:1)),e._direction=-1):(e.loop?(e.wheel.style.cssText+="-webkit-transition:0ms;-webkit-transform:translate3d(-"+f/2*e.width+"px,0,0);",a.later(function(){d._move(f/2-1)},20)):d._move(c?1:0),e._direction=1),d},_move:function(a){var b=this._data,c=b.dotIndex[a];if(this.trigger("slide",c),b.lazyImgs.length){var d=b.allImgs[a];d&&d.src||(d.src=d.getAttribute("lazyload"))}b.showDot&&(b.dot.className="",b.dots[c].className="ui-slider-dot-select",b.dot=b.dots[c]),b.index=a,b.wheel.style.cssText+="-webkit-transition:"+b.animationTime+"ms;-webkit-transform:translate3d(-"+a*b.width+"px,0,0);"},_transitionEnd:function(){var a=this,b=a._data;if(a.trigger("slideend",b.dotIndex[b.index]),b.lazyImgs.length){for(var c=b._stepLength,d=0;c>d;d++){var e=b.lazyImgs.shift();e&&(e.src=e.getAttribute("lazyload")),b.loop&&(e=b.allImgs[b.index+b.length/2],e&&!e.src&&(e.src=e.getAttribute("lazyload")))}b._stepLength=1}a._setTimeout()},_setTimeout:function(){var b=this,c=b._data;return c.autoPlay?(clearTimeout(c.play),c.play=a.later(function(){b._slide.call(b,c.index+c._direction,!0)},c.autoPlayTime),b):b},_resize:function(){var a=this,b=a._data,c=b.root.offsetWidth/b.viewNum,d=b.length,e=b.items;if(!c)return a;b.width=c,clearTimeout(b.play);for(var f=0;d>f;f++)e[f].style.cssText+="width:"+c+"px;-webkit-transform:translate3d("+f*c+"px,0,0);";return b.wheel.style.removeProperty("-webkit-transition"),b.wheel.style.cssText+="width:"+c*d+"px;-webkit-transform:translate3d(-"+b.index*c+"px,0,0);",b._direction=1,a._setTimeout(),a},pre:function(){var a=this;return a._slide(a.data("index")-1),a
},next:function(){var a=this;return a._slide(a.data("index")+1),a},stop:function(){var a=this;return clearTimeout(a.data("play")),a.data("autoPlay",!1),a},resume:function(){var a=this;return a.data("_direction",1),a.data("autoPlay",!0),a._setTimeout(),a}})}(Zepto),function(a){var b='<% for (var i=0, len=left.length; i<len; i++) { %><a href="<%=left[i].url%>" class="ui-navigator-fix ui-navigator-fixleft"><%=left[i].text%></a><% } %><ul class="ui-navigator-list"><% for (var i=0, len=mid.length; i<len; i++) { %><li><a href="<%=mid[i].url%>"><%=mid[i].text%></a></li><% } %></ul><% for (var i=0, len=right.length; i<len; i++) { %><a href="<%=right[i].url%>" class="ui-navigator-fix ui-navigator-fixright"><%=right[i].text%></a><% } %>';a.ui.define("navigator",{_data:{container:"",content:[],defTab:0,beforetabselect:null,tabselect:null},_create:function(){var c,d=this,e=d._data,f=d.root(),g=a(e.container||document.body).get(0),h={left:[],mid:[],right:[]};a.each(e.content,function(){h[this.pos?this.pos:"mid"].push(this)}),c=a.parseTpl(b,h),f?(f.append(c),(!f.parent().length||g!==document.body)&&f.appendTo(g)):d.root(a("<div></div>").append(c)).appendTo(g)},_setup:function(b){var c=this,d=c._data,e=d.defTab,f=c.root();b||(f.children("a").addClass("ui-navigator-fix"),f.children("ul").addClass("ui-navigator-list")),f.find("a").each(function(b){0===e?a(this).hasClass("cur")&&(d.defTab=b):a(this).removeClass("cur")})},_init:function(){var b=this,c=b._data,d=b.root(),e=c.content,f=d.find("a");f.each(function(b){this.index=b,e.length&&e[b].attr&&a(this).attr(e[b].attr)}),c._$tabList=f,c._lastIndex=-1,d.addClass("ui-navigator").on("click",a.proxy(b._switchTabHandler,b)),b.switchTo(c.defTab,!0)},_switchTabHandler:function(b){var c=this,d=b.target;return a(d).closest("a").get(0)&&c.switchTo(d.index,!1,b),c},switchTo:function(b,c,d){var e=this,f=e._data,g=f._lastIndex,h=f._$tabList,i=a.Event("beforetabselect");return e.trigger(i,[h[b]]),i.defaultPrevented?(d&&d.preventDefault(),e):g==b?(d&&d.preventDefault(),e):(g>=0&&h.eq(g).removeClass("cur"),h.eq(b).addClass("cur"),f._lastIndex=b,e.trigger("tabselect",[h.get(b),b]))},getCurTab:function(){var a=this,b=a._data,c=b._lastIndex;return{index:c,info:b._$tabList[c]}}})}(Zepto),function(a,b){a.ui.navigator.register(function(){return{pluginName:"iscroll",_init:function(){return this._adjustHtml()._reBindEvent()._initOrg()},_reBindEvent:function(){var c=this,d=c._data;return d.isScrollToNext=d.isScrollToNext===b?!0:d.isScrollToNext,d.isShowShadow=d.isShowShadow===b?!0:d.isShowShadow,c._loadIscroll(),a(window).on("ortchange",a.proxy(c._ortChangeHandler,c)),c.on("destroy",function(){a(window).off("ortchange",c._ortChangeHandler),d.iScroll.destroy()}),c},_adjustHtml:function(){var b=this,c=b._data,d=b.root().addClass("ui-navigator"),e=d.find("ul"),f=d.find(".ui-navigator-wrapper"),g=e.find("li"),h=[0];return!f.length&&e.wrap('<div class="ui-navigator-wrapper"></div>'),e.find("li").each(function(a){h[a]=a?h[a-1]+this.offsetWidth:h[a]+this.offsetLeft-e[0].offsetLeft+this.offsetWidth}),a.extend(c,{_$navWrapper:d.find(".ui-navigator-wrapper"),_$navScroller:e.width(h[g.length-1]),_$navList:g,_scrollerNum:g.length,_scrollerSumWidth:h,_$fixElemLeft:d.find(".ui-navigator-fixleft"),_$fixElemRight:d.find(".ui-navigator-fixright")}),b},_loadIscroll:function(){var b=this,c=b._data;return c.iScroll=new iScroll(c._$navWrapper.get(0),c.iScrollOpts=a.extend({hScroll:!0,vScroll:!1,hScrollbar:!1,vScrollbar:!1},c.iScrollOpts,{onScrollStart:function(a){b.trigger("scrollstart",a)},onScrollMove:function(a){b.trigger("scrollmove",a)},onScrollEnd:function(a){c.isShowShadow&&b._setShadow(),b.trigger("scrollend",a)}})),b},_setShadow:function(){var a=this,b=a._data,c=b._$navWrapper,d={left:"ui-navigator-shadowl",right:"ui-navigator-shadowr",all:"ui-navigator-shadowall"},e=b.iScroll,f=e.x;return 0>f?(c.removeClass(d.left+" "+d.right).addClass(d.all),f<=e.maxScrollX&&c.removeClass(d.all+" "+d.right).addClass(d.left)):(c.removeClass(d.all+" "+d.left),e.hScroll?c.addClass(d.right):c.removeClass(d.all+" "+d.left+" "+d.right)),a},_scrollToNext:function(a,b){var c=this,d=c._data,e=d._scrollerSumWidth,f=d.iScroll;return f.scrollTo("last"==b?f.wrapperW-(e[a+1]||e[e.length-1]):"first"==b?-e[a-2]||0:f.x,0,400),c},_getPos:function(a){var b=this,c=b._data,d=c.iScroll,e=Math.abs(d.x)||0,f=c._scrollerSumWidth,g=c._$navList,h=f[a]-e,i=f[a-1||0]-e,j=(f[a+1]||f[f.length-1])-e,k=d.wrapperW;return h>=k||j>k?"last":h<=g[a].offsetWidth||i<g[a-1].offsetWidth?"first":"middle"},_ortChangeHandler:function(){var a=this,b=a._data,c=b.iScroll;c.refresh(),a._setShadow(),b._$navWrapper.width(c.wrapperW-c.wrapperOffsetLeft)},switchTo:function(a,b,c){var d=this,e=d._data;if(d.switchToOrg(a,b,c),!e._$tabList.eq(a).hasClass("ui-navigator-fix")){var f=e._$fixElemLeft,a=a-(f.length?f.length:0),g=d._getPos(a);b&&e.isShowShadow&&d._setShadow(),e.isScrollToNext&&d._scrollToNext(a,g)}return d}}})}(Zepto),function(a,b){a.ui.define("tab",{_data:{index:0,springBackDis:15,animationTime:400,draggable:!1,onBeforeToggle:function(){return!0},onAfterToggle:function(){}},_setup:function(){{var b=this,c=b.root(),d=c.find("[data-role=BTButton]").attr("data-tab"),e=a("#"+d).closest(".ui-tab");e.children()}0==e.length&&alert("初始化tab失败,请查看最新版本示例！"),e.wrapInner("<div class='ui-tab-wheel'>"),b.data({tabs:e})},_init:function(){var b=this,c=(b.data("index"),b.root()),d=b._data,e=a.proxy(b._tabEventHandler,b);b._setWidth(),d.draggable&&a(d.tabs).on("touchstart touchmove touchend touchcancel webkitTransitionEnd",e),a(".navbar > ul > li ,.navbar table tr td ",c).unbind("tap").bind("tap",function(a){b._navbarEventHandler.call(this,b),a.stopPropagation()})},_setWidth:function(){var b,c=this,d=c._data,e=d.tabs,f=e.width(),g=e.height(),h=a("tab",e).toArray(),i=h.length,j=a(".ui-tab-wheel",e).width(f*i)[0];for(b=0;i>b;b++)h[b].style.cssText+="width:"+f+"px;position:absolute;-webkit-transform:translate3d("+b*f+"px,0,0);z-index:"+(80-b);return c.data({root:e[0],wheel:j,items:h,length:i,width:f,height:g,wheelX:-f*(i-1)}),c},_navbarEventHandler:function(b){var c=this,d=b._data,e=a(c).find("[data-role=BTButton]").attr("data-tab"),f=a(c).closest("ul").find("li").index(a(c)),g=d.onBeforeToggle(e);g&&b._move(f,0)},_tabEventHandler:function(a){var b=this;switch(a.type){case"touchmove":b._touchMove(a);break;case"touchstart":b._touchStart(a);break;case"touchcancel":case"touchend":b._touchEnd();break;case"webkitTransitionEnd":b._transitionEnd()}},_touchStart:function(a){var b=this;b.data({pageX:a.touches[0].pageX,pageY:a.touches[0].pageY,S:!1,T:!1,X:0,isExecBeforeFunc:!1,beforeFuncResult:!1}),b.data("wheel").style.webkitTransitionDuration="0ms"},_touchMove:function(a){var b=this._data,c=b.X=a.touches[0].pageX-b.pageX;if(!b.T){var d=(b.index,b.length,Math.abs(c)<Math.abs(a.touches[0].pageY-b.pageY));b.T=!0,b.S=d}if(!b.S){b.stopPropagation&&a.stopPropagation(),a.preventDefault();var e=c-b.index*b.width;0>e&&e>b.wheelX&&(b.isExecBeforeFunc||(b.beforeFuncResult=b.onBeforeToggle(),b.isExecBeforeFunc=!0),b.beforeFuncResult&&(b.wheel.style.webkitTransform="translate3d("+e+"px,0,0)"))}},_touchEnd:function(){var a=this,b=a._data;if(!b.S){var c=b.springBackDis,d=b.X<=-c?Math.ceil(-b.X/b.width):b.X>c?-Math.ceil(b.X/b.width):0;b._stepLength=Math.abs(d),b.beforeFuncResult&&a._slide(b.index+d)}},_slide:function(a){var b=this,c=b._data,d=c.length,e=d;return a>-1&&e>a?b._move(a):a>=e?(b._move(e-1),c._direction=-1):(b._move(0),c._direction=1),b},_move:function(a,c){var d=this,e=d._data;e.index=a,d._toggleNavbar(a,function(){c===b&&(c=e.animationTime),e.wheel.style.cssText+="-webkit-transition:"+c+"ms;-webkit-transform:translate3d(-"+a*e.width+"px,0,0);"})},_transitionEnd:function(){{var a=this;a._data}},_toggleNavbar:function(b,c){var d=this,e=d._data,f=d.root();$li=f.find("li:eq("+b+")"),tabId=$li.find("[data-role=BTButton]").attr("data-tab"),$li.siblings().find("[data-role=BTButton]").removeClass("btn-active"),$li.find("[data-role=BTButton]").addClass("btn-active"),c();for(var g=a("section.active").attr("id")||"body",h=GC.get(g,"iScroll"),i=h.length-1;i>=0;i-=1)h[i].animating=a(h[i].wrapper).parent("#"+tabId).length>0?!1:!0;e.onAfterToggle(tabId)}})}(Zepto);