﻿
isProxy = false;

var isOnline = true;//正式 true

if (isOnline) {
     //获取应用接口地址
     var applist_url = "http://10.248.97.236/supp/httpClient/msa_xhyzd_app/app/";
     //通知通告，工作动态地址
     var announcement_url = "http://10.248.97.236/supp/httpClient/xinghuo-admin/";
     //待办事项接口地址
     var daiban_url = "http://10.248.97.236/xinghuo-apaas-pendingcountservice/pendingcountservice/v1/";
     //通知通告，工作动态下载文件路径
     var downFileUrl = "http://10.248.97.236";
     //转发接口
     var zf_url = "http://10.248.97.236/supp/httpClient";

     var todo_count = "http://10.248.97.236/supp/httpClient/xinghuo-admin/act/task/toDoTaskCountForPush"
} else {
    //获取应用接口地址
    var applist_url = "http://10.248.97.236:9027/supp/httpClient/msa_xhyzd_app/app/";
    //通知通告，工作动态地址
    var announcement_url = "http://10.248.97.236:9027/supp/httpClient/xinghuo-admin/";
    //待办事项接口地址
    var daiban_url = "http://10.248.97.236:9041/xinghuo-apaas-pendingcountservice/pendingcountservice/v1/";
    //通知通告，工作动态下载文件路径
    var downFileUrl = "http://10.248.97.236:9999";
    //转发接口
    var zf_url = "http://10.248.97.236:9027/supp/httpClient";
}

//端口映射地址
// var url = "http://172.28.0.56:9008";
var url = "http://172.28.0.56:9000";
url = app.getUrl(url);
var category_test_url = "http://172.28.0.186:9403";
//服务器真实地址
var _url = "http://10.42.0.235:9000";

//测试
// var _url2 = "http://68.64.156.234:8086";
//正式
var _url2 = "http://68.61.8.101";

//测试
//var _file_url = "http://10.42.0.171:8099";
//正式
var _file_url = "http://10.235.136.220:10001";

//测试
//var fj_task_url = "http://10.42.0.171:9008";
//正式
var fj_task_url = "http://10.235.136.220:10001";


//全局变量，app名称和版本信息
APP_NAME = "com.page.index";

APP_VERSION = "V1.82.20230922";
//测试
//var app_code_cad = "com.xinghuo.test.cad";//110警情
//var app_code_manager = "com.test.oa_task_manager";//任务管理
//正式
var app_code_cad = "com.xinghuo.cad";//110警情
var app_code_manager = "OA_TASK_MANAGER";//任务管理
const app_url_manager = "modules3/OA_TASK_MANAGER/oa_task_list.html"; //任务管理

function shuiyin () {
    xh.getWaterMark();

}
//获取分区信息
function testqu (callback) {
    Cordova.exec(function (result) {
        callback && callback(result)
    }, null, "LinkPlugin", "userOrg", []);
}
var announcement_url_lg = "http://10.248.97.237:9027/supp/httpClient/xinghuo-admin-lg/"
var announcement_url_gm = "http://10.248.97.237:9027/supp/httpClient/xinghuo-admin-gm/"