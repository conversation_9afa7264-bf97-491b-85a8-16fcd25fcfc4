/**
 * Created by dzq on 2021/3/5.
 */
var ui = {
    //参数设置
    settings : {
        //single  单页面工程  muti  多页面工程
        appType : 'single',
        //page没有设置data-transition时默认动画效果
        transitionType : 'slide',
        //是否显示页面加载中(页面加载时，页面回退或其他页面的加载都会失效，直到此页面失败或成功)
        showPageLoading : true ,
        //加载中文字
        showPageLoadingText : "加载中...",
        //加载中图标（为样式）
        showPageLoadingIcon : "icon-spinner5",
        //初始化操作
        init : function(){}
    },
    //是否有打开的侧边菜单
    hasSidebarOpen : false,
    //是否有打开的弹出框
    hasPopupOpen : false,
    //是否页面正在切换
    isPageSwitching : false,
    /*
     * 启动程序
     */
    launch : function(){
        $.noop = function(){}; //增加一个空函数

        //设置viewport
        ui.Utils.setViewPort();


        //如果主页就有侧边栏, DOM元素不被删除
        //$("aside").addClass("noRemove");

        ui.init(); //uiwidget

        //单页初始化的东西
        if(ui.settings.appType == 'single'){
            ui.Page.init(); //主页添加到page缓存
        }

        ui.settings.init();

        document.addEventListener('doubleTap', function (e) { e.preventDefault(); }, false);


        /*$(window).on("ortchange",function(){
         //设置viewport
         ui.Utils.setViewPort();
         });*/

        //这句加上后将导致无iscroll的页面拖动不了
        //document.addEventListener('touchmove', function (e) { e.preventDefault(); }, false);


    },
    //用户启动程序
    run : function (options){
        $.extend(this.settings, options);

        $.each(ui.pages,function(k,v){
            var sectionId = '#'+k;
            $('body').delegate(sectionId,'pageReady',function(e, options){
                v.pageReady && v.pageReady.call(v, e, options);
            });
            $('body').delegate(sectionId,'pageLoad',function(e, options){
                v.pageLoad && v.pageLoad.call(v, e, options);
            });
            $('body').delegate(sectionId,'pageBack',function(e, options){
                v.pageBack && v.pageBack.call(v, e, options);
            });
        });

        this.launch();
    },
    pages : {},
    //添加页面的事件处理
    addPageEvent : function(id,factory){
        ui.pages[id] = new factory();
    }
}