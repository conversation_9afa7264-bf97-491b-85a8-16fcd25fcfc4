/**
 * ichartjs Library v1.2 http://www.ichartjs.com/
 * 
 * <AUTHOR>
 * @Copyright 2013 <EMAIL> Licensed under the Apache License, Version 2.0 (the "License"); 
 * you may not use this file except in compliance with the License. 
 * You may obtain a copy of the License at http://www.apache.org/licenses/LICENSE-2.0
 */
;(function(c){var a=navigator.userAgent.toLowerCase(),b=Object.prototype.toString,d=/opera/.test(a),e=/\bchrome\b/.test(a),f=/webkit/.test(a),g=!e&&/safari/.test(a),j=!d&&/msie/.test(a),k=!!document.createElement("canvas").getContext,l=!f&&/gecko/.test(a),p=/ipod|ipad|iphone|android/gi.test(a),m=function(a,b,d,i){return d*a/i+b},r={easeIn:function(a,b,d,i){return d*(a/=i)*a*a+b},easeOut:function(a,b,d,i){return d*((a=a/i-1)*a*a+1)+b},easeInOut:function(a,b,d,i){return 1>(a/=i/2)?d/2*a*a*a+b:d/2*((a-=2)*a*a+2)+b}},o=function(a){var c=!1,v=!1,i=[],u=function(){if(document.addEventListener)return function(){document.removeEventListener("DOMContentLoaded",u,!1);w()};if(document.attachEvent)return function(){"complete"===document.readyState&&(document.detachEvent("onreadystatechange",u),w())}}(),y=function(){if(!c){try{document.documentElement.doScroll("left")}catch(a){setTimeout(y,1);return}w()}},w=function(){if(!c){c=!0;for(var a=0;a<i.length;a++)i[a].call(document);i=[]}},x=function(){if(!v){v=!0;if("complete"===document.readyState)return setTimeout(w,1);if(document.addEventListener)document.addEventListener("DOMContentLoaded",u,!1),a.addEventListener("load",w,!1);else if(document.attachEvent){document.attachEvent("onreadystatechange",u);a.attachEvent("onload",w);var i=!1;try{i=null==a.frameElement}catch(b){}document.documentElement.doScroll&&i&&y()}}},S=function(a){x();c?a.call(document,s):i.push(function(){return a.call(this)})},s=function(a){if(!a||a.nodeType)return a;if("string"===typeof a)return-1!=a.indexOf("#")&&(a=a.substring(1)),document.getElementById(a);"function"===typeof a&&S(a)};s.apply=function(a,i){if(a&&i&&"object"==typeof i)for(var b in i)"undefined"!=typeof i[b]&&(a[b]=i[b]);if(!i&&a){var d={};for(b in a)d[b]=a[b];return d}return a};s.apply(s,{version:"1.0",email:"<EMAIL>",isEmpty:function(a,i){return null===a||void 0===a||s.isArray(a)&&!a.length||(!i?""===a:!1)},isArray:function(a){return"[object Array]"===b.apply(a)},isDate:function(a){return"[object Date]"===b.apply(a)},isObject:function(a){return!!a&&"[object Object]"===b.apply(a)},isFunction:function(a){return"[object Function]"===b.apply(a)},isNumber:function(a){return"number"===typeof a&&isFinite(a)},isString:function(a){return"string"===typeof a},isBoolean:function(a){return"boolean"===typeof a},isFalse:function(a){return"boolean"===typeof a&&!a},isElement:function(a){return a?!!a.tagName:!1},isDefined:function(a){return"undefined"!==typeof a}});s.applyIf=function(a,i){if(a&&s.isObject(i))for(var b in i)s.isDefined(i[b])&&!s.isDefined(a[b])&&(a[b]=i[b]);return!i&&a?s.apply(a):a};s.merge=function(a,i,b){if(a&&s.isObject(i)){for(var d in i)s.isDefined(i[d])&&(s.isObject(i[d])?s.isObject(a[d])?s.merge(a[d],i[d]):a[d]=s.clone(i[d],!0):a[d]=i[d]);if(s.isObject(b))return s.merge(a,b)}return a};s.clone=function(a,i,b){var d={};if(s.isArray(a)&&s.isObject(i))for(var c=0;c<a.length;c++)d[a[c]]=b&&s.isObject(i[a[c]])?s.clone(i[a[c]],b):i[a[c]];else if(s.isObject(a))for(c in a)d[c]=i&&s.isObject(a[c])&&!a[c].ICHARTJS_OBJECT?s.clone(a[c],i):a[c];return d};s.override=function(a,i){if(a&&i){var b=a.prototype;s.apply(b,i);s.isIE&&i.hasOwnProperty("toString")&&(b.toString=i.toString)}};s.extend=function(){var a=function(a){for(var i in a)this[i]=a[i]},i=Object.prototype.constructor;return function(b,d){var c=function(){b.apply(this,arguments)},u=function(){},e=b.prototype;u.prototype=e;u=c.prototype=new u;u.constructor=c;c.superclass=e;e.constructor==i&&(e.constructor=b);c.override=function(a){s.override(c,a)};u.superclass=u.supr=function(){return e};u.override=a;s.override(c,d);c.extend=function(a){return s.extend(c,a)};c.plugin_={};c.plugin=function(a,i){s.isString(a)&&s.isFunction(i)&&(c.plugin_[a]=i)};return c}}();var E=Math.sin,I=Math.cos,n=Math.atan,T=Math.sqrt,J=Math.abs,z=Math.PI,B=2*z,K=Math.ceil,C=Math.round,F=Math.floor,L=Math.max,M=Math.min,A=parseFloat,D={},G={},N=function(a,i){if(0==a)return a;var b=J(a),d=0.1;if(1<b){for(;1<b;)b/=10,d*=10;return F(a/d+i)*d}for(d=1;1>b;)b*=10,d*=10;return C(a*d+i)/d},O={white:"rgb(255,255,255)",green:"rgb(0,128,0)",gray:"rgb(80,80,80)",red:"rgb(255,0,0)",blue:"rgb(0,0,255)",yellow:"rgb(255,255,0)",black:"rgb(0,0,0)"},P=function(a){var i=/rgb\((\w*),(\w*),(\w*)\)/.exec(a);if(i)return[i[1],i[2],i[3]];if(i=/rgba\((\w*),(\w*),(\w*),(.*)\)/.exec(a))return[i[1],i[2],i[3],i[4]];throw Error("invalid colors value '"+a+"'");},H=function(a){if(!a)return a;a=a.replace(/\s/g,"").toLowerCase();if(/^rgb\([0-9]{1,3},[0-9]{1,3},[0-9]{1,3}\)$/.exec(a)||/^rgba\([0-9]{1,3},[0-9]{1,3},[0-9]{1,3},(0(\.[0-9])?|1(\.0)?)\)$/.exec(a))return a;if(/^#(([a-fA-F0-9]{6,7})|([a-fA-F0-9]{3}))$/.exec(a))return a=a.replace(/#/g,"").replace(/^(\w)(\w)(\w)$/,"$1$1$2$2$3$3"),(7==a.length?"rgba(":"rgb(")+parseInt(a.substring(0,2),16)+","+parseInt(a.substring(2,4),16)+","+parseInt(a.substring(4,6),16)+(7==a.length?",0."+a.substring(6,7)+")":")");if(O[a])return O[a];throw Error("invalid colors value '"+a+"'");},Q=function(a,i){i=i||0.14;return 0.5<a?i-(1-a)/10:0.1<a?i-0.16+a/5:a>i?i:a/2},R=function(a,i,b,d){if(!i)return i;var i=P(H(i)),c;c=i;var u=void 0,e=void 0;s.isArray(c)&&(u=c[1],e=c[2],c=c[0]);c/=255;var u=u/255,e=e/255,y=L(L(c,u),e),f=M(M(c,u),e),f=y-f;if(0==f)c=[0,0,y];else{var w;c==y?w=(u-e)/f:u==y?w=(e-c)/f+2:e==y&&(w=(c-u)/f+4);w*=60;0>w&&(w+=360);c=[w,f/y,y]}c[1]-=0!=d?d||0.05:d;a?(c[2]-=Q(c[2],b),c[1]=s.upTo(c[1],1),c[2]=s.lowTo(c[2],0)):(c[2]+=Q(1-c[2],b),c[1]=s.lowTo(c[1],0),c[2]=s.upTo(c[2],1));d=i[3];a=i=void 0;s.isArray(c)&&(a=d,d=c[1],i=c[2],c=c[0]);var g,j,k,b=F(c/60)%6,e=c/60-b;c=i*(1-d);u=i*(1-d*e);d=i*(1-d*(1-e));switch(b){case 0:g=i;j=d;k=c;break;case 1:g=u;j=i;k=c;break;case 2:g=c;j=i;k=d;break;case 3:g=c;j=u;k=i;break;case 4:g=d;j=c;k=i;break;case 5:g=i,j=c,k=u}return"rgb"+(a?"a":"")+"("+C(255*g)+","+C(255*j)+","+C(255*k)+(a?","+a+")":")")};s.apply(s,{getFont:function(a,i,b,c){return a+" "+i+(c||"px")+" "+b},getDoc:function(){return a.contentWindow?a.contentWindow.document:a.contentDocument?a.contentDocument:a.document},DefineAbstract:function(a,i){if(!i[a])throw Error("Cannot instantiate the type '"+i.type+"'.you must implements it with method '"+a+"'.");},getAA:function(a){return"linear"==a?m:"easeInOut"==a||"easeIn"==a||"easeOut"==a?r[a]:m},noConflict:function(){return o},plugin:function(a,i,b){s.isFunction(a)&&a.plugin(i,b)},parsePadding:function(a,i){a=a||0;if(s.isNumber(a))return[a,a,a,a];if(s.isArray(a))return a;i=i||0;a=a.replace(/^\s+|\s+$/g,"").replace(/\s{2,}/g,/\s/).replace(/\s/g,",").split(",");1==a.length?a[0]=a[1]=a[2]=a[3]=A(a[0])||i:2==a.length?(a[0]=a[2]=A(a[0])||i,a[1]=a[3]=A(a[1])||i):3==a.length?(a[0]=A(a[0])||i,a[1]=a[3]=A(a[1])||i,a[2]=A(a[2])||i):(a[0]=A(a[0])||i,a[1]=A(a[1])||i,a[2]=A(a[2])||i,a[3]=A(a[3])||i);return a},distanceP2P:function(a,i,b,c){return T((b-a)*(b-a)+(c-i)*(c-i))},atan2Radian:function(a,i,b,c){if(a==b)return c>i?z/2:3*z/2;if(i==c)return b>a?0:z;var d=s.quadrant(a,i,b,c),a=n(J((i-c)/(a-b)));return d?(3==d?B:z)+(2==d?a:-a):a},angle2Radian:function(a){return a*z/180},radian2Angle:function(a){return 180*a/z},quadrant:function(a,i,b,c){return a<b?i<c?0:3:i<c?1:2},toPI2:function(a){for(;0>a;)a+=B;return a},visible:function(a,i,b){if(a>=i)return[];var c=s.quadrantd(a),d=s.quadrantd(i);if((2==c||3==c)&&(2==d||3==d)&&i-a<z)return[];a=s.toPI2(a);i=s.toPI2(i);i<=a&&(i+=B);if(a>z)a=B;else{if(i>B)return[{s:a,e:z,f:b},{s:B,e:i,f:b}];i>z&&(i=z)}return{s:a,e:i,f:b}},quadrantd:function(a){if(0==a)return 0;if(0==a%B)return 3;for(;0>a;)a+=B;return K(2*(a%B)/z)-1},upTo:function(a,i){return i>a?a:i},lowTo:function(a,i){return i<a?a:i},between:function(a,i,b){return a>i?s.between(i,a,b):b>i?i:b<a?a:b},inRange:function(a,i,b){return i>b&&a<b},angleInRange:function(a,i,b){b-=a;b=0>b?b+B:b;b%=B;return i-a>b},angleZInRange:function(a,i,b){return i<a?b>a||b<i:i>b&&a<b},inRangeClosed:function(a,i,b){return i>=b&&a<=b},inEllipse:function(a,i,b,c){return 1>=a*a/b/b+i*i/c/c},p2Point:function(a,i,b,c){return{x:a+I(b)*c,y:i+E(b)*c}},toRgb:H,toRgba:function(a,i){var b=P(H(a));return"rgba("+b[0]+","+b[1]+","+b[2]+","+i+")"},vectorP2P:function(a,i,b){b||(i=s.angle2Radian(i),a=s.angle2Radian(a));i=E(i);return{x:i*E(a),y:i*I(a)}},uid:function(a){return(a||"ichartjs")+"_"+K(1E4*Math.random())+(new Date).getTime().toString().substring(4)},register:function(a){if(s.isString(a))G[a.toLowerCase()]=a;else{var i=a.get("id");if(!i||""==i){for(i=s.uid(a.type);D[i];)i=s.uid(a.type);a.push("id",i)}if(D[i])throw Error("exist reduplicate id :"+i);a.id=i;D[i]=a}},create:function(a){if(!a.type||!G[a.type])throw Error("TypeNotFoundException["+a.type+"]");return new s[G[a.type]](a)},get:function(a){return D[a]},isPercent:function(a){return s.isString(a)&&a.match(/(.*)%/)},parsePercent:function(a,i){s.isString(a)&&(a=a.match(/(.*)%/))&&(a=i?F(A(a[1])*i/100):a[1]/100);return!a||0>=a||a>i?i:a},parseFloat:function(a,i){if(!s.isNumber(a)&&(a=A(a),!s.isNumber(a)))throw Error("["+i+"]="+a+"is not a valid number.");return a},ceil:function(a){return N(a,1)},floor:function(a){return N(a,-1)},_2D:"2d",_3D:"3d",light:function(a,i,b){return R(!1,a,i,b)},dark:function(a,i,b){return R(!0,a,i,b)},fixPixel:function(a){return s.isNumber(a)?a:A(a.replace("px",""))||0},toPixel:function(a){return s.isNumber(a)?a+"px":s.fixPixel(a)+"px"},emptyFn:function(){return!0},supportCanvas:k,isOpera:d,isWebKit:f,isChrome:e,isSafari:g,isIE:j,isGecko:l,isMobile:p,touch:"ontouchend"in document,FRAME:p?30:60});s.Assert={isTrue:function(a,i){if(!0!==a)throw Error(i);}};s.requestAnimFrame=function(){var i=a.requestAnimationFrame||a.webkitRequestAnimationFrame||a.mozRequestAnimationFrame||a.oRequestAnimationFrame||a.msRequestAnimationFrame||function(i){a.setTimeout(i,1E3/60)};return function(a){i(a)}}();s.Event={addEvent:function(a,i,b,c){a.addEventListener?a.addEventListener(i,b,c):a.attachEvent?a.attachEvent("on"+i,b):a["on"+i]=b},fix:function(i){"undefined"==typeof i&&(i=a.event);var b={target:i.target,pageX:i.pageX,pageY:i.pageY,offsetX:i.offsetX,offsetY:i.offsetY,stopPropagation:!1,event:i};if("undefined"==typeof i.offsetX){i.target||(b.target=i.srcElement||document);i.targetTouches&&(b.pageX=i.targetTouches[0].pageX,b.pageY=i.targetTouches[0].pageY);if(null==b.pageX&&null!=i.clientX){var c=document.documentElement,d=document.body;b.pageX=i.clientX+(c&&c.scrollLeft||d&&d.scrollLeft||0)-(c&&c.clientLeft||d&&d.clientLeft||0);b.pageY=i.clientY+(c&&c.scrollTop||d&&d.scrollTop||0)-(c&&c.clientTop||d&&d.clientTop||0)}for(var d=c=0,u=i.target;u!=document.body&&u;)c+=u.offsetLeft-(u.scrollLeft||0),d+=u.offsetTop,u=u.offsetParent;b.offsetX=b.pageX-c;b.offsetY=b.pageY-d}b.x=b.offsetX;b.y=b.offsetY;i.stopPropagation||(i.stopPropagation=function(){a.event.cancelBubble=true});return b}};return s}(c);Array.prototype.each=function(a,b){for(var c=this.length,i,d=0;d<c&&!(i=b?a.call(b,this[d],d):a(this[d],d),"boolean"===typeof i&&!i);d++);return this};Array.prototype.eachAll=function(a,b){this.each(function(c,i){return o.isArray(c)?c.eachAll(a,b):b?a.call(b,c,i):a(c,i)},b)};Array.prototype.sor=function(a){for(var b=this.length-1,c,i=0;i<b;i++)for(var d=b;d>i;d--)if(a?!a(this[d],this[d-1]):this[d]<this[d-1])c=this[d],this[d]=this[d-1],this[d-1]=c};c.iChart=o;c.$||(c.$=c.iChart)})(window);(function(c){c.Element=function(a){var b=this._();b.type="element";b.ICHARTJS_OBJECT=!0;c.DefineAbstract("configure",b);c.DefineAbstract("afterConfiguration",b);b.options={};b.set({border:{enable:!1,color:"#BCBCBC",style:"solid",width:1,radius:0},shadow:!1,shadow_color:"#666666",shadow_blur:4,shadow_offsetx:0,shadow_offsety:0});b.W="width";b.H="height";b.O="top";b.B="bottom";b.L="left";b.R="right";b.C="center";b.X="originx";b.Y="originy";b.variable={};b.events={mouseup:[],touchstart:[],touchmove:[],touchend:[],mousedown:[],dblclick:[]};b.registerEvent("initialize");b.initialization=!1;b.configure.apply(b,Array.prototype.slice.call(arguments,1));b.default_=c.clone(b.options,!0);b.set(a);b.afterConfiguration(b)};c.Element.prototype={_:function(){return this},afterConfiguration:function(a){if(c.isObject(a.get("listeners")))for(var b in a.get("listeners"))a.on(b,a.get("listeners")[b]);a.initialize();a.fireEvent(a,"initialize",[a])},registerEvent:function(){for(var a=0;a<arguments.length;a++)this.events[arguments[a]]=[]},fireString:function(a,b,d,e){a=this.fireEvent(a,b,d);return c.isString(a)?a:!0!==a&&c.isDefined(a)?a.toString():e},fireEvent:function(a,b,c){var e=this.events[b].length;if(1==e)return this.events[b][0].apply(a,c);for(var f=!0,g=0;g<e;g++)this.events[b][g].apply(a,c)||(f=!1);return f},on:function(a,b){c.isString(a)&&c.isArray(this.events[a])?this.events[a].push(b):c.isArray(a)&&a.each(function(a){this.on(a,b)},this);return this},getPlugin:function(a){return this.constructor.plugin_[a]},set:function(a){c.isObject(a)&&c.merge(this.options,a)},pushIf:function(a,b){return!c.isDefined(this.get(a))||null==this.get(a)?this.push(a,b):this.get(a)},push:function(a,b){for(var c=a.split("."),e=c.length-1,f=this.options,g=0;g<e;g++)f[c[g]]||(f[c[g]]={}),f=f[c[g]];return f[c[e]]=b},get:function(a){for(var a=a.split("."),b=this.options[a[0]],c=1;c<a.length;c++){if(!b)return null;b=b[a[c]]}return b}};c.Painter=c.extend(c.Element,{configure:function(){this.type="painter";this.dimension=c._2D;c.DefineAbstract("commonDraw",this);c.DefineAbstract("initialize",this);this.set({strokeStyle:"gray",padding:10,color:"black",offsetx:0,offsety:0,background_color:"#FEFEFE",color_factor:0.15,style:"",border:{enable:!0},gradient:!1,gradient_mode:"LinearGradientUpDown",z_index:0,listeners:null,originx:null,originy:null});this.variable.event={mouseover:!1};this.variable.animation={};this.registerEvent("click","mousemove","mouseover","mouseout","beforedraw","draw")},is3D:function(){return this.dimension==c._3D},applyGradient:function(a,b,c,e){var f=this._();f.get("gradient")&&f.get("f_color")&&(f.push("f_color",f.T.gradient(a||f.x||0,b||f.y||0,c||f.get(f.W),e||f.get(f.H),[f.get("dark_color"),f.get("light_color")],f.get("gradient_mode"))),f.push("light_color",f.T.gradient(a||f.x||0,b||f.y||0,c||f.get(f.W),e||f.get(f.H),[f.get("background_color"),f.get("light_color")],f.get("gradient_mode"))),f.push("f_color_",f.get("f_color")))},draw:function(a,b){if(b)this.root.draw(a);else{if(!this.fireEvent(this,"beforedraw",[this,a]))return this;this.commonDraw(this,a);this.fireEvent(this,"draw",[this,a])}},inject:function(a){a&&(this.root=a,this.target=this.T=a.T)},doConfig:function(){var a=this._(),b=c.parsePadding(a.get("padding")),d=a.get("border.enable"),d=d?c.parsePadding(a.get("border.width")):[0,0,0,0],e=c.toRgb(a.get("background_color")),f=a.get("color_factor"),g=a.get("gradient")?0:null;a.set({border_top:d[0],border_right:d[1],border_bottom:d[2],border_left:d[3],hborder:d[1]+d[3],vborder:d[0]+d[2],padding_top:b[0]+d[0],padding_right:b[1]+d[1],padding_bottom:b[2]+d[2],padding_left:b[3]+d[3],hpadding:b[1]+b[3]+d[1]+d[3],vpadding:b[0]+b[2]+d[0]+d[2]});!0===a.get("shadow")&&a.push("shadow",{color:a.get("shadow_color"),blur:a.get("shadow_blur"),offsetx:a.get("shadow_offsetx"),offsety:a.get("shadow_offsety")});a.push("f_color",e);a.push("f_color_",e);a.push("light_color",c.light(e,f,g));a.push("dark_color",c.dark(e,0.8*f,g));a.push("light_color2",c.light(e,2*f,g));a.is3D()&&!a.get("xAngle_")&&(b=c.vectorP2P(a.get("xAngle"),a.get("yAngle")),a.push("xAngle_",b.x),a.push("yAngle_",b.y))}});c.Html=c.extend(c.Element,{configure:function(a){this.type="html";this.T=a;c.DefineAbstract("beforeshow",this);this.set({animation:!0,default_action:!0,width:0,height:0,style:"",index:999,offset_top:0,offset_left:0});this.transitions=""},initialize:function(){var a=this._();a.wrap=a.get("wrap");a.dom=document.createElement("div");a.get("shadow")&&a.css("boxShadow",a.get("shadow_offsetx")+"px "+a.get("shadow_offsety")+"px "+a.get("shadow_blur")+"px "+a.get("shadow_color"));a.get("border.enable")&&(a.css("border",a.get("border.width")+"px "+a.get("border.style")+" "+a.get("border.color")),a.css("borderRadius",a.get("border.radius")+"px"));a.css("position","absolute");a.css("zIndex",a.get("index"));a.applyStyle();a.wrap.appendChild(a.dom);a.style=a.dom.style;a.get("default_action")&&a.doAction(a)},width:function(){return this.dom.offsetWidth},height:function(){return this.dom.offsetHeight},onTransitionEnd:function(a,b){var d="transitionend";c.isWebKit?d="webkitTransitionEnd":c.isOpera&&(d="oTransitionEnd");c.Event.addEvent(this.dom,d,a,b)},destroy:function(){this.wrap.removeChild(this.dom);this.dom=null},transition:function(a){this.transitions=""==this.transitions?a:this.transitions+","+a;c.isWebKit?this.css("WebkitTransition",this.transitions):c.isGecko?this.css("MozTransition",this.transitions):c.isOpera?this.css("OTransition",this.transitions):this.css("transition",this.transitions)},beforeshow:function(a,b,c){c.follow(a,b,c)},show:function(a,b){this.beforeshow(a,b,this);this.css("visibility","visible");this.get("animation")&&this.css("opacity",1)},hidden:function(){this.css("visibility","hidden")},getDom:function(){return this.dom},css:function(a,b){if(c.isString(a))if(c.isDefined(b))this.dom.style[a]=b;else return this.dom.style[a]},applyStyle:function(){for(var a=this.get("style").split(";"),b,c=0;c<a.length;c++)b=a[c].split(":"),1<b.length&&this.css(b[0],b[1])}});c.Component=c.extend(c.Painter,{configure:function(a){c.Component.superclass.configure.apply(this,arguments);this.type="component";this.set({fontsize:12,font:"Verdana",fontweight:"normal",fontunit:"px",tip:{enable:!1,border:{width:2}}});this.ICHARTJS_CHART=this.proxy=this.atomic=!1;this.inject(a)},initialize:function(){c.DefineAbstract("isEventValid",this);c.DefineAbstract("doDraw",this);this.doConfig();this.initialization=!0},getDimension:function(){return{x:this.x,y:this.y,width:this.get("width"),height:this.get("height")}},destroy:function(){this.tip&&this.tip.destroy()},doConfig:function(){c.Component.superclass.doConfig.call(this);var a=this._(),b=a.get(a.W),d=a.get("maxwidth"),e=a.get(a.X);if(b&&d&&(b=a.push(a.W,c.parsePercent(b,d)),b>d&&(b=a.push("width",d)),d>b)){var f=a.get("align")||a.C;f==a.C?e+=(d-b)/2:f==a.R&&(e+=d-b)}a.x=a.push(a.X,e+a.get("offsetx"));a.y=a.push(a.Y,a.get(a.Y)+a.get("offsety"));a.push("fontStyle",c.getFont(a.get("fontweight"),a.get("fontsize"),a.get("font"),a.get("fontunit")));a.data=a.get("data");a.get("tip.enable")&&(a.pushIf("tip.border.color",a.get("f_color")),c.isFunction(a.get("tip.invokeOffset"))||a.push("tip.invokeOffset",a.tipInvoke()))},isMouseOver:function(a){return this.isEventValid(a,this)},redraw:function(a){this.root.draw(a,this.root.Combination)},last:c.emptyFn,commonDraw:function(a){a.proxy||a.doDraw.call(a,a)}});c.Tip=c.extend(c.Html,{configure:function(){c.Tip.superclass.configure.apply(this,arguments);this.type="tip";this.set({name:"",index:0,value:"",text:"",showType:"follow",invokeOffset:null,fade_duration:300,move_duration:100,timing_function:"ease-out",invokeOffsetDynamic:!1,style:"textAlign:left;padding:4px 5px;cursor:pointer;backgroundColor:rgba(239,239,239,.85);fontSize:12px;color:black;",border:{enable:!0,radius:5},delay:200});this.registerEvent("parseText")},position:function(a,b,c){c.style.top=(0>a?0:a)+"px";c.style.left=(0>b?0:b)+"px"},follow:function(a,b,d){d.get("invokeOffsetDynamic")?b.hit&&((c.isString(b.text)||c.isNumber(b.text))&&d.text(b.name,b.value,b.text,b.i,d),a=d.get("invokeOffset")(d.width(),d.height(),b),d.position(a.top,a.left,d)):"follow"!=d.get("showType")&&c.isFunction(d.get("invokeOffset"))?(a=d.get("invokeOffset")(d.width(),d.height(),b),d.position(a.top,a.left,d)):d.position(a.y-1.1*d.height()-2,a.x+2,d)},text:function(a,b,c,e,f){f.dom.innerHTML=f.fireString(f,"parseText",[f,a,b,c,e],c)},hidden:function(){this.get("animation")?this.css("opacity",0):this.css("visibility","hidden")},doAction:function(a){a.T.on("mouseover",function(b,c,e){a.show(c,e)}).on("mouseout",function(b,c){a.hidden(c)});if("follow"==a.get("showType"))a.T.on("mousemove",function(b,c,e){a.T.variable.event.mouseover&&setTimeout(function(){a.T.variable.event.mouseover&&a.follow(c,e,a)},a.get("delay"))})},initialize:function(){c.Tip.superclass.initialize.call(this);var a=this._();a.text(a.get("name"),a.get("value"),a.get("text"),a.get("index"),a);a.hidden();if(a.get("animation")){var b=a.get("move_duration")/1E3+"s "+a.get("timing_function")+" 0s";a.transition("opacity "+a.get("fade_duration")/1E3+"s "+a.get("timing_function")+" 0s");a.transition("top "+b);a.transition("left "+b);a.onTransitionEnd(function(){0==a.css("opacity")&&a.css("visibility","hidden")},!1)}}});c.CrossHair=c.extend(c.Html,{configure:function(){c.CrossHair.superclass.configure.apply(this,arguments);this.type="crosshair";this.set({top:0,left:0,hcross:!0,vcross:!0,invokeOffset:null,line_width:1,line_color:"#1A1A1A",delay:200})},follow:function(a,b,c){c.get("invokeOffset")?(a=c.get("invokeOffset")(a,b))&&a.hit?(c.o_valid=!0,c.position(a.top-c.top,a.left-c.left,c)):(!a||!c.o_valid)&&c.position(c.owidth,c.oheight,c):c.position(a.y-c.top-1,a.x-c.left-1,c)},position:function(a,b,c){c.horizontal.style.top=a-c.size+"px";c.vertical.style.left=b-c.size+"px"},doCreate:function(a,b,d){var e=document.createElement("div");e.style.width=c.toPixel(b);e.style.height=c.toPixel(d);e.style.backgroundColor=a.get("line_color");e.style.position="absolute";a.dom.appendChild(e);return e},doAction:function(a){a.T.on("mouseover",function(b,c,e){a.show(c,e)}).on("mouseout",function(b,c,e){a.hidden(c,e)}).on("mousemove",function(b,c,e){a.follow(c,e,a)})},initialize:function(){c.CrossHair.superclass.initialize.call(this);var a=this._(),b=c.toPixel(a.get("line_width"));a.size=a.get("line_width")/2;a.top=c.fixPixel(a.get(a.O));a.left=c.fixPixel(a.get(a.L));a.owidth=-a.T.root.width;a.oheight=-a.T.root.height;a.o_valid=!1;a.css("width","0px");a.css("height","0px");a.css("top",a.top+"px");a.css("left",a.left+"px");a.css("visibility","hidden");a.horizontal=a.doCreate(a,a.get("hcross")?c.toPixel(a.get(a.W)):"0px",b);a.vertical=a.doCreate(a,b,a.get("vcross")?c.toPixel(a.get(a.H)):"0px")}});c.Legend=c.extend(c.Component,{configure:function(){c.Legend.superclass.configure.apply(this,arguments);this.type="legend";this.set({data:void 0,width:"auto",column:1,row:"max",maxwidth:0,line_height:16,sign:"square",sign_size:10,sign_space:5,legend_space:5,z_index:1009,text_with_sign_color:!1,align:"right",valign:"middle"});this.atomic=!0;this.registerEvent("parse")},isEventValid:function(a,b){var c={valid:!1};a.x>this.x&&a.x<b.x+b.width&&a.y>b.y&&a.y<b.y+b.height&&b.data.each(function(e,f){if(a.x>e.x&&a.x<e.x+e.width_+b.get("signwidth")&&a.y>e.y&&a.y<e.y+b.get("line_height"))return c={valid:!0,index:f,target:e},!1},b);return c},drawCell:function(a,b,c,e,f,g){var j=g.get("sign_size"),k=g.getPlugin("sign");if(!k||!k.call(g,g.T,f,{x:a+j/2,y:b},j,e))-1!=f.indexOf("bar")&&g.T.box(a,b-j/12,j,j/6,0,e),"round"==f?g.T.round(a+j/2,b,j/2,e):"round-bar"==f?g.T.round(a+j/2,b,j/4,e):"square-bar"==f?g.T.box(a+j/4,b-j/4,j/2,j/2,0,e):"square"==f&&g.T.box(a,b-j/2,j,j,0,e);g.T.fillText(c,a+g.get("signwidth"),b,0,g.get("text_with_sign_color")?e:g.get("color"),"lr",g.get("line_height"))},doDraw:function(a){a.T.box(a.x,a.y,a.width,a.height,a.get("border"),a.get("f_color"),!1,a.get("shadow"));a.T.textStyle(a.L,"middle",c.getFont(a.get("fontweight"),a.get("fontsize"),a.get("font")));a.data.each(function(b){a.drawCell(b.x,b.y,b.text,b.color,b.sign,a)})},doLayout:function(a,b){var d=a.get("sign_size"),e=0,f=0,g=0,j=a.get("column"),k=a.get("row"),l=a.data.length;a.T.textFont(a.get("fontStyle"));a.get("line_height")<d&&a.push("line_height",d+d/5);a.push("signwidth",d+a.get("sign_space"));a.data.each(function(b){b.width_=a.T.measureText(b.text)},a);for(var p=0;p<j;p++){for(var g=0,m=p;m<l;m+=j)g=Math.max(g,a.data[m].width_);a.columnwidth[p]=g;e+=g}for(p=0;p<k;p++){g=0;for(m=p*j;m<l;m++)g=Math.max(g,a.data[m].text.split("\n").length);a.columnheight[p]=g;f+=g}e=a.push(a.W,e+a.get("hpadding")+a.get("signwidth")*j+(j-1)*a.get("legend_space"));if(e>a.get("maxwidth")&&(d=Math.floor(a.get("fontsize")*(a.get("maxwidth")/e)),!(10>d&&1==j))){9<d?a.push("fontStyle",c.getFont(a.get("fontweight"),a.push("fontsize",d),a.get("font"))):1<j&&a.push("row",Math.ceil(l/a.push("column",j-1)));a.doLayout(a,b);return}var r;a.width=e;a.height=f=a.push(a.H,f*a.get("line_height")+a.get("vpadding"));a.y=a.get("valign")==a.O?b.get("t_originy"):a.get("valign")==a.B?b.get("b_originy")-f:b.get("centery")-f/2;a.x=a.get("align")==a.L?b.get("l_originx"):a.get("align")==a.C?b.get("centerx")-e/2:b.get("r_originx")-e;a.x=a.push(a.X,(0>a.x?b.get("l_originx"):a.x)+a.get("offsetx"));a.y=a.push(a.Y,(0>a.y?b.get("t_originy"):a.y)+a.get("offsety"));g=a.y+a.get("padding_top");d=a.get("legend_space")+a.get("signwidth");for(p=0;p<k;p++){f=a.x+a.get("padding_left");r=a.columnheight[p]/2*a.get("line_height");g+=r;for(m=0;m<j&&p*j+m<l;m++)e=a.data[p*j+m],e.y=g,e.x=f,f+=a.columnwidth[m]+d;g+=r}},doConfig:function(){c.Legend.superclass.doConfig.call(this);var a=this._(),b=a.root,d=c.isNumber(a.get("column")),e=c.isNumber(a.get("row")),f=a.data.length;a.get("align")==a.C&&"middle"==a.get("valign")&&a.push("valign",a.O);b.get("align")==a.L&&"middle"==a.get("valign")&&a.push("align",a.R);a.data.each(function(b,d){c.merge(b,a.fireEvent(a,"parse",[a,b.name,d]));b.text=b.text||b.name||"";b.sign=b.sign||a.get("sign")},a);!d&&!e&&(d=a.push("column",1));d&&!e&&(e=a.push("row",Math.ceil(f/a.get("column"))));!d&&e&&(d=a.push("column",Math.ceil(f/a.get("row"))));d=a.get("column");e=a.get("row");f>e*d&&(e+=Math.ceil((f-e*d)/d),e=a.push("row",e));a.columnwidth=[];a.columnheight=[];a.doLayout(a,b)}});c.Label=c.extend(c.Component,{configure:function(){c.Label.superclass.configure.apply(this,arguments);this.type="label";this.set({text:"",line_height:12,line_thickness:1,sign:"square",sign_size:12,padding:"2 5",offsety:2,sign_space:5,background_color:"#efefef",text_with_sign_color:!1});this.atomic=!0;this.registerEvent()},isEventValid:function(a,b){return{valid:c.inRange(b.labelx,b.labelx+b.get(b.W),a.x)&&c.inRange(b.labely,b.labely+b.get(b.H),a.y)}},text:function(a){a&&this.push("text",a);this.push(this.W,this.T.measureText(this.get("text"))+this.get("hpadding")+this.get("sign_size")+this.get("sign_space"))},localizer:function(a){var b=a.get("quadrantd"),c=a.get("line_points"),e=a.get("smooth"),b=1<=b&&2>=b,f=a.get("labelx"),g=a.get("labely");a.labelx=f+(b?-a.get(a.W)-e:e);a.labely=g-a.get(a.H)/2;c[2]={x:f,y:g};c[3]={x:c[2].x+(b?-e:e),y:c[2].y}},doLayout:function(a,b,c,e){e.push("labelx",e.get("labelx")+a/c);e.push("labely",e.get("labely")+b/c);e.get("line_points").each(function(c,d){c.x+=a;c.y+=b;return 1==d},e);e.localizer(e)},doDraw:function(a){var b=a.get("line_points"),c=a.get("sign_size"),e=a.labelx+a.get("padding_left"),f=a.labely+a.get("padding_top");a.T.label(b,a.get("line_thickness"),a.get("border.color"));a.T.box(a.labelx,a.labely,a.get(a.W),a.get(a.H),a.get("border"),a.get("f_color"),!1,a.get("shadow"));a.T.textStyle(a.L,a.O,a.get("fontStyle"));b=a.get("color");a.get("text_with_sign_color")&&(b=a.get("scolor"));"square"==a.get("sign")?a.T.box(e,f,c,c,0,a.get("scolor")):a.get("sign")&&a.T.round(e+c/2,f+c/2,c/2,a.get("scolor"));a.T.fillText(a.get("text"),e+c+a.get("sign_space"),f,a.get("textwidth"),b)},doConfig:function(){c.Label.superclass.doConfig.call(this);var a=this._();a.T.textFont(a.get("fontStyle"));a.get("fontsize")>a.get("line_height")&&a.push("line_height",a.get("fontsize"));a.get("sign")||(a.push("sign_size",0),a.push("sign_space",0));a.push(a.H,a.get("line_height")+a.get("vpadding"));a.text();a.localizer(a)}});c.Text=c.extend(c.Component,{configure:function(){c.Text.superclass.configure.apply(this,arguments);this.type="text";this.set({text:"",textAlign:"center",align:"center",background_color:0,textBaseline:"top",border:{enable:!1},width:0,height:0,padding:0,writingmode:"lr",line_height:16,rotate:0});this.registerEvent()},doDraw:function(a){a.get("box_feature")&&a.T.box(a.x,a.y,a.get(a.W),a.get(a.H),a.get("border"),a.get("f_color"));a.T.text(a.get("text"),a.get("textx"),a.get("texty"),a.get(a.W)-a.get("hpadding"),a.get("color"),a.get("textAlign"),a.get("textBaseline"),a.get("fontStyle"),a.get("writingmode"),a.get("line_height"),a.get("shadow"),a.get("rotate"))},isEventValid:function(){return{valid:!1}},doLayout:function(a,b,c,e){e.x=e.push(e.X,e.x+a);e.y=e.push(e.Y,e.y+b);e.push("textx",e.get("textx")+a);e.push("texty",e.get("texty")+b)},doConfig:function(){c.Text.superclass.doConfig.call(this);var a=this._(),b=a.x,d=a.y+a.get("padding_top"),e=a.get(a.W),f=a.get(a.H),g=a.get("textAlign"),b=b+(g==a.C?e/2:g==a.R?e-a.get("padding_right"):a.get("padding_left"));f&&(d+=f/2,a.push("textBaseline","middle"));a.push("textx",b);a.push("texty",d);a.push("box_feature",e&&f);a.applyGradient()}});(function(a){function b(i){"string"===typeof i&&(i=a(i));if(!i||!i.tagName||"canvas"!=i.tagName.toLowerCase())throw Error("there not a canvas element");this.canvas=i;this.c=this.canvas.getContext("2d")}var c=Math.PI,e=c/90,f=e/2,g=Math.ceil,j=Math.floor,k=2*c,l=Math.max,p=Math.min,m=Math.sin,r=Math.cos,o=function(a,b){return 1==a?j(b)+0.5:Math.round(b)},t=function(a,b,c,d){var e=b.x,f=b.y,g=a[c-1],j=a[c+1],k,n;if(c<a.length-1){var a=g.y,c=j.y,o;k=(d*e+g.x)/(d+1);n=(d*f+a)/(d+1);j=(d*e+j.x)/(d+1);d=(d*f+c)/(d+1);o=(d-n)*(j-e)/(j-k)+f-d;n+=o;d+=o;n>a&&n>f?(n=l(a,f),d=2*f-n):n<a&&n<f&&(n=p(a,f),d=2*f-n);d>c&&d>f?(d=l(c,f),n=2*f-d):d<c&&d<f&&(d=p(c,f),n=2*f-d);b.rcx=j;b.rcy=d}return[g.rcx||g.x,g.rcy||g.y,k||e,n||f,e,f]},q=function(i){return a.isNumber(i)?i:a.parseFloat(i,i)},v=function(i,b){var c,d=0,e,f=0,g=!1,j=b.get("labels");b.data=i;if("simple"==b.dataType)b.total=0,i.each(function(i){i.background_color=i.color;d=i.value||0;if(a.isArray(d)){var j=0;f=d.length>f?d.length:f;for(var k=0;k<d.length;k++)d[k]=q(d[k]),j+=d[k],g||(c=e=d[k],g=!0),c=l(d[k],c),e=p(d[k],e);i.total=j}else d=q(d),i.value=d,b.total+=d,g||(c=e=d,g=!0),c=l(d,c),e=p(d,e)},b),a.isArray(j)&&(f=j.length>f?j.length:f),b.push("maxItemSize",f);else if("stacked"==b.dataType||"complex"==b.dataType){var k=j.length,n,o,m,r="stacked"==b.dataType;if(0==k)for(var k=i[0].value.length,t=0;t<k;t++)j.push("");b.columns=[];for(t=0;t<k;t++)n=[],o=0,i.each(function(b,u){if(d=b.value[t])b.value[t]=d=q(d,d),o+=d,r?m=i[u].color:(m=b.color,g||(c=e=d,g=!0),c=l(d,c),e=p(d,e)),n.push(a.applyIf({name:b.name,value:b.value[t],background_color:m,color:m},a.isArray(b.extra)?b.extra[t]||{}:b))}),r&&(g||(c=e=d,g=!0),c=l(o,c),e=p(o,e)),b.columns.push({total:o,name:j[t],item:n})}b.push("minValue",e);b.push("maxValue",c);b.doConfig();b.initialization=!0};b.prototype={getContext:function(){return this.c},css:function(i,b){if(a.isDefined(b))this.canvas.style[i]=b;else return this.canvas.style[i]},ellipse:function(a,b,c,d,f,g,j,k,l,n,o,p,q,t){q=!!q;this.save().gCo(t).strokeStyle(k,l,n).shadowOn(o).fillStyle(j).moveTo(a,b).beginPath();for(q&&this.moveTo(a,b);f<=g;)this.lineTo(a+c*r(f),b+d*m(f)),f+=e;return this.lineTo(a+c*r(g),b+d*m(g)).closePath().stroke(k).fill(j).restore()},arc:function(a,b,c,d,e,f,g,k,l,n,o,q,p,t){k&&(c-=j(l/2));if(0>=c)return this;this.save().gCo(t).strokeStyle(k,l,n).fillStyle(g).beginPath();d?(this.moveTo(a+r(e)*(c-d),b+m(e)*(c-d)).lineTo(a+r(e)*c,b+m(e)*c),this.c.arc(a,b,c,e,f,q),this.lineTo(a+r(f)*(c-d),b+m(f)*(c-d)),this.c.arc(a,b,c-d,f,e,!q)):(this.c.arc(a,b,c,e,f,q),p&&this.lineTo(a,b));this.closePath();k?this.shadowOn(o).stroke(k).shadowOff().fill(g):this.shadowOn(o).fill(g);return this.restore()},sector:function(a,b,c,d,e,f,g,j,k,n,l,o,m,q){l&&this.arc(a,b,c,d,e,f,g,j,k,n,l,o,!m,!q);return this.arc(a,b,c,d,e,f,g,j,k,n,!1,o,!m)},sector3D:function(){var i=function(i,b,c,d,u,f,g,j,y){var g=function(a,e){this.lineTo(i+c*r(a),b+(e||0)+d*m(a))},k=u;for(this.fillStyle(a.dark(y)).moveTo(i+c*r(u),b+d*m(u)).beginPath();k<=f;)g.call(this,k),k+=e;g.call(this,f);this.lineTo(i+c*r(f),b+j+d*m(f));for(k=f;k>=u;)g.call(this,k,j),k-=e;g.call(this,u,j);this.lineTo(i+c*r(u),b+d*m(u)).closePath().fill(!0)},b=function(a,i,b,c,d,e,u,f){b=a+b*r(u);c=i+e+c*m(u);this.moveTo(a,i).beginPath().fillStyle(f).lineTo(a,i+e).lineTo(b,c).lineTo(b,c-e).lineTo(a,i).closePath().fill(!0)},c=function(i,c,d,e,f,g,k,j,y){var w=a.quadrantd(f),l=a.quadrantd(g),y=a.dark(y);(1==w||2==w)&&b.call(this,i,c,d,e,k,j,f,y);(0==l||3==l)&&b.call(this,i,c,d,e,k,j,g,y)},d=function(a,b,d,e,u,f,g,k,j,w,l,o,m){this.ellipse(a,b+g,d,e,u,f,k,j,w,l,o,m,!0);c.call(this,a,b,d,e,u,f,m,g,k);this.ellipse(a,b,d,e,u,f,k,j,w,l,!1,m,!0);i.call(this,a,b,d,e,u,f,m,g,k);return this};d.layerPaint=c;d.sPaint=i;d.layerDraw=b;return d}(),textStyle:function(a,b,c){return this.textAlign(a).textBaseline(b).textFont(c)},strokeStyle:function(a,b,c,d){if(a&&(b&&(this.c.lineWidth=b),c&&(this.c.strokeStyle=c),d))this.c.lineJoin=d;return this},globalAlpha:function(a){a&&(this.c.globalAlpha=a);return this},fillStyle:function(a){a&&(this.c.fillStyle=a);return this},arc2:function(a,b,c,d,e,f){c&&this.c.arc(a,b,c,d,e,f);return this},textAlign:function(a){a&&(this.c.textAlign=a);return this},textBaseline:function(a){a&&(this.c.textBaseline=a);return this},textFont:function(a){a&&(this.c.font=a);return this},shadowOn:function(a){a&&(this.c.shadowColor=a.color,this.c.shadowBlur=a.blur,this.c.shadowOffsetX=a.offsetx,this.c.shadowOffsetY=a.offsety);return this},shadowOff:function(){this.c.shadowColor="white";this.c.shadowBlur=this.c.shadowOffsetX=this.c.shadowOffsetY=0;return this},gradient:function(a,b,c,d,e,f,g){var f=f.toLowerCase(),k=a,j=b,n=!f.indexOf("linear"),f=f.substring(14);if(n){switch(f){case "updown":j+=d;break;case "downup":b+=d;break;case "leftright":k+=c;break;case "rightleft":a+=c;break;default:return e[0]}return this.avgLinearGradient(a,b,k,j,e)}a+=c/2;b+=d/2;return this.avgRadialGradient(a,b,g||0,a,b,c>d?d:c,"outin"==f?e.reverse():e)},avgLinearGradient:function(a,b,c,d,e){a=this.createLinearGradient(a,b,c,d);for(b=0;b<e.length;b++)a.addColorStop(b/(e.length-1),e[b]);return a},createLinearGradient:function(a,b,c,d){return this.c.createLinearGradient(a,b,c,d)},avgRadialGradient:function(a,b,c,d,e,f,g){a=this.createRadialGradient(a,b,c,d,e,f);for(b=0;b<g.length;b++)a.addColorStop(b/(g.length-1),g[b]);return a},createRadialGradient:function(a,b,c,d,e,f){return this.c.createRadialGradient(a,b,c,d,e,f)},text:function(a,b,c,d,e,f,g,k,j,n,l,o){return this.save().textStyle(f,g,k).fillText(a,b,c,d,e,j,n,l,o).restore()},fillText:function(a,b,c,d,e,g,k,j,l){a=a.toString();if(!a||!a.length)return this;d=d||!1;g=g||"lr";k=k||16;b=o(0,b);c=o(0,c);a=a.split("tb"==g?"":"\n");1<a.length&&("middle"==this.c.textBaseline?c-=(a.length-1)*k/2:"bottom"==this.c.textBaseline&&(c-=(a.length-1)*k));this.save().fillStyle(e).translate(b,c).rotate(f*l).shadowOn(j);a.each(function(a,i){try{d&&0<d?this.c.fillText(a,0,i*k,d):this.c.fillText(a,0,i*k)}catch(e){console.log(e.message+"["+a+","+b+","+c+"]")}},this);return this.restore()},measureText:function(a){var a=a.split("\n"),b=0;a.each(function(a){b=l(this.measureText(a).width,b)},this.c);return b},moveTo:function(a,b){this.c.moveTo(a||0,b||0);return this},lineTo:function(a,b){this.c.lineTo(a||0,b||0);return this},save:function(){this.c.save();return this},restore:function(){this.c.restore();return this},beginPath:function(){this.c.beginPath();return this},closePath:function(){this.c.closePath();return this},stroke:function(a){a&&this.c.stroke();return this},fill:function(a){a&&this.c.fill();return this},cube:function(b,c,d,e,f,g,k,j,l,n,m,q){b=o(n,b);c=o(n,c);k=k&&0<k?k:f;e=c-k*e;d=o(n,b+k*d);e=o(n,e);q&&(this.polygon(j,l,n,m,q,!1,[{x:b,y:c},{x:d,y:e},{x:d+f,y:e},{x:b+f,y:c}]),this.polygon(j,l,n,m,q,!1,[{x:b,y:c},{x:b,y:c+g},{x:b+f,y:c+g},{x:b+f,y:c}]),this.polygon(j,l,n,m,q,!1,[{x:b+f,y:c},{x:d+f,y:e},{x:d+f,y:e+g},{x:b+f,y:c+g}]));this.polygon(a.dark(j),l,n,m,!1,!1,[{x:b,y:c},{x:d,y:e},{x:d+f,y:e},{x:b+f,y:c}]);this.polygon(j,l,n,m,!1,!1,[{x:b,y:c},{x:b,y:c+g},{x:b+f,y:c+g},{x:b+f,y:c}]);this.polygon(a.dark(j),l,n,m,!1,!1,[{x:b+f,y:c},{x:d+f,y:e},{x:d+f,y:e+g},{x:b+f,y:c+g}]);return this},cube3D:function(b,c,d,e,f,g,k,j,l,n,m,q){b=o(n,b);c=o(n,c);j=!j||0==j?g:j;f?(e=a.vectorP2P(d,e),d=b+j*e.x,e=c-j*e.y):(d=b+j*d,e=c-j*e);for(;6>q.length;)q.push(!1);d=o(n,d);e=o(n,e);j=[];0>e?a.isObject(q[4])&&j.push(a.applyIf({points:[{x:b,y:c-k},{x:d,y:e-k},{x:d+g,y:e-k},{x:b+g,y:c-k}]},q[4])):a.isObject(q[0])&&j.push(a.applyIf({points:[{x:b,y:c},{x:d,y:e},{x:d+g,y:e},{x:b+g,y:c}]},q[0]));a.isObject(q[1])&&j.push(a.applyIf({points:[{x:d,y:e},{x:d,y:e-k},{x:d+g,y:e-k},{x:d+g,y:e}]},q[1]));a.isObject(q[2])&&j.push(a.applyIf({points:[{x:b,y:c},{x:b,y:c-k},{x:d,y:e-k},{x:d,y:e}]},q[2]));a.isObject(q[3])&&j.push(a.applyIf({points:[{x:b+g,y:c},{x:b+g,y:c-k},{x:d+g,y:e-k},{x:d+g,y:e}]},q[3]));0>e?a.isObject(q[0])&&j.push(a.applyIf({points:[{x:b,y:c},{x:d,y:e},{x:d+g,y:e},{x:b+g,y:c}]},q[0])):a.isObject(q[4])&&j.push(a.applyIf({points:[{x:b,y:c-k},{x:d,y:e-k},{x:d+g,y:e-k},{x:b+g,y:c-k}]},q[4]));a.isObject(q[5])&&j.push(a.applyIf({points:[{x:b,y:c},{x:b,y:c-k},{x:b+g,y:c-k},{x:b+g,y:c}]},q[5]));j.each(function(a){this.polygon(a.color,l,n,m,a.shadow,a.alpha,a.points)},this);return this},polygon:function(a,b,c,d,e,f,g,k,j,l){this.save().strokeStyle(b,c,d).beginPath().fillStyle(a).globalAlpha(f).shadowOn(e).moveTo(g[0].x,g[0].y);if(k){this.moveTo(o(c,l[0].x),o(c,l[0].y)).lineTo(o(c,g[0].x),o(c,g[0].y));for(d=1;d<g.length;d++)this.bezierCurveTo(t(g,g[d],d,j));this.lineTo(o(c,l[1].x),o(c,l[1].y))}else for(d=1;d<g.length;d++)this.lineTo(o(c,g[d].x),o(c,g[d].y));return this.closePath().stroke(b).fill(a).restore()},lines:function(a,b,c,d){if(!b)return this;this.save().gCo(d).beginPath().strokeStyle(!0,b,c).moveTo(o(b,a[0]),o(b,a[1]));for(c=2;c<a.length-1;c+=2)this.lineTo(o(b,a[c]),o(b,a[c+1]));return this.stroke(!0).restore()},bezierCurveTo:function(a){this.c.bezierCurveTo(a[0],a[1],a[2],a[3],a[4],a[5]);return this},label:function(a,b,c){return this.save().beginPath().strokeStyle(!0,b,c).moveTo(o(b,a[0].x),o(b,a[0].y)).bezierCurveTo([a[1].x,a[1].y,a[2].x,a[2].y,a[3].x,a[3].y]).stroke(!0).restore()},lineArray:function(a,b,c,d,e){if(!b)return this;this.save().beginPath().strokeStyle(!0,b,c).moveTo(o(b,a[0].x),o(b,a[0].y));for(c=1;c<a.length;c++)d?this.bezierCurveTo(t(a,a[c],c,e||1.5)):this.lineTo(o(b,a[c].x),o(b,a[c].y));return this.stroke(!0).restore()},dotted:function(b,c,d,e,f,g,k,l,m){if(!f)return this;var b=o(f,b),c=o(f,c),d=o(f,d),e=o(f,e),n=a.distanceP2P(b,c,d,e),q;if(0>=k||n<=k||b!=d&&c!=e)return this.line(b,c,d,e,f,g,m);if(b>d||c>e)q=b,b=d,d=q,q=c,c=e,e=q;this.save().gCo(m).strokeStyle(!0,f,g).beginPath().moveTo(b,c);f=k*(l||1);g=j(n/(k+f));n=n-g*(k+f)>k;l=c==e;g=n?g+1:g;for(m=1;m<=g;m++)this.lineTo(l?b+k*m+f*(m-1):b,l?c:c+k*m+f*(m-1)).moveTo(l?b+(k+f)*m:b,l?c:c+(k+f)*m);n||this.lineTo(d,e);return this.stroke(!0).restore()},line:function(a,b,c,d,e,f,g){if(!e)return this;this.save().gCo(g);return this.beginPath().strokeStyle(!0,e,f).moveTo(o(e,a),o(e,b)).lineTo(o(e,c),o(e,d)).stroke(!0).restore()},round:function(a,b,c,d,e,f){return this.arc(a,b,c,0,0,k,d,!!f,e,f)},round0:function(a,b,c,d,e){return this.arc(a.x,a.y,b,0,0,k,c,!!e,d,e)},fillRect:function(a,b,c,d){this.c.fillRect(a,b,c,d);return this},translate:function(a,b){this.c.translate(a,b);return this},rotate:function(a){this.c.rotate(a);return this},clearRect:function(a,b,c,d){c=c||this.canvas.width;d=d||this.canvas.height;this.c.clearRect(a||0,b||0,c,d);return this},gCo:function(a){return a?this.gCO(a):this},gCO:function(a){this.c.globalCompositeOperation=a?"destination-over":"source-over";return this},box:function(b,e,f,l,m,q,s,p,t){m=m||{enable:0};if(m.enable){var n=m.width,r=m.color,v=m.radius,z=a.isNumber(n),n=a.parsePadding(n);n[0]==n[1]&&n[1]==n[2]&&n[2]==n[3]&&(z=!0);p=p?1:-1;f+=p*(n[1]+n[3])/2;l+=p*(n[0]+n[2])/2;b-=p*(n[3]/2);e-=p*(n[0]/2);n=z?n[0]:n;v=!z||!v||0==v||"0"==v?0:a.parsePadding(v)}this.save().gCo(t).fillStyle(q).strokeStyle(z,n,r);v?this.beginPath().moveTo(o(n,b+v[0]),o(n,e)).lineTo(o(n,b+f-v[1]),o(n,e)).arc2(o(n,b+f-v[1]),o(n,e+v[1]),v[1],3*c/2,k).lineTo(o(n,b+f),o(n,e+l-v[2])).arc2(o(n,b+f-v[2]),o(n,e+l-v[2]),v[2],0,c/2).lineTo(o(n,b+v[3]),o(n,e+l)).arc2(o(n,b+v[3]),o(n,e+l-v[3]),v[3],c/2,c).lineTo(o(n,b),o(n,e+v[0])).arc2(o(n,b+v[0]),o(n,e+v[0]),v[0],c,3*c/2).closePath().shadowOn(s).stroke(n).shadowOff().fill(q):!m.enable||z?(n&&m.enable&&(this.shadowOn(s).c.strokeRect(b,e,f,l),this.shadowOff()),q&&this.fillRect(b,e,f,l)):(n&&(r=a.isArray(r)?r:[r,r,r,r],this.shadowOn(s).line(b+f,e+n[0]/2,b+f,e+l-n[0]/2,n[1],r[1],0).line(b,e+n[0]/2,b,e+l-n[0]/2,n[3],r[3],0).line(j(b-n[3]/2),e,b+f+n[1]/2,e,n[0],r[0],0).line(j(b-n[3]/2),e+l,b+f+n[1]/2,e+l,n[2],r[2],0).shadowOff()),q&&this.beginPath().moveTo(j(b+n[3]/2),j(e+n[0]/2)).lineTo(g(b+f-n[1]/2),e+n[0]/2).lineTo(g(b+f-n[1]/2),g(e+l-n[2]/2)).lineTo(j(b+n[3]/2),g(e+l-n[2]/2)).lineTo(j(b+n[3]/2),j(e+n[0]/2)).closePath().fill(q));return this.restore()},toDataURL:function(a){return this.canvas.toDataURL(a||"image/png")},addEvent:function(b,c,d){a.Event.addEvent(this.canvas,b,c,d)}};a.taylor={light:function(a,b){b.highlight=!1;a.on("mouseover",function(){b.highlight=!0;a.redraw("mouseover")}).on("mouseout",function(){b.highlight=!1;a.redraw("mouseout")}).on("beforedraw",function(){a.push("f_color",b.highlight?a.get("light_color"):a.get("f_color_"));return!0})}};a.Chart=a.extend(a.Painter,{configure:function(){a.Chart.superclass.configure.apply(this,arguments);this.type="chart";this.dataType="simple";this.set({id:"",render:"",data:[],width:void 0,height:void 0,lineJoin:"round",align:"center",default_mouseover_css:!0,turn_off_touchmove:!1,showpercent:!1,decimalsnum:1,title:{text:"",fontweight:"bold",fontsize:20,height:30},subtitle:{text:"",fontweight:"bold",fontsize:16,height:20},footnote:{text:"",color:"#5d7f97",textAlign:"right",height:20},animation:!1,doAnimation:null,animation_timing_function:"easeInOut",animation_duration:1E3,z_index:999,legend:{enable:!1},tip:{enable:!1}});this.registerEvent("beforeAnimation","afterAnimation","resize","animating");this.T=null;this.show=this.Animationed=this.Combination=this.Rendered=!1;this.data=[];this.plugins=[];this.components=[];this.oneways=[];this.total=0;this.ICHARTJS_CHART=!0},toDataURL:function(a){return this.T.toDataURL(a)},segmentRect:function(){this.Combination||this.T.clearRect()},resetCanvas:function(){this.Combination||this.T.box(this.get("l_originx"),this.get("t_originy"),this.get("client_width"),this.get("client_height"),0,this.get("f_color"),0,0,!0)},animation:function(b){b.segmentRect();b.coo&&!b.ILLUSIVE_COO&&b.coo.draw();b.doAnimation(b.variable.animation.time,b.duration,b);b.plugins.each(function(a){a.A_draw&&(a.variable.animation.animating=!0,a.variable.animation.time=b.variable.animation.time,a.draw(),a.variable.animation.animating=!1)});b.Combination||(b.oneways.each(function(a){a.draw()}),b.variable.animation.time<b.duration?(b.variable.animation.time++,a.requestAnimFrame(function(){b.animation(b)})):a.requestAnimFrame(function(){b.Animationed=!0;b.plugins.each(function(a){a.Animationed=!0});b.processAnimation=!1;b.draw();b.plugins.each(function(a){a.processAnimation=!1});b.fireEvent(b,"afterAnimation",[b])}))},runAnimation:function(a){a.fireEvent(a,"beforeAnimation",[a]);a.A_draw||(a.variable.animation={type:0,time:0,queue:[]});a.processAnimation=!0;a.animation(a)},doSort:function(){var b=function(b,c){return(a.isArray(b)?b.zIndex||0:b.get("z_index"))>(a.isArray(c)?c.zIndex||0:c.get("z_index"))};this.components.sor(b);this.oneways.sor(b)},commonDraw:function(b,c){b.show=!1;b.redraw||(a.Assert.isTrue(b.Rendered,b.type+" has not rendered"),a.Assert.isTrue(b.data&&0<b.data.length,b.type+"'s data is empty"),a.Assert.isTrue(b.initialization,b.type+" Failed to initialize"),b.doSort());b.redraw=!0;!b.Animationed&&b.get("animation")?b.runAnimation(b):(b.segmentRect(),b.components.eachAll(function(a){a.draw(c)}),b.components.eachAll(function(a){a.last&&a.last(a)}),b.oneways.each(function(a){a.draw()}),b.show=!0)},plugin:function(a){var b=this._();a.inject(b);a.ICHARTJS_CHART&&(a.Combination=!0,a.setUp());b.get("animation")||a.push("animation",!1);a.duration=b.duration;b.register(a);b.plugins.push(a)},destroy:function(a){a.components.eachAll(function(a){a.destroy()});a.oneways.each(function(a){a.destroy()})},getTitle:function(){return this.title},getSubTitle:function(){return this.subtitle},getFootNote:function(){return this.footnote},getDrawingArea:function(){return{x:this.get("l_originx"),x:this.get("t_originy"),width:this.get("client_width"),height:this.get("client_height")}},create:function(c,d){if(c.get("fit")){var e=window.innerWidth,f=window.innerHeight,g=a.getDoc().body.style;g.padding="0px";g.margin="0px";g.overflow="hidden";c.push(c.W,e);c.push(c.H,f)}c.canvasid=a.uid(c.type);c.shellid="shell-"+c.canvasid;e=[];e.push("<div id='");e.push(c.shellid);e.push("' style='padding:0px;margin:0px;overflow:hidden;position:relative;'>");e.push("<canvas id= '");e.push(c.canvasid);e.push("' style='-webkit-text-size-adjust: none;'>");e.push("<p>Your browser does not support the canvas element</p></canvas>");e.push("</div>");d.innerHTML=e.join("");c.shell=a(c.shellid);c.T=c.target=new b(c.canvasid);c.size(c);c.Rendered=!0},setUp:function(){var a=this._();a.redraw=!1;a.T.clearRect();a.initialization=!1;a.initialize()},load:function(a){var b=this._();b.push("data",a||[]);b.setUp();(b.Combination?b.root:b).draw()},resize:function(b,c){var b=a.parseFloat(b),c=a.parseFloat(c),d=this._();d.Combination||(d.width=d.push(d.W,b),d.height=d.push(d.H,c),d.size(d));d.set(d.fireEvent(d,"resize",[b,c]));d.setUp();d.plugins.eachAll(function(a){a.Combination&&a.resize(b,c)});d.Combination||d.draw()},size:function(a){a.T.canvas.width=a.width=a.pushIf(a.W,400);a.T.canvas.height=a.height=a.pushIf(a.H,300);a.shell.style.width=a.width+"px";a.shell.style.height=a.height+"px"},initialize:function(){var b=this._(),c=b.get("data"),d=b.get("render");b.push(b.X,null);b.push(b.Y,null);b.Combination?(a.apply(b.options,a.clone([b.W,b.H,"padding","border","client_height","client_width","minDistance","maxDistance","centerx","centery","l_originx","r_originx","t_originy","b_originy"],b.root.options,!0)),b.width=b.get(b.W),b.height=b.get(b.H),b.shell=b.root.shell,b.Rendered=!0):b.Rendered||d&&b.create(b,a(d));b.Rendered&&!b.initialization&&(c&&0<c.length?v.call(b,c,b):a.isString(b.get("url"))&&b.ajax.call(b,b.get("url"),function(a){b.push("data",a);b.initialize();b.draw()}))},eventOff:function(){this.stopEvent=!0},eventOn:function(){this.stopEvent=!1},oneWay:function(b){b.T.strokeStyle(!0,0,b.get("strokeStyle"),b.get("lineJoin"));b.processAnimation=b.get("animation");a.isFunction(b.get("doAnimation"))&&(b.doAnimation=b.get("doAnimation"));b.animationArithmetic=a.getAA(b.get("animation_timing_function"));var c=b.variable.event,d=b.Combination,e=!b.get("turn_off_touchmove")&&!d,f=!a.touch&&b.get("default_mouseover_css")&&!d,k,j=a.touch?["touchstart","touchmove"]:["click","mousemove"];b.stopEvent=!1;b.A_draw=d&&b.processAnimation;a.register(b);d||j.each(function(c){b.T.addEvent(c,function(d){b.processAnimation||b.stopEvent||d.targetTouches&&d.targetTouches.length!=1||b.fireEvent(b,c,[b,a.Event.fix(d)])},false)});b.on(j[0],function(a,b){a.components.eachAll(function(a){if(a.ICHARTJS_CHART){if(a.fireEvent(a,j[0],[a,b])){c.click=true;return false}}else{var d=a.isMouseOver(b);if(d.valid){c.click=true;a.fireEvent(a,"click",[a,b,d]);return!b.stopPropagation}}});if(c.click){e&&b.event.preventDefault();c.click=false;return true}});if(!a.touch||e)if(b.on(j[1],function(a,b){k=false;a.components.eachAll(function(a){if(a.ICHARTJS_CHART){if(a.fireEvent(a,j[1],[a,b])){k=true;return false}}else{var c=a.variable.event,d=a.isMouseOver(b);if(d.valid){k=k||a.atomic;if(!c.mouseover){c.mouseover=true;a.fireEvent(a,"mouseover",[a,b,d])}a.fireEvent(a,"mousemove",[a,b,d]);if(d.stop)return false}else if(c.mouseover){c.mouseover=false;a.fireEvent(a,"mouseout",[a,b,d])}return!b.stopPropagation}});if(c.mouseover){b.event.preventDefault();if(!k){c.mouseover=false;a.fireEvent(a,"mouseout",[a,b])}return c.mouseover}if(k){c.mouseover=k;a.fireEvent(a,"mouseover",[a,b])}}),f)b.on("mouseover",function(){b.T.css("cursor","pointer")}).on("mouseout",function(){b.T.css("cursor","default")});a.applyIf(b.get("sub_option"),a.clone(["shadow","tip"],b.options,!0));b.Combination||(b.bg=new a.Custom({z_index:-1,drawFn:function(){b.T.box(0,0,b.width,b.height,b.get("border"),b.get("f_color"),0,0,true)}}),b.duration=g(b.get("animation_duration")*a.FRAME/1E3));b.oneWay=a.emptyFn},originXY:function(a,b,c){var d=a.get("align");d==a.L?a.pushIf(a.X,b[0]):d==a.R?a.pushIf(a.X,b[1]):a.pushIf(a.X,b[2]);a.x=a.push(a.X,a.get(a.X)+a.get("offsetx"));a.y=a.push(a.Y,c[0]+a.get("offsety"));return{x:a.x,y:a.y}},getPercent:function(a,b){return this.get("showpercent")?(100*(a/(b||this.total||1))).toFixed(this.get("decimalsnum"))+"%":a},doActing:function(b,c,d,e,f){var g=!!b.get("communal_acting"),k=b.getPercent(c.value,c.total);b.push(g?"sub_option":"communal_acting",a.clone(b.get(g?"communal_acting":"sub_option"),!0));a.merge(b.get("sub_option"),c);a.merge(b.get("sub_option"),d);b.push("sub_option.value",k);b.push("sub_option.value_",c.value);b.get("sub_option.tip.enable")&&(b.push("sub_option.tip.text",f||c.name+" "+k),b.push("sub_option.tip.name",c.name),b.push("sub_option.tip.index",e),b.push("sub_option.tip.value",c.value),b.push("sub_option.tip.total",c.total||b.total))},register:function(b){b.id=a.uid(b.type);this.components.push(b);return b},remove:function(a,b){b&&a.components.each(function(c,d){if(b.id==c.id)return a.components.splice(d,1),!1})},doConfig:function(){a.Chart.superclass.doConfig.call(this);var b=this._();b.destroy(b);b.oneways.length=0;b.oneWay(b);b.push("communal_acting",0);if(!b.Combination){b.oneways.push(b.bg);b.push("r_originx",b.width-b.get("padding_right"));b.push("b_originy",b.height-b.get("padding_bottom"));b.applyGradient();a.isString(b.get("title"))&&b.push("title",a.applyIf({text:b.get("title")},b.default_.title));a.isString(b.get("subtitle"))&&b.push("subtitle",a.applyIf({text:b.get("subtitle")},b.default_.subtitle));a.isString(b.get("footnote"))&&b.push("footnote",a.applyIf({text:b.get("footnote")},b.default_.footnote));var c=0,d=b.push("l_originx",b.get("padding_left")),e=b.push("t_originy",b.get("padding_top")),f=b.push("client_width",b.width-b.get("hpadding"));if(""!=b.get("title.text")){var g=""!=b.get("subtitle.text"),c=g?b.get("title.height")+b.get("subtitle.height"):b.get("title.height"),e=b.push("t_originy",e+c);b.push("title.originx",d);b.push("title.originy",b.get("padding_top"));b.push("title.maxwidth",f);b.pushIf("title.width",f);b.title=new a.Text(b.get("title"),b);b.oneways.push(b.title);g&&(b.push("subtitle.originx",d),b.push("subtitle.originy",b.get("padding_top")+b.get("title.height")),b.pushIf("subtitle.width",f),b.push("subtitle.maxwidth",f),b.subtitle=new a.Text(b.get("subtitle"),b),b.oneways.push(b.subtitle))}""!=b.get("footnote.text")&&(g=b.get("footnote.height"),c+=g,b.push("b_originy",b.get("b_originy")-g),b.push("footnote.originx",d),b.push("footnote.originy",b.get("b_originy")),b.push("footnote.maxwidth",f),b.pushIf("footnote.width",f),b.footnote=new a.Text(b.get("footnote"),b),b.oneways.push(b.footnote));c=b.push("client_height",b.get(b.H)-b.get("vpadding")-b.pushIf("other_height",c));b.push("minDistance",p(f,c));b.push("maxDistance",l(f,c));b.push("centerx",d+f/2);b.push("centery",e+c/2)}b.get("legend.enable")&&(b.legend=new a.Legend(a.apply({maxwidth:b.get("client_width"),data:b.data},b.get("legend")),b),b.oneways.push(b.legend));b.push("sub_option.tip.wrap",b.push("tip.wrap",b.shell))}})})(c);c.Custom=c.extend(c.Component,{configure:function(){c.Custom.superclass.configure.apply(this,arguments);this.type="custom";this.set({drawFn:c.emptyFn,configFn:c.emptyFn,eventValid:void 0,animating_draw:!0})},doDraw:function(a){a.get("drawFn").call(a,a)},isEventValid:function(a,b){return c.isFunction(this.get("eventValid"))?this.get("eventValid").call(this,a,b):{valid:!1}},doConfig:function(){c.Custom.superclass.doConfig.call(this);var a=this._();a.A_draw=a.get("animating_draw");a.variable.animation={animating:!1,time:0};a.duration=0;a.get("configFn").call(a,a)}});c.Scale=c.extend(c.Component,{configure:function(){c.Scale.superclass.configure.apply(this,arguments);this.type="scale";this.set({position:"left",which:"h",basic_value:0,scale2grid:!0,distance:void 0,start_scale:0,end_scale:void 0,min_scale:void 0,max_scale:void 0,scale_space:void 0,scale_share:5,scale_enable:!0,scale_size:1,scale_width:4,scale_color:"#333333",scaleAlign:"center",labels:[],label:{},text_space:6,textAlign:"left",decimalsnum:0,join_style:"none",join_size:2});this.registerEvent("parseText")},isEventValid:function(){return{valid:!1}},getScale:function(a){a=[a.get("basic_value"),a.get("start_scale"),a.get("end_scale"),a.get("end_scale")-a.get("start_scale"),0];a[4]=c.inRange(a[1],a[2]+1,a[0])||c.inRange(a[2]-1,a[1],a[0]);return{range:a[4],basic:a[4]?(a[0]-a[1])/a[3]:0,start:a[4]?a[0]:a[1],end:a[2],distance:a[3]}},doDraw:function(a){a.get("scale_enable")&&a.items.each(function(b){a.T.line(b.x0,b.y0,b.x1,b.y1,a.get("scale_size"),a.get("scale_color"),!1)});a.labels.each(function(a){a.draw()})},doLayout:function(a,b,c){c.get("scale_enable")&&c.items.each(function(c){c.x0+=a;c.y0+=b;c.x1+=a;c.y1+=b});c.labels.each(function(c){c.doLayout(a,b,0,c)})},doConfig:function(){c.Scale.superclass.doConfig.call(this);var a=this._(),b=Math.abs,d=a.get("labels").length,e=a.get("min_scale"),f=a.get("max_scale"),g=a.get("scale_space"),j=a.get("end_scale"),k=a.get("start_scale");a.items=[];a.labels=[];a.number=0;if(0<d)a.number=d-1;else{k>e&&(k=a.push("start_scale",c.floor(e)));if(!c.isNumber(j)||j<f)j=c.ceil(f),j=a.push("end_scale",!j&&!k?1:j);g&&b(g)<b(j-k)&&a.push("scale_share",(j-k)/g);a.number=a.push("scale_share",b(a.get("scale_share")));if(!g||g>j-k){g=(j-k+"").indexOf(".")+1;for(b=1;0<g;)g--,b*=10;g=a.push("scale",(j-k)*b/a.get("scale_share")/b)}parseInt(g)!=g&&0==a.get("decimalsnum")&&a.push("decimalsnum",(g+"").substring((g+"").indexOf(".")+1).length)}a.push("distanceOne",a.get("valid_distance")/a.number);var l,p,m,r=f=e=b=j=0,o=0;l=a.get("scale_width");p=l/2;m=a.get("scaleAlign");var t=a.get("position"),q=a.get("text_space"),v="",i=a.get("coo").get("axis.width");a.push("which",a.get("which").toLowerCase());a.isH="h"==a.get("which");a.isH?(m==a.O?f=-l:m==a.C?(f=-p,b=p):b=l,t==a.O?(o=-q-i[0],v=a.B):(o=q+i[2],v=a.O),t=a.C):(m==a.L?e=-l:m==a.C?(e=-p,j=p):j=l,v="middle",t==a.R)?(t=a.L,r=q+i[1]):(t=a.R,r=-q-i[3]);for(q=0;q<=a.number;q++)l=d?a.get("labels")[q]:(g*q+k).toFixed(a.get("decimalsnum")),p=a.isH?a.get("valid_x")+q*a.get("distanceOne"):a.x,m=a.isH?a.y:a.get("valid_y")+a.get("valid_distance")-q*a.get("distanceOne"),a.items.push({x:p,y:m,x0:p+e,y0:m+f,x1:p+j,y1:m+b}),a.get("label")&&a.labels.push(new c.Text(c.applyIf(c.apply(a.get("label"),c.merge({text:l,x:p,y:m,originx:p+r,originy:m+o},a.fireEvent(a,"parseText",[l,p+r,m+o,q,a.number==q]))),{textAlign:t,textBaseline:v}),a))}});c.Coordinate={coordinate_:function(a){var b=this._(),d=b.get("coordinate");if(d.ICHARTJS_OBJECT)return b.x=b.push(b.X,d.x),b.y=b.push(b.Y,d.y),b.ILLUSIVE_COO=!0,d;var d=c.parsePercent,e=b.get("coordinate.scale"),f=b.get("scaleAlign"),g=b.push("coordinate._width",d(b.get("coordinate.width")||"85%",Math.floor(b.get("client_width"))));h=b.push("coordinate._height",d(b.get("coordinate.height")||"85%",Math.floor(b.get("client_height")))-(b.is3D()?(b.get("coordinate.pedestal_height")||22)+(b.get("coordinate.board_deep")||20):0));b.push("coordinate.valid_height_value",d(b.get("coordinate.valid_height"),h));b.push("coordinate.valid_width_value",d(b.get("coordinate.valid_width"),g));b.originXY(b,[b.get("l_originx"),b.get("r_originx")-g,b.get("centerx")-g/2],[b.get("centery")-h/2]);b.push("coordinate.originx",b.x);b.push("coordinate.originy",b.y);a&&a();c.isObject(e)&&(e=[e]);if(c.isArray(e)){var j="stacked"!=b.dataType;e.each(function(a){b.get("percent")&&a.position==f&&(a=c.apply(a,{start_scale:0,end_scale:100,scale_space:10,listeners:{parseText:function(a){return{text:a+"%"}}}}));if(!a.start_scale||j&&a.start_scale>b.get("minValue"))a.min_scale=b.get("minValue");if(!a.end_scale||j&&a.end_scale<b.get("maxValue"))a.max_scale=b.get("maxValue")})}else b.push("coordinate.scale",{position:f,scaleAlign:f,max_scale:b.get("maxValue"),min_scale:b.get("minValue")});b.is3D()&&(b.push("coordinate.xAngle_",b.get("xAngle_")),b.push("coordinate.yAngle_",b.get("yAngle_")),b.push("coordinate.zHeight",b.get("zHeight")*b.get("bottom_scale")));b.remove(b,b.coo);return b.register(new (c[b.is3D()?"Coordinate3D":"Coordinate2D"])(b.get("coordinate"),b))}};c.Coordinate2D=c.extend(c.Component,{configure:function(){c.Coordinate2D.superclass.configure.apply(this,arguments);this.type="coordinate2d";this.set({sign_size:12,sign_space:5,scale:[],width:"85%",height:"85%",valid_width:"100%",valid_height:"100%",grid_line_width:1,grid_color:"#dbe1e1",gridHStyle:{},gridVStyle:{},gridlinesVisible:!0,scale2grid:!0,grids:void 0,ignoreOverlap:!0,ignoreEdge:!1,xlabel:"",ylabel:"",background_color:0,striped:!0,striped_direction:"v",striped_factor:0.01,crosshair:{enable:!1},z_index:-1,axis:{enable:!0,color:"#666666",width:1}});this.scale=[];this.gridlines=[]},getScale:function(a,b){for(var c=this._(),e=0;e<c.scale.length;e++)if(c.scale[e].get("position")==a)return c.scale[e].getScale(c.scale[e]);if(!b)return a=a==c.L?c.R:a==c.R?c.L:a==c.O?c.B:c.O,c.getScale(a,!0);throw Error("there no valid scale");},isEventValid:function(a,b){return{valid:a.x>b.x&&a.x<b.x+b.width&&a.y<b.y+b.height&&a.y>b.y}},doDraw:function(a){a.T.box(a.x,a.y,a.width,a.height,0,a.get("f_color"));if(a.get("striped")){var b,d,e=!1;a.get("axis.width");var f=c.dark(a.get("background_color"),a.get("striped_factor"),0)}var g="v"==a.get("striped_direction");a.gridlines.each(function(c){a.get("striped")&&(e&&(g?a.T.box(c.x1,c.y1+c.width,c.x2-c.x1,d-c.y1-c.width,0,f):a.T.box(b+c.width,c.y2,c.x1-b,c.y1-c.y2,0,f)),b=c.x1,d=c.y1,e=!e)}).each(function(b){b.overlap||(b.solid?a.T.line(b.x1,b.y1,b.x2,b.y2,b.width,b.color):a.T.dotted(b.x1,b.y1,b.x2,b.y2,b.width,b.color,b.size,b.fator))});a.T.box(a.x,a.y,a.width,a.height,a.get("axis"),!1,a.get("shadow"),!0);a.scale.each(function(a){a.draw()})},destroy:function(){this.crosshair&&this.crosshair.destroy()},doCrosshair:function(a){a.get("crosshair.enable")&&!a.crosshair&&(a.push("crosshair.wrap",a.root.shell),a.push("crosshair.height",a.height),a.push("crosshair.width",a.width),a.push("crosshair.top",a.y),a.push("crosshair.left",a.x),a.crosshair=new c.CrossHair(a.get("crosshair"),a))},doConfig:function(){c.Coordinate2D.superclass.doConfig.call(this);var a=this._();a.atomic=!1;a.width=a.get("_width");a.height=a.get("_height");a.valid_width=a.get("valid_width_value");a.valid_height=a.get("valid_height_value");a.get("gradient")&&c.isString(a.get("f_color"))&&a.push("f_color",a.T.avgLinearGradient(a.x,a.y,a.x,a.y+a.height,[a.get("dark_color"),a.get("light_color")]));if(a.get("axis.enable")){var b=a.get("axis.width");c.isArray(b)||a.push("axis.width",[b,b,b,b])}else a.push("axis.width",[0,0,0,0]);a.doCrosshair(a);var d,e=(b=!(!a.get("gridlinesVisible")||!a.get("grids")))&&!!a.get("grids.horizontal"),f=b&&!!a.get("grids.vertical"),g=a.height,j=a.width,k=a.valid_width,l=a.valid_height,b=a.get("gridlinesVisible")&&a.get("scale2grid")&&!(e&&f),p=a.push("x_start",a.x+(j-k)/2),m=a.push("y_start",a.y+(g-l)/2),r=a.get("axis.width");a.push("x_end",a.x+(j+k)/2);a.push("y_end",a.y+(g+l)/2);c.isArray(a.get("scale"))||(c.isObject(a.get("scale"))?a.push("scale",[a.get("scale")]):a.push("scale",[]));a.get("scale").each(function(b){d=(d=b.position)||a.L;d=d.toLowerCase();b[a.X]=a.x;b.coo=a;b[a.Y]=a.y;b.valid_x=p;b.valid_y=m;b.position=d;d==a.O?(b.which="h",b.distance=j,b.valid_distance=k):d==a.R?(b.which="v",b.distance=g,b.valid_distance=l,b[a.X]+=j,b.valid_x+=k):d==a.B?(b.which="h",b.distance=j,b.valid_distance=k,b[a.Y]+=g,b.valid_y+=l):(b.which="v",b.distance=g,b.valid_distance=l);a.scale.push(new c.Scale(b,a.root))},a);var o=a.push("ignoreOverlap",a.get("ignoreOverlap")&&a.get("axis.enable")||a.get("ignoreEdge"));if(o)var t=a.get("ignoreEdge")?function(b,c,d){return"v"==b?d==a.y||d==a.y+g:c==a.x||c==a.x+b}:function(b,c,d){return"v"==b?d==a.y&&0<r[0]||d==a.y+g&&0<r[2]:c==a.x&&0<r[3]||c==a.x+j&&0<r[1]};var q={solid:!0,size:10,fator:1,width:a.get("grid_line_width"),color:a.get("grid_color")},v=c.applyIf(a.get("gridHStyle"),q),i=c.applyIf(a.get("gridVStyle"),q);if(b){var u,y,w;a.scale.each(function(b){w=b.get("position");if(!c.isFalse(b.get("scale2grid"))&&!(e&&"v"==b.get("which")||f&&"h"==b.get("which")))u=y=0,w==a.O?y=g:w==a.R?u=-j:w==a.B?y=-g:u=j,b.items.each(function(d){o&&a.gridlines.push(c.applyIf({overlap:t.call(a,b.get("which"),d.x,d.y),x1:d.x,y1:d.y,x2:d.x+u,y2:d.y+y},b.isH?i:v))})})}if(f){var x=a.get("grids.vertical");c.Assert.isTrue(0<x.value,"vertical value");b=j/x.value;q=x.value;"given_value"==x.way&&(q=b,b=x.value,b=b>j?j:b);for(x=0;x<=q;x++)o&&a.gridlines.push(c.applyIf({overlap:t.call(a,"h",a.x+x*b,a.y),x1:a.x+x*b,y1:a.y,x2:a.x+x*b,y2:a.y+g,H:!1,width:i.width,color:i.color},i))}if(e){x=a.get("grids.horizontal");c.Assert.isTrue(0<x.value,"horizontal value");b=g/x.value;q=x.value;"given_value"==x.way&&(q=b,b=x.value,b=b>g?g:b);for(x=0;x<=q;x++)o&&a.gridlines.push(c.applyIf({overlap:t.call(a,"v",a.x,a.y+x*b),x1:a.x,y1:a.y+x*b,x2:a.x+j,y2:a.y+x*b,H:!0,width:v.width,color:v.color},v))}}});c.Coordinate3D=c.extend(c.Coordinate2D,{configure:function(){c.Coordinate3D.superclass.configure.apply(this,arguments);this.type="coordinate3d";this.dimension=c._3D;this.set({xAngle:60,yAngle:20,xAngle_:void 0,yAngle_:void 0,zHeight:0,pedestal_height:22,board_deep:20,left_board:!0,gradient:!0,color_factor:0.18,ignoreEdge:!0,striped:!1,grid_color:"#a4ad96",background_color:"#d6dbd2",shadow_offsetx:4,shadow_offsety:2,wall_style:[],axis:{enable:!1}})},doDraw:function(a){var b=a.width,c=a.height,e=a.get("xAngle_"),f=a.get("yAngle_"),g=a.get("zHeight"),j=a.get("z_offx"),k=a.get("z_offy");a.get("pedestal_height")&&a.T.cube3D(a.x,a.y+c+a.get("pedestal_height"),e,f,!1,b,a.get("pedestal_height"),3*g/2,a.get("axis.enable"),a.get("axis.width"),a.get("axis.color"),a.get("bottom_style"));a.get("board_deep")&&a.T.cube3D(a.x+j,a.y+c-k,e,f,!1,b,c,a.get("board_deep"),a.get("axis.enable"),a.get("axis.width"),a.get("axis.color"),a.get("board_style"));a.T.cube3D(a.x,a.y+c,e,f,!1,b,c,g,a.get("axis.enable"),a.get("axis.width"),a.get("axis.color"),a.get("wall_style"));a.gridlines.each(function(b){b.solid?(a.get("left_board")&&a.T.line(b.x1,b.y1,b.x1+j,b.y1-k,b.width,b.color),a.T.line(b.x1+j,b.y1-k,b.x2+j,b.y2-k,b.width,b.color)):(a.get("left_board")&&a.T.dotted(b.x1,b.y1,b.x1+j,b.y1-k,b.width,b.color,b.size,b.fator),a.T.dotted(b.x1+j,b.y1-k,b.x2+j,b.y2-k,b.width,b.color,b.size,b.fator))});a.scale.each(function(a){a.draw()})},doConfig:function(){c.Coordinate3D.superclass.doConfig.call(this);for(var a=this._(),b=a.get("wall_style"),d=a.get("background_color")||"#d6dbd2",e=a.height,f=a.width,g=a.get("color_factor"),j=a.push("z_offx",a.get("xAngle_")*a.get("zHeight")),k=a.push("z_offy",a.get("yAngle_")*a.get("zHeight"));6>b.length;)b.push({color:d});a.get("left_board")||(b[2]=!1,a.scale.each(function(a){a.doLayout(j,-k,a)}));a.push("bottom_style",[{color:a.get("shadow_color"),shadow:a.get("shadow")},!1,!1,{color:b[3].color},!1,{color:b[3].color}]);a.push("board_style",[!1,!1,!1,{color:b[4].color},{color:b[5].color},!1]);a.get("gradient")&&(c.isString(b[0].color)&&(b[0].color=a.T.avgLinearGradient(a.x,a.y+e,a.x+f,a.y+e,[c.dark(b[0].color,g/2+0.06),c.dark(b[0].color,g/2+0.06)])),c.isString(b[1].color)&&(b[1].color=a.T.avgLinearGradient(a.x+j,a.y-k,a.x+j,a.y+e-k,[c.dark(b[1].color,g),c.light(b[1].color,g)])),c.isString(b[2].color)&&(b[2].color=a.T.avgLinearGradient(a.x,a.y,a.x,a.y+e,[c.light(b[2].color,g/3),c.dark(b[2].color,g)])),a.get("bottom_style")[5].color=a.T.avgLinearGradient(a.x,a.y+e,a.x,a.y+e+a.get("pedestal_height"),[c.light(b[3].color,g/2+0.06),c.dark(b[3].color,g/2,0)]));a.push("wall_style",[b[0],b[1],b[2]])}});c.Rectangle=c.extend(c.Component,{configure:function(){c.Rectangle.superclass.configure.apply(this,arguments);this.type="rectangle";this.set({width:0,height:0,value_space:4,value:"",label:{},name:"",tipAlign:"top",valueAlign:"top",shadow_blur:3,shadow_offsety:-1});this.atomic=!0;this.registerEvent("parseText");this.label=null},last:function(a){a.label&&a.label.draw()},doDraw:function(a){a.drawRectangle()},doConfig:function(){c.Rectangle.superclass.doConfig.call(this);var a=this._(),b=a.variable.event,d=a.get("valueAlign");c.taylor.light(a,b);a.width=a.get(a.W);a.height=a.get(a.H);var b=a.push("centerx",a.x+a.width/2),e=a.push("centery",a.y+a.height/2),f=a.C,g="middle",j=a.get("value_space");d==a.L?(f=a.R,b=a.x-j):d==a.R?(f=a.L,b=a.x+a.width+j):d==a.B?(e=a.y+a.height+j,g=a.O):d==a.O&&(e=a.y-j,g=a.B);a.get("label")&&(a.push("label.originx",b),a.push("label.originy",e),a.push("label.text",a.push("value",a.fireString(a,"parseText",[a,a.get("value")],a.get("value")))),c.applyIf(a.get("label"),{textAlign:f,textBaseline:g,color:a.get("color")}),a.label=new c.Text(a.get("label"),a));a.get("tip.enable")&&("follow"!=a.get("tip.showType")&&a.push("tip.invokeOffsetDynamic",!1),a.tip=new c.Tip(a.get("tip"),a))}});c.Rectangle2D=c.extend(c.Rectangle,{configure:function(){c.Rectangle2D.superclass.configure.apply(this,arguments);this.type="rectangle2d";this.set({shadow_offsety:-2})},drawRectangle:function(){var a=this._();a.T.box(a.get(a.X),a.get(a.Y),a.get(a.W),a.get(a.H),a.get("border"),a.get("f_color"),a.get("shadow"))},isEventValid:function(a,b){return{valid:a.x>b.x&&a.x<b.x+b.width&&a.y<b.y+b.height&&a.y>b.y}},tipInvoke:function(){var a=this._();return function(b,c){return{left:a.tipX(b,c),top:a.tipY(b,c)}}},doConfig:function(){c.Rectangle2D.superclass.doConfig.call(this);var a=this._(),b=a.get("tipAlign");b==a.L||b==a.R?a.tipY=function(b,c){return a.get("centery")-c/2}:a.tipX=function(b){return a.get("centerx")-b/2};b==a.L?a.tipX=function(b){return a.x-a.get("value_space")-b}:b==a.R?a.tipX=function(){return a.x+a.width+a.get("value_space")}:a.tipY=b==a.B?function(){return a.y+a.height+3}:function(b,c){return a.y-c-3};a.applyGradient()}});c.Rectangle3D=c.extend(c.Rectangle,{configure:function(){c.Rectangle3D.superclass.configure.apply(this,arguments);this.type="rectangle3d";this.dimension=c._3D;this.set({zHeight:void 0,xAngle:60,yAngle:20,xAngle_:void 0,yAngle_:void 0,shadow_offsetx:2})},drawRectangle:function(){var a=this._();a.T.cube(a.get(a.X),a.get(a.Y),a.get("xAngle_"),a.get("yAngle_"),a.get(a.W),a.get(a.H),a.get("zHeight"),a.get("f_color"),a.get("border.enable"),a.get("border.width"),a.get("light_color"),a.get("shadow"))},isEventValid:function(a,b){return{valid:a.x>b.x&&a.x<b.x+b.get(b.W)&&a.y<b.y+b.get(b.H)&&a.y>b.y}},tipInvoke:function(){var a=this._();return function(b,c){return{left:a.topCenterX-b/2,top:a.topCenterY-c}}},doConfig:function(){c.Rectangle3D.superclass.doConfig.call(this);var a=this._();a.pushIf("zHeight",a.get(a.W));a.topCenterX=a.x+(a.get(a.W)+a.get(a.W)*a.get("xAngle_"))/2;a.topCenterY=a.y-a.get(a.W)*a.get("yAngle_")/2-a.get("value_space");a.get("valueAlign")==a.O&&a.label&&(a.label.push("textx",a.topCenterX),a.label.push("texty",a.topCenterY))}});c.Sector=c.extend(c.Component,{configure:function(){c.Sector.superclass.configure.apply(this,arguments);this.type="sector";this.set({value:"",name:"",ignored:!1,counterclockwise:!1,startAngle:0,middleAngle:0,endAngle:0,totalAngle:0,bound_event:"click",expand:!1,donutwidth:0,mutex:!1,increment:void 0,label_length:void 0,gradient_mode:"RadialGradientOutIn",mini_label_threshold_angle:15,mini_label:!1,label:{},rounded:!1});this.atomic=!0;this.registerEvent("changed","parseText");this.tip=this.label=null},bound:function(){this.expanded||this.toggle()},rebound:function(){this.expanded&&this.toggle()},toggle:function(){this.fireEvent(this,this.get("bound_event"),[this])},getDimension:function(){var a=this._();return{x:a.x,x:a.y,startAngle:a.get("startAngle"),middleAngle:a.get("middleAngle"),endAngle:a.get("endAngle")}},doDraw:function(a){a.get("ignored")||(a.label&&!a.get("mini_label")&&a.label.draw(),a.drawSector(),a.label&&a.get("mini_label")&&a.label.draw())},doText:function(a,b,d){a.push("label.originx",b);a.push("label.originy",d);a.push("label.textBaseline","middle");a.label=new c.Text(a.get("label"),a)},doLabel:function(a,b,d,e,f,g,j,k){a.push("label.originx",b);a.push("label.originy",d);a.push("label.quadrantd",e);a.push("label.line_points",f);a.push("label.labelx",g);a.push("label.labely",j);a.push("label.smooth",k);a.push("label.angle",a.get("middleAngle")%(2*Math.PI));a.label=new c.Label(a.get("label"),a)},isLabel:function(){return this.get("label")&&!this.get("mini_label")},doConfig:function(){c.Sector.superclass.doConfig.call(this);var a=this._(),b=a.variable.event,d=a.get("label"),e=a.get("bound_event"),f;a.get("rounded")?(a.push("startAngle",0),a.push("endAngle",2*Math.PI)):(c.taylor.light(a,b),a.push("totalAngle",a.get("endAngle")-a.get("startAngle")),d&&(a.get("mini_label")&&(a.get("mini_label_threshold_angle")*Math.PI/180>a.get("totalAngle")?a.push("mini_label",!1):c.apply(a.get("label"),a.get("mini_label"))),a.push("label.text",a.fireString(a,"parseText",[a,a.get("label.text")],a.get("label.text"))),a.pushIf("label.border.color",a.get("border.color")),a.push("label.scolor",a.get("background_color"))),a.variable.event.status=a.expanded=a.get("expand"),a.get("tip.enable")&&("follow"!=a.get("tip.showType")&&a.push("tip.invokeOffsetDynamic",!1),a.tip=new c.Tip(a.get("tip"),a)),b.poped=!1,a.on(e,function(){b.poped=true;a.expanded=!a.expanded;a.redraw(e);b.poped=false}),a.on("beforedraw",function(c,j){if(j==e){f=false;a.x=a.get(a.X);a.y=a.get(a.Y);if(a.expanded)if(a.get("mutex")&&!b.poped){a.expanded=false;f=true}else{a.x=a.x+a.get("inc_x");a.y=a.y-a.get("inc_y")}if(b.status!=a.expanded){a.fireEvent(a,"changed",[a,a.expanded]);f=true;b.status=a.expanded}d&&f&&a.label.doLayout(a.get("inc_x")*(a.expanded?1:-1),-a.get("inc_y")*(a.expanded?1:-1),2,a.label)}return true}))}});c.Sector2D=c.extend(c.Sector,{configure:function(){c.Sector2D.superclass.configure.apply(this,arguments);this.type="sector2d";this.set({radius:0})},drawSector:function(){this.T.sector(this.x,this.y,this.r,this.get("donutwidth"),this.get("startAngle"),this.get("endAngle"),this.get("f_color"),this.get("border.enable"),this.get("border.width"),this.get("border.color"),this.get("shadow"),this.get("counterclockwise"))},isEventValid:function(a,b){if(!b.get("ignored")){if(b.isLabel()&&b.label.isEventValid(a,b.label).valid)return{valid:!0};var d=c.distanceP2P(b.x,b.y,a.x,a.y),e=b.get("donutwidth");if(b.r<d||e&&b.r-e>d)return{valid:!1};if(c.angleInRange(b.get("startAngle"),b.get("endAngle"),c.atan2Radian(b.x,b.y,a.x,a.y)))return{valid:!0}}return{valid:!1}},tipInvoke:function(){var a=this,b=a.get("middleAngle"),d=c.quadrantd(b);return function(e,f){var g=c.p2Point(a.x,a.y,b,0.8*a.r);return{left:1<=d&&2>=d?g.x-e:g.x,top:2<=d?g.y-f:g.y}}},doConfig:function(){c.Sector2D.superclass.doConfig.call(this);var a=this._();a.r=a.get("radius");a.get("donutwidth")>a.r&&a.push("donutwidth",0);a.applyGradient(a.x-a.r,a.y-a.r,1.8*a.r,1.8*a.r);var b=a.get("middleAngle"),d=a.pushIf("increment",c.lowTo(5,a.r/10));a.push("inc_x",d*Math.cos(2*Math.PI-b));a.push("inc_y",d*Math.sin(2*Math.PI-b));d*=2;if(a.get("label"))if(a.get("mini_label"))P2=c.p2Point(a.x,a.y,b,a.get("donutwidth")?a.r-a.get("donutwidth")/2:5*a.r/8),a.doText(a,P2.x,P2.y);else{var e=c.quadrantd(b),f=c.p2Point(a.x,a.y,b,a.r+d),g=c.p2Point(a.x,a.y,b,a.r+0.6*d);P2=c.p2Point(a.x,a.y,b,a.r);a.doLabel(a,P2.x,P2.y,e,[{x:P2.x,y:P2.y},{x:g.x,y:g.y},{x:f.x,y:f.y}],f.x,f.y,0.4*d)}}});c.Sector3D=c.extend(c.Sector,{configure:function(){c.Sector3D.superclass.configure.apply(this,arguments);this.type="sector3d";this.dimension=c._3D;this.set({semi_major_axis:0,semi_minor_axis:0,cylinder_height:0});this.proxy=!0},isEventValid:function(a,b){if(!b.get("ignored")){if(b.isLabel()&&b.label.isEventValid(a,b.label).valid)return{valid:!0};if(!c.inEllipse(a.x-b.x,a.y-b.y,b.a,b.b))return{valid:!1};if(c.angleZInRange(b.sA,b.eA,c.atan2Radian(b.x,b.y,a.x,a.y)))return{valid:!0}}return{valid:!1}},p2p:function(a,b,c,e){return{x:a+this.a*Math.cos(c)*e,y:b+this.b*Math.sin(c)*e}},tipInvoke:function(){var a=this,b=a.get("middleAngle"),d=c.quadrantd(b);return function(c,f){var g=a.p2p(a.x,a.y,b,0.6);return{left:2<=d&&3>=d?g.x-c:g.x,top:3<=d?g.y-f:g.y}}},doConfig:function(){c.Sector3D.superclass.doConfig.call(this);var a=this._(),b=a.get("counterclockwise"),d=a.get("middleAngle");a.a=a.get("semi_major_axis");a.b=a.get("semi_minor_axis");a.h=a.get("cylinder_height");c.Assert.isTrue(0<=a.a*a.b,"major&minor");var e=2*Math.PI,f=function(d){for(;0>d;)d+=e;return Math.abs(c.atan2Radian(0,0,a.a*Math.cos(d),b?-a.b*Math.sin(d):a.b*Math.sin(d)))},g=a.pushIf("increment",c.lowTo(5,a.a/10));a.sA=f.call(a,a.get("startAngle"));a.eA=f.call(a,a.get("endAngle"));a.mA=f.call(a,d);a.push("inc_x",g*Math.cos(e-a.mA));a.push("inc_y",g*Math.sin(e-a.mA));g*=2;if(a.get("label"))if(a.get("mini_label"))g=a.p2p(a.x,a.y,d,0.5),a.doText(a,g.x,g.y);else{var f=c.quadrantd(d),j=a.p2p(a.x,a.y,d,g/a.a+1),k=a.p2p(a.x,a.y,d,0.6*g/a.a+1),d=a.p2p(a.x,a.y,d,1);a.doLabel(a,d.x,d.y,f,[{x:d.x,y:d.y},{x:k.x,y:k.y},{x:j.x,y:j.y}],j.x,j.y,0.4*g)}}});c.Pie=c.extend(c.Chart,{configure:function(){c.Pie.superclass.configure.call(this);this.type="pie";this.set({radius:"100%",offset_angle:0,separate_angle:0,bound_event:"click",counterclockwise:!1,intellectLayout:!0,layout_distance:4,pop_animate:!1,mutex:!1,increment:void 0,sub_option:{label:{}}});this.registerEvent("bound","rebound");this.sectors=[];this.components.push(this.sectors);this.ILLUSIVE_COO=!0},toggle:function(a){this.sectors[a||0].toggle()},bound:function(a){this.sectors[a||0].bound()},rebound:function(a){this.sectors[a||0].rebound()},getSectors:function(){return this.sectors},doAnimation:function(a,b,c){var e=0,f=c.oA;c.sectors.each(function(g){e=c.animationArithmetic(a,0,g.get("totalAngle"),b);g.push("startAngle",f);g.push("endAngle",f+=e);c.is3D()||g.drawSector()});c.is3D()&&c.proxy.drawSector()},parse:function(a){a.data.each(function(b,c){a.doParse(a,b,c)},a);a.localizer(a)},doParse:function(a,b,c){var e=b.name+" "+a.getPercent(b.value);a.doActing(a,b,null,c,e);a.push("sub_option.id",c);a.get("sub_option.label")&&a.push("sub_option.label.text",e);a.push("sub_option.listeners.changed",function(b,c){a.fireEvent(a,c?"bound":"rebound",[a,b.get("name")])});a.sectors.push(a.doSector(a,b))},doSector:function(a){return new c[a.sub](a.get("sub_option"),a)},dolayout:function(a,b,d,e,f,g){if(a.is3D()?c.inEllipse(a.get(a.X)-b,a.topY-d,a.a,a.b):c.distanceP2P(a.get(a.X),a.topY,b,d)<a.r)d=a.topY-d,e.push("labelx",a.get(a.X)+(2*Math.sqrt(a.r*a.r-d*d)+f)*(0==g||3==g?1:-1)),e.localizer(e)},localizer:function(a){if(a.get("intellectLayout")){var b=[],c=[],e=a.get("layout_distance"),f,g,j;a.sectors.each(function(a){a.isLabel()&&b.push(a.label)});b.sor(function(a,b){return 0<Math.abs(Math.sin(a.get("angle")))-Math.abs(Math.sin(b.get("angle")))});b.each(function(b){c.each(function(c){g=c.labelx;j=c.labely;if(b.labely<=j&&j-b.labely-1<b.get(a.H)||b.labely>j&&b.labely-j-1<c.get(a.H))if(b.labelx<=g&&g-b.labelx<b.get(a.W)||b.labelx>g&&b.labelx-g<c.get(a.W))f=b.get("quadrantd"),b.push("labely",b.get("labely")+j-b.labely+(b.get(a.H)+e)*(1<f?-1:1)),b.localizer(b),a.dolayout(a,b.get("labelx"),b.get("labely")+b.get(a.H)/2*(2>f?-1:1),b,e,f)},a);c.push(b)})}},doConfig:function(){c.Pie.superclass.doConfig.call(this);var a=this._(),b,d=a.get("radius"),e=a.get("sub_option.label")?0.35:0.44,f=2*Math.PI;a.sub=a.is3D()?"Sector3D":"Sector2D";a.sectors.zIndex=a.get("z_index");a.sectors.length=0;a.oA=c.angle2Radian(a.get("offset_angle"))%f;a.is3D()&&(e+=0.06);var g=a.data.length,j=c.angle2Radian(c.between(0,90,a.get("separate_angle"))),k=f-j,j=j/g,l=a.oA+j,p=l;0==a.total&&(b=1/g);a.data.each(function(c,d){l+=(b||c.value/a.total)*k;d==g-1&&(l=f+a.oA);c.startAngle=p;c.endAngle=l;c.totalAngle=l-p;c.middleAngle=(p+l)/2;p=l+j},a);a.r=d=c.parsePercent(d,Math.floor(a.get("minDistance")*e));a.topY=a.originXY(a,[d+a.get("l_originx"),a.get("r_originx")-d,a.get("centerx")],[a.get("centery")]).y;c.apply(a.get("sub_option"),c.clone([a.X,a.Y,"bound_event","mutex","increment"],a.options))}});c.Pie2D=c.extend(c.Pie,{configure:function(){c.Pie2D.superclass.configure.call(this);this.type="pie2d"},doConfig:function(){c.Pie2D.superclass.doConfig.call(this);var a=this._();a.push("sub_option.radius",a.r);a.parse(a)}});c.register("Pie2D");c.Pie3D=c.extend(c.Pie,{configure:function(){c.Pie3D.superclass.configure.apply(this,arguments);this.type="pie3d";this.dimension=c._3D;this.set({zRotate:45,yHeight:30});this.positive=!0},doSector:function(a,b){a.push("sub_option.cylinder_height",b.cylinder_height?b.cylinder_height*a.get("zRotate_"):a.get("cylinder_height"));return new c[a.sub](a.get("sub_option"),a)},one:function(a){var b,d,e=[],f=a.get("counterclockwise"),g=function(a,b){return 1+Math.sin(b?a+Math.PI:a)},j;lay=function(a,d,e,f){j=c.quadrantd(d);(a&&(0==j||3==j)||!a&&(2==j||1==j))&&b.push({g:d,z:d==e,x:f.x,y:f.y,a:f.a,b:f.b,color:c.dark(f.get("background_color")),h:f.h,F:f})};a.proxy=new c.Custom({z_index:a.get("z_index")+1,drawFn:function(){this.drawSector();e=[];a.sectors.each(function(a){a.get("label")&&(a.expanded?e.push(a.label):a.label.draw())});e.each(function(a){a.draw()})}});a.proxy.drawSector=function(){a.sectors.each(function(b){a.T.ellipse(b.x,b.y+b.h,b.a,b.b,b.get("startAngle"),b.get("endAngle"),0,b.get("border.enable"),b.get("border.width"),b.get("border.color"),b.get("shadow"),f,!0)},a);b=[];d=[];a.sectors.each(function(a){lay(f,a.get("startAngle"),a.get("endAngle"),a);lay(!f,a.get("endAngle"),a.get("startAngle"),a);d=d.concat(c.visible(a.get("startAngle"),a.get("endAngle"),a))},a);b.sor(function(a,b){var c=g(a.g)-g(b.g);return 0==c?a.z:0<c});b.each(function(b){a.T.sector3D.layerDraw.call(a.T,b.x,b.y,b.a+0.5,b.b+0.5,f,b.h,b.g,b.color)},a);a.processAnimation||d.sor(function(a,b){return 0>g((a.s+a.e)/2,1)-g((b.s+b.e)/2,1)});d.each(function(b){a.T.sector3D.sPaint.call(a.T,b.f.x,b.f.y,b.f.a,b.f.b,b.s,b.e,f,b.f.h,b.f.get("f_color"))},a);a.sectors.each(function(b){a.T.ellipse(b.x,b.y,b.a,b.b,b.get("startAngle"),b.get("endAngle"),b.get("f_color"),b.get("border.enable"),b.get("border.width"),b.get("border.color"),!1,!1,!0)},a)};a.one=c.emptyFn},doConfig:function(){c.Pie3D.superclass.doConfig.call(this);var a=this._(),b=c.angle2Radian(a.get("zRotate"));a.push("cylinder_height",a.get("yHeight")*a.push("zRotate_",Math.abs(Math.cos(b))));a.a=a.push("sub_option.semi_major_axis",a.r);a.b=a.push("sub_option.semi_minor_axis",a.r*Math.abs(Math.sin(b)));a.topY=a.push("sub_option.originy",a.get(a.Y)-a.get("yHeight")/2);a.parse(a);a.one(a);a.components.push(a.proxy)}});c.register("Pie3D");c.Donut2D=c.extend(c.Pie,{configure:function(){c.Donut2D.superclass.configure.call(this);this.type="donut2d";this.set({donutwidth:0.3,center:{text:"",line_height:24,fontweight:"bold",fontsize:24}})},doConfig:function(){c.Donut2D.superclass.doConfig.call(this);var a=this._(),b=a.r;a.push("sub_option.radius",b);0<a.get("donutwidth")&&(1>a.get("donutwidth")?a.push("donutwidth",Math.floor(b*a.get("donutwidth"))):a.get("donutwidth")>=b&&a.push("donutwidth",0),a.push("sub_option.donutwidth",a.get("donutwidth")));c.isString(a.get("center"))&&a.push("center",c.applyIf({text:a.get("center")},a.default_.center));""!=a.get("center.text")&&(a.push("center.originx",a.get(a.X)),a.push("center.originy",a.get(a.Y)),a.push("center.textBaseline","middle"),a.center=new c.Text(a.get("center"),a),a.components.push(a.center));a.parse(a)}});c.register("Donut2D");c.Column=c.extend(c.Chart,{configure:function(){c.Column.superclass.configure.call(this);this.type="column";this.set({coordinate:{},column_width:void 0,column_space:void 0,text_space:6,scaleAlign:"left",sub_option:{},label:{}});this.registerEvent();this.rectangles=[];this.labels=[];this.components.push(this.labels);this.components.push(this.rectangles)},doAnimation:function(a,b,c){var e;c.labels.each(function(a){a.draw()});c.rectangles.each(function(f){e=Math.ceil(c.animationArithmetic(a,0,f.height,b));f.push(c.Y,f.y+(f.height-e));f.push(c.H,e);f.drawRectangle()})},getCoordinate:function(){return this.coo},doLabel:function(a,b,d,e,f){a.labels.push(new c.Text(c.apply(a.get("label"),{id:b,text:d,originx:e,originy:f}),a))},doParse:function(a,b,c,e){a.doActing(a,b,e,c)},engine:function(a){var b=a.get("column_width"),c=a.get("column_space"),e=a.coo.getScale(a.get("scaleAlign")),f=a.coo.valid_height,g=b/2,j=b*(a.get("group_fator")||0),k="complex"!=a.dataType?b+c:a.data.length*b+c+(a.is3D()?(a.data.length-1)*j:0),l=a.coo.get("y_end"),p=l-e.basic*f-(a.is3D()?a.get("zHeight")*(a.get("bottom_scale")-1)/2*a.get("yAngle_"):0),m=c+a.coo.get("x_start"),l=l+a.get("text_space")+a.coo.get("axis.width")[2];a.doEngine(a,b,c,e,f,g,j,k,m,p,l)},doConfig:function(){c.Column.superclass.doConfig.call(this);var a=this._();a.sub=a.is3D()?"Rectangle3D":"Rectangle2D";a.rectangles.length=0;a.labels.length=0;a.rectangles.zIndex=a.get("z_index");a.labels.zIndex=a.get("z_index")+1;a.coo=c.Coordinate.coordinate_.call(a,function(){var b=a.data.length,c=a.get("coordinate.valid_width_value"),e,f,g;"complex"==a.dataType?(g=a.get("labels").length,b=g*b+(a.is3D()?(b-1)*g*a.get("group_fator"):0),e=Math.floor(c/(g+1+b)),f=a.pushIf("column_width",e),g+=1):("stacked"==a.dataType&&(b=a.get("labels").length),e=Math.floor(2*c/(3*b+1)),f=a.pushIf("column_width",e),g=b+1);f*b>c&&(f=a.push("column_width",e));a.push("column_space",(c-f*b)/g);a.is3D()&&(a.push("zHeight",a.get("column_width")*a.get("zScale")),a.push("sub_option.zHeight",a.get("zHeight")),a.push("sub_option.xAngle_",a.get("xAngle_")),a.push("sub_option.yAngle_",a.get("yAngle_")))});a.push("sub_option.width",a.get("column_width"))}});c.Column2D=c.extend(c.Column,{configure:function(){c.Column2D.superclass.configure.call(this);this.type="column2d"},doEngine:function(a,b,d,e,f,g,j,k,l,p,m){var r;a.data.each(function(b,d){r=(b.value-e.start)*f/e.distance;a.doParse(a,b,d,{id:d,originx:l+d*k,originy:p-(0<r?r:0),height:Math.abs(r)});a.rectangles.push(new c[a.sub](a.get("sub_option"),a));a.doLabel(a,d,b.name,l+k*d+g,m)},a)},doConfig:function(){c.Column2D.superclass.doConfig.call(this);this.engine(this)}});c.register("Column2D");c.Column3D=c.extend(c.Column2D,{configure:function(){c.Column3D.superclass.configure.call(this);this.type="column3d";this.dimension=c._3D;this.set({coordinate:{},xAngle:60,yAngle:20,zScale:1,bottom_scale:1.4})},doConfig:function(){c.Column3D.superclass.doConfig.call(this)}});c.register("Column3D");c.ColumnMulti2D=c.extend(c.Column,{configure:function(){c.ColumnMulti2D.superclass.configure.call(this);this.type="columnmulti2d";this.dataType="complex";this.set({labels:[]})},doEngine:function(a,b,d,e,f,g,j,k,l,p,m){var r;a.columns.each(function(g,t){g.item.each(function(d,g){r=(d.value-e.start)*f/e.distance;a.doParse(a,d,g,{id:t+"_"+g,originx:l+g*(b+j)+t*k,originy:p-(0<r?r:0),height:Math.abs(r)});a.rectangles.push(new c[a.sub](a.get("sub_option"),a))},a);a.doLabel(a,t,g.name,l-0.5*d+(t+0.5)*k,m)},a)},doConfig:function(){c.ColumnMulti2D.superclass.doConfig.call(this);this.engine(this)}});c.register("ColumnMulti2D");c.ColumnMulti3D=c.extend(c.ColumnMulti2D,{configure:function(){c.ColumnMulti3D.superclass.configure.call(this);this.type="columnmulti3d";this.dataType="complex";this.dimension=c._3D;this.set({xAngle:60,yAngle:20,zScale:1,group_fator:0.3,bottom_scale:1.4})},doConfig:function(){c.ColumnMulti3D.superclass.doConfig.call(this)}});c.register("ColumnMulti3D");c.ColumnStacked2D=c.extend(c.Column,{configure:function(){c.ColumnStacked2D.superclass.configure.call(this);this.type="columnstacked2d";this.dataType="stacked";this.set({percent:!1,labels:[],sub_option:{label:{color:"#ffffff"},valueAlign:"middle"}})},doEngine:function(a,b,d,e,f,g,j,k,l,p,m){var r,o,t,q=a.get("percent");a.columns.each(function(b,g){r=0;t=q?100/b.total:1;b.item.each(function(d,j){o=(d.value*t-e.start)*f/e.distance;d.total=b.total;a.doParse(a,d,j,{id:g+"_"+j,originx:l+g*k,originy:p-(0<o?o:0)-r,height:Math.abs(o)});r+=o;a.rectangles.push(new c[a.sub](a.get("sub_option"),a))},a);a.doLabel(a,g,b.name,l-0.5*d+(g+0.5)*k,m)},a)},doConfig:function(){c.ColumnStacked2D.superclass.doConfig.call(this);this.engine(this)}});c.register("ColumnStacked2D");c.ColumnStacked3D=c.extend(c.ColumnStacked2D,{configure:function(){c.ColumnStacked3D.superclass.configure.call(this);this.type="columnstacked3d";this.dataType="stacked";this.dimension=c._3D;this.set({percent:!1,sub_option:{label:{color:"#ffffff"},valueAlign:"middle"},coordinate:{},xAngle:60,yAngle:20,zScale:1,bottom_scale:1.4})},doConfig:function(){c.ColumnStacked3D.superclass.doConfig.call(this)}});c.register("ColumnStacked3D");c.Bar=c.extend(c.Chart,{configure:function(){c.Bar.superclass.configure.call(this);this.type="bar";this.set({coordinate:{striped_direction:"h"},bar_height:void 0,bar_space:void 0,text_space:6,scaleAlign:"bottom",sub_option:{},label:{}})},getCoordinate:function(){return this.coo},doLabel:function(a,b,d,e,f){a.labels.push(new c.Text(c.apply(a.get("label"),{id:b,text:d,textAlign:"right",textBaseline:"middle",originx:e,originy:f}),a))},doParse:function(a,b,c,e){a.doActing(a,b,e,c)},engine:function(a){var b=a.get("bar_height"),c=a.get("bar_space"),e=a.coo.getScale(a.get("scaleAlign")),f=a.coo.valid_width,g=b/2,j="complex"!=a.dataType?b+c:a.data.length*b+c,k=a.coo.get("x_start")+e.basic*f,l=a.coo.get(a.X)-a.get("text_space")-a.coo.get("axis.width")[3],p=a.coo.get("y_start")+c;a.doEngine(a,b,c,e,f,g,j,k,l,p)},doAnimation:function(a,b,c){c.labels.each(function(a){a.draw()});c.rectangles.each(function(e){e.push(c.W,Math.ceil(c.animationArithmetic(a,0,e.width,b)));e.drawRectangle()})},doConfig:function(){c.Bar.superclass.doConfig.call(this);var a=this._();a.rectangles=[];a.labels=[];a.rectangles.zIndex=a.get("z_index");a.labels.zIndex=a.get("z_index")+1;a.components.push(a.labels);a.components.push(a.rectangles);a.coo=c.Coordinate.coordinate_.call(a,function(){var b=a.data.length,c=a.get("coordinate.valid_height_value"),e,f,g;"complex"==a.dataType?(g=a.get("labels").length,b=g*b+(a.is3D()?(b-1)*g*a.get("group_fator"):0),e=Math.floor(c/(g+1+b)),f=a.pushIf("bar_height",e),g+=1):("stacked"==a.dataType&&(b=a.get("labels").length),e=Math.floor(2*c/(3*b+1)),f=a.pushIf("bar_height",e),g=b+1);f*b>c&&(f=a.push("bar_height",e));a.push("bar_space",(c-f*b)/g)});a.push("sub_option.height",a.get("bar_height"));a.push("sub_option.valueAlign",a.R);a.push("sub_option.tipAlign",a.R)}});c.Bar2D=c.extend(c.Bar,{configure:function(){c.Bar2D.superclass.configure.call(this);this.type="bar2d"},doEngine:function(a,b,d,e,f,g,j,k,l,p){var m;a.data.each(function(b,d){m=(b.value-e.start)*f/e.distance;a.doParse(a,b,d,{id:d,originy:p+d*j,width:Math.abs(m),originx:k+(0<m?0:-Math.abs(m))});a.rectangles.push(new c.Rectangle2D(a.get("sub_option"),a));a.doLabel(a,d,b.name,l,p+d*j+g)},a)},doConfig:function(){c.Bar2D.superclass.doConfig.call(this);this.engine(this)}});c.register("Bar2D");c.BarMulti2D=c.extend(c.Bar,{configure:function(){c.BarMulti2D.superclass.configure.call(this);this.type="barmulti2d";this.dataType="complex";this.set({labels:[]})},doEngine:function(a,b,d,e,f,g,j,k,l,p){var m;a.columns.each(function(g,o){g.item.each(function(d,g){m=(d.value-e.start)*f/e.distance;a.doParse(a,d,g,{id:o+"_"+g,originy:p+g*b+o*j,width:Math.abs(m),originx:k+(0<m?0:-Math.abs(m))});a.rectangles.push(new c.Rectangle2D(a.get("sub_option"),a))},a);a.doLabel(a,o,g.name,l,p-0.5*d+(o+0.5)*j)},a)},doConfig:function(){c.BarMulti2D.superclass.doConfig.call(this);this.engine(this)}});c.register("BarMulti2D");c.BarStacked2D=c.extend(c.Bar,{configure:function(){c.BarStacked2D.superclass.configure.call(this);this.type="barstacked2d";this.dataType="stacked";this.set({percent:!1,labels:[],sub_option:{label:{color:"#ffffff"},valueAlign:"middle"}})},doEngine:function(a,b,d,e,f,g,j,k,l,p){var m,r,o,t=a.get("percent");a.columns.each(function(b,g){m=0;o=t?100/b.total:1;b.item.each(function(d,l){r=(d.value*o-e.start)*f/e.distance;d.total=b.total;a.doParse(a,d,l,{id:g+"_"+l,originy:p+g*j,originx:k+(0<r?0:-Math.abs(r))+m,width:Math.abs(r)});m+=r;a.rectangles.push(new c.Rectangle2D(a.get("sub_option"),a))},a);a.doLabel(a,g,b.name,l,p-0.5*d+(g+0.5)*j)},a)},doConfig:function(){c.BarStacked2D.superclass.doConfig.call(this);this.push("sub_option.valueAlign",this.C);this.engine(this)}});c.register("BarStacked2D");c.LineSegment=c.extend(c.Component,{configure:function(){c.LineSegment.superclass.configure.apply(this,arguments);this.type="linesegment";this.set({brushsize:1,intersection:!0,label:{},sign:"round",area_color:null,hollow:!0,hollow_inside:!0,hollow_color:"#FEFEFE",smooth:!1,smoothing:1.5,point_size:6,points:[],keep_with_coordinate:!1,shadow_blur:1,shadow_offsety:1,point_space:0,coordinate:null,event_range_x:0,limit_y:!1,tip_offset:2,event_range_y:0});this.registerEvent("parseText");this.tip=null},drawSegment:function(){var a=this._();a.polygons.each(function(b){a.T.polygon.apply(a.T,b)});a.T.shadowOn(a.get("shadow"));a.lines.each(function(b){a.T.lineArray.apply(a.T,b)});a.intersections.each(function(b){a.sign_plugin?a.sign_plugin_fn.apply(a,b):a.T.round0.apply(a.T,b)});a.get("shadow")&&a.T.shadowOff()},doDraw:function(a){a.drawSegment();a.get("label")&&a.labels.each(function(a){a.draw()})},isEventValid:function(){},tipInvoke:function(){var a=this.x,b=this.get("tip_offset"),c=this.get("point_size")+b,e=this;return function(f,g,j){var k=j.left,j=j.top,k=3>e.tipPosition&&0<k-f-a-b||2<e.tipPosition&&0>k-f-a-b?k-(f+b):k+b,j=0==e.tipPosition%2?j+c:j-g-c;return{left:k,top:j}}},PP:function(a,b,c,e,f,g){a.get("area")&&a.polygons.push([a.get("area_color")||a.get("light_color2"),0,a.get("brushsize"),0,0,a.get("area_opacity"),a.get("smooth")?b:[{x:c,y:e}].concat(b.concat([{x:f,y:g}])),a.get("smooth"),a.get("smoothing")||1.5,[{x:c,y:e},{x:f,y:g}]])},parse:function(a){a.polygons=[];a.lines=[];a.intersections=[];a.labels=[];var b=a.get("points"),d=a.get("intersection"),e=!!a.get("label"),f=[],g=!1,j=a.get("smooth"),k=a.get("smoothing")||1.5,l=a.get("f_color"),p=a.get("brushsize"),m=a.get("point_size");if(d){var r=a.getPlugin("sign"),o=l,t=a.get("hollow_color");a.sign_plugin=c.isFunction(r);a.sign_plugin_fn=r;a.get("hollow_inside")&&(o=t,t=l)}b.each(function(b){b.x_=b.x;b.y_=b.y;!b.ignored&&e&&(a.push("label.originx",b.x),a.push("label.originy",b.y-m/2-1),a.push("label.text",a.fireString(a,"parseText",[a,b.value],b.value)),c.applyIf(a.get("label"),{textBaseline:"bottom",color:a.get("f_color")}),a.labels.push(new c.Text(a.get("label"),a)));b.ignored&&g?(a.lines.push([f,p,l,j,k]),a.PP(a,f,f[0].x,a.y,f[f.length-1].x,a.y),f=[],g=!1):b.ignored||(f.push(b),g=!0);d&&!b.ignored&&a.intersections.push(a.sign_plugin?[a.T,a.get("sign"),b,m,b.color||o,b.hollow_color||t]:a.get("hollow")?[b,m/2-p+1,b.color||o,p+1,b.hollow_color||t]:[b,m/2,b.color||o])});f.length&&(a.lines.push([f,p,l,j,k]),a.PP(a,f,f[0].x,a.y,f[f.length-1].x,a.y))},doConfig:function(){c.LineSegment.superclass.doConfig.call(this);c.Assert.isTrue(0<this.get("point_space"),"point_space");var a=this._(),b=3*a.get("point_size")/2,d=a.get("point_space"),e=a.get("event_range_y"),f=a.get("event_range_x"),g=a.get("tipInvokeHeap"),j=a.get("points"),k=a.get("name");a.parse(a);if(0>=f||f>d/2)f=a.push("event_range_x",d/2);0==e&&(e=a.push("event_range_y",b/2));a.get("tip.enable")&&(a.on("mouseover",function(){g.push(a);a.tipPosition=g.length}).on("mouseout",function(){g.pop()}),a.push("tip.invokeOffsetDynamic",!0),a.tip=new c.Tip(a.get("tip"),a));var l=a.get("coordinate"),p=a.get("limit_y"),m=a.get("keep_with_coordinate"),r=function(a,b,c){return!a.ignored&&Math.abs(b-a.x)<f&&(!p||p&&Math.abs(c-a.y)<e)?true:false},o=function(a){return{valid:true,name:k,value:j[a].value,text:j[a].text,top:j[a].y,left:j[a].x,i:a,hit:true}};a.isEventValid=function(b){if(l&&!l.isEventValid(b,l).valid)return{valid:false};var e=Math.floor((b.x-a.x)/d);if(e<0||e>=j.length-1){e=c.between(0,j.length-1,e);return r(j[e],b.x,b.y)?o(e):{valid:m}}for(var f=e;f<=e+1;f++)if(r(j[f],b.x,b.y))return o(f);return{valid:m}}}});c.Line=c.extend(c.Chart,{configure:function(){c.Line.superclass.configure.call(this);this.type="line";this.set({brushsize:1,coordinate:{axis:{width:[0,0,2,2]}},crosshair:{enable:!1},tipMocker:null,tipMockerOffset:null,scaleAlign:"left",labelAlign:"bottom",labels:[],label_space:6,proportional_spacing:!0,sub_option:{},legend:{sign:"bar"},label:{}});this.registerEvent("parsePoint");this.lines=[];this.components.push(this.lines)},getCoordinate:function(){return this.coo},doConfig:function(){c.Line.superclass.doConfig.call(this);var a=this._(),b=1==a.data.length;a.lines.length=0;a.lines.zIndex=a.get("z_index");var d=a.pushIf("sub_option.keep_with_coordinate",b);a.get("crosshair.enable")&&(a.pushIf("crosshair.hcross",b),a.push("crosshair.invokeOffset",function(b){b=a.lines[0].isEventValid(b);return b.valid?b:d}));a.Combination||(a.push("coordinate.crosshair",a.get("crosshair")),a.pushIf("coordinate.scale",[{position:a.get("scaleAlign"),max_scale:a.get("maxValue")},{position:a.get("labelAlign"),start_scale:1,scale:1,end_scale:a.get("maxItemSize"),labels:a.get("labels"),label:a.get("label")}]));a.coo=c.Coordinate.coordinate_.call(a);a.Combination&&(a.coo.push("crosshair",a.get("crosshair")),a.coo.doCrosshair(a.coo));var e=b=a.coo.valid_width,f=a.get("maxItemSize")-1,g=b/f,j=a.get("point_space");a.get("proportional_spacing")&&(j&&j<g?e=f*j:a.push("point_space",g));a.push("sub_option.width",e);a.push("sub_option.height",a.coo.valid_height);a.push("sub_option.originx",a.coo.get("x_start")+(b-e)/2);a.push("sub_option.originy",a.coo.get("y_end"));if(a.get("tip.enable")&&!a.mocker&&c.isFunction(a.get("tipMocker"))){a.push("sub_option.tip.enable",!1);a.push("tip.invokeOffsetDynamic",!0);var k,l=a.coo.get(a.X),p=a.coo.get(a.Y),m=a.coo.height,r=a.get("tipMockerOffset"),o,t,q,r=c.isNumber(r)?0>r?0:1<r?1:r:null;a.push("tip.invokeOffset",function(a,b,c){if(r!=null)c.top=p+(m-b)*r;else{c.top=c.maxTop-(c.maxTop-c.minTop)/3-b;if(b>m||p>c.top)c.top=p}return{left:c.left-a-l>5?c.left-a-5:c.left+5,top:c.top}});var v=a.get("tip.listeners.parseText");v&&delete a.get("tip.listeners").parseText;a.mocker=new c.Custom({eventValid:function(b){t=a.lines[0].isEventValid(b);t.hit=o!=t.i;if(t.valid){o=t.i;k=[];a.lines.each(function(a,c){q=a.isEventValid(b);if(c==0)t.minTop=t.maxTop=q.top;else{t.minTop=Math.min(t.minTop,q.top);t.maxTop=Math.max(t.maxTop,q.top)}k.push(v?v(null,q.name,q.value,q.text,q.i):q.name+" "+q.value)});t.text=a.get("tipMocker").call(a,k,t.i)||"tipMocker not return"}return t.valid?t:false}});new c.Tip(a.get("tip"),a.mocker);a.register(a.mocker)}a.pushIf("sub_option.area_opacity",a.get("area_opacity"))}});c.LineBasic2D=c.extend(c.Line,{configure:function(){c.LineBasic2D.superclass.configure.call(this);this.type="basicline2d";this.tipInvokeHeap=[]},doAnimation:function(a,b,c){c.lines.each(function(e){e.get("points").each(function(f){f.y=e.y-Math.ceil(c.animationArithmetic(a,0,e.y-f.y_,b))});e.drawSegment()})},doConfig:function(){c.LineBasic2D.superclass.doConfig.call(this);var a=this._(),b,d=a.coo.valid_height,e=a.get("point_space"),f,g,j,k=a.get("sub_option.originx"),l,p;a.push("sub_option.tip.showType","follow");a.push("sub_option.coordinate",a.coo);a.push("sub_option.tipInvokeHeap",a.tipInvokeHeap);a.push("sub_option.point_space",e);a.data.each(function(m){b=a.coo.getScale(m.scaleAlign||a.get("scaleAlign"));l=a.get("sub_option.originy")-b.basic*d;f=[];m.value.each(function(r,o){g=e*o;j=(r-b.start)*d/b.distance;p={x:k+g,y:l-j,value:r,text:m.name+" "+r};c.merge(p,a.fireEvent(a,"parsePoint",[m,r,g,j,o,b]));f.push(p)},a);c.merge(a.get("sub_option"),m);a.push("sub_option.points",f);a.push("sub_option.brushsize",m.linewidth||m.line_width);a.lines.push(new c.LineSegment(a.get("sub_option"),a))},this)}});c.register("LineBasic2D");c.Area2D=c.extend(c.LineBasic2D,{configure:function(){c.Area2D.superclass.configure.call(this);this.type="area2d";this.set({area_opacity:0.3})},doConfig:function(){this.push("sub_option.area",!0);c.Area2D.superclass.doConfig.call(this)}});c.register("Area2D")})(iChart);
