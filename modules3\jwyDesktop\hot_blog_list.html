﻿<!DOCTYPE html>
<html lang="en-US">
<head>
    <meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/>
    <meta name="format-detection" content="telephone=no"/>
    <meta name="viewport" content="width=device-width,initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0,user-scalable=no"/>
    <!--CSS-->
    <link rel="stylesheet" href="../../frame3/css/bingotouch.css"/>
    <link rel="stylesheet" href="../../frame3/css/app.css"/>

    <!--JS-->
    <script src="../../frame3/js/cordova.js"></script>
    <script src="../../frame3/js/zepto.js"></script>
    <script src="../../frame3/js/iscroll.js"></script>
    <script src="../../frame3/js/baiduTemplate.js"></script>
    <script src="../../frame3/js/bingotouch.js"></script>
    <script src="../../frame3/js/require.js"></script>
    <script src="../../frame3/js/plugin/linkplugins.js"></script>
    <script src="../../frame3/js/app/app.js"></script>
    <script src="../../js/xh_public.js"></script>
    <script src="../../js/xh_iscroll.js"></script>

    <script src="../../js/SzgaPlugin.js"></script>

    <title>BingoTouch</title>

    <!--引用url定义文件-->
    <script src="url.js" type="text/javascript"></script>

    <script type="text/javascript">
        var loginId;
        var userName;
        var deptName;
        var userPosition;
        var userId;

        var scrollList;

        var userPhotoList = {};

        var page = 1;
        var pageSize = 10;

        //Dom结构加载完成后执行
        app.page.onReady = function () {
            //to do
        }

        //页面所有元素加载完成后执行
        app.page.onLoad = function () {
            document.addEventListener("deviceready", function () {
                document.addEventListener("backbutton", function () {
                    app.back();
                }, false);
            }, false);

//            app.getMeid(function(result){
//                mMeid = result;
//            });

            scrollList = ui.IScroll.init(".pullrefresh-wrapper", {
                scrollBar : true,      //是否出现滚动条
                enablePullDown: false,   //是否允许下拉刷新
                enablePullUp: true,      //是否允许上拉刷新
                pullUpAction: loadMoreData
            });

            xh.getLoginInfo(function (res) {
                //alert("xh.getLoginInfo:\n" + JSON.stringify(res));
                loginId = res.loginId;
                userName = res.userName;
                userId = res.userId;

                loadData();
            });
        }

        //捕获全局js错误
        app.page.onError = function (msg, url, line) {
            //alert(msg);
        }

        function loadData() {
            var param = {
                "from": "hiboard",
                "userId": userId,
                "blogPageSize": pageSize,
                "blogPageNum": 1
            };

            //alert(JSON.stringify(param));
            ui.showMask("加载中..");

            app.ajax({
                "url": "http://20.97.6.131:3001/momentsuiservice/v1/moments/ui/getNutritionHotBlog",
                "data": param,
                "timeout": 30,
                "method": "POST",
                contentType: "application/json",
                "success": function (res) {
                    ui.hideMask();
                    if (debug) {
                        alert(res.returnValue);
                    }

                    var result = eval('(' + res.returnValue + ')');

                    var bt = baidu.template;
                    var html = "";
                    for (var i = 0; i < result.data.result.length; i++) {
                        if (result.data.result[i].blogAttachments == undefined) {
                            result.data.result[i].blogAttachments = [];
                        } else {
                            //alert(JSON.stringify(result.data.result[i].blogAttachments));
                        }
                        html += bt("hot_blog_list_item", result.data.result[i]);

                        addUserPhotoListById(result.data.result[i].userId);
                    }

                    $("#hot_blog_list").html(html);
                    $("#hot_blog_list").uiwidget();

                    scrollList.refresh();

                    //alert(JSON.stringify(userPhotoList));

                    for (var id in userPhotoList) {
                        if (userPhotoList[id] == "") {
                            getUserPhotoById(id);
                        }
                    }
                },
                "fail": function (res) {
                    ui.hideMask();
                    app.alert("服务器访问出错，请刷新页面重试");
                    //alert(res.returnValue);
                },
                "async": true
            });
        }

        function loadMoreData(refreshCallback) {
            page++;
            var param = {
                "from": "hiboard",
                "userId": userId,
                "blogPageSize": pageSize,
                "blogPageNum": page
            };

            app.ajax({
                "url": "http://20.97.6.131:3001/momentsuiservice/v1/moments/ui/getNutritionHotBlog",
                "data": param,
                "timeout": 30,
                "method": "POST",
                contentType: "application/json",
                "success": function (res) {
                    if (debug) {
                        alert(res.returnValue);
                    }

                    var result = eval('(' + res.returnValue + ')');

                    if (result.data.result.length == 0) {
                        page--;
                        if (refreshCallback) {
                            refreshCallback();
                        }
                        return;
                    }

                    var bt = baidu.template;
                    var html = "";
                    for (var i = 0; i < result.data.result.length; i++) {
                        if (result.data.result[i].blogAttachments == undefined) {
                            result.data.result[i].blogAttachments = [];
                        } else {
                            //alert(JSON.stringify(result.data.result[i].blogAttachments));
                        }
                        html += bt("hot_blog_list_item", result.data.result[i]);

                        addUserPhotoListById(result.data.result[i].userId);
                    }

                    $("#hot_blog_list").append(html);
                    $("#hot_blog_list").uiwidget();

                    if (refreshCallback) {
                        refreshCallback();
                    }

                    for (var id in userPhotoList) {
                        if (userPhotoList[id] == "") {
                            getUserPhotoById(id);
                        } else {
                            var mHeadImg = "data:image/jpeg;base64," + userPhotoList[id];
                            $(".user_id_" + id).attr("src", mHeadImg);
                        }
                    }
                },
                "fail": function (res) {
                    //app.alert("服务器访问出错，请刷新页面重试");

                    page--;
                    if (refreshCallback) {
                        refreshCallback();
                    }
                    //alert(res.returnValue);
                },
                "async": true
            });
        }

        function addUserPhotoListById(id) {
            if (userPhotoList[id] == undefined) {
                userPhotoList[id] = "";
            }
        }

        function getUserPhotoById(id) {
            var param = {
                userId: id,
                APP_URL: 'http://172.28.0.56:9999/service/user/photo',
                URL_TYPE: "JMT"
            };

            //alert(JSON.stringify(param));

            xh.post("http://172.28.0.56:9999/service/user/photo", param, function (res) {
                //alert(res.returnValue);
                var key = eval("("+res.returnValue+")");
                if (key.flag == 1) {
                    userPhotoList[id] = key.obj;
                    var mHeadImg = "data:image/jpeg;base64," + key.obj;
                    $(".user_id_" + id).attr("src", mHeadImg);
                } else {
                    //getPinGaoPic(mAttrPic);
                }
            }, function (res) {
                alert(JSON.stringify(res));
            });
        }

        function getDateTime(timestamp) {
            var date = new Date(timestamp);
            var year = date.getFullYear();
            var month = date.getMonth() + 1;
            if (month < 10) {
                month = "0" + month;
            }
            var day = date.getDate();
            if (day < 10) {
                day = "0" + day;
            }
            var hour = date.getHours();
            if (hour < 10) {
                hour = "0" + hour;
            }
            var minute = date.getMinutes();
            if (minute < 10) {
                minute = "0" + minute;
            }
            var second = date.getSeconds();
            if (second < 10) {
                second = "0" + second;
            }

            return year + "-" + month + "-" + day + " " + hour + ":" + minute + ":" + second
        }

        function openDongtaiDetail(blogId) {
            //alert(blogId);
            var params = {
                action: "xinghuo.mpaas.dynamic.ui.DynamicDefaultActivity1",
                names: ["dynamicId"],
                values: [blogId]
            };

            app.szgaplugin.startActivityForResult(params, function (res) {
                //alert("success: " + res);
            }, function (err) {
                //alert("fail: " + err);
            });
        }


    </script>

    <style type="text/css">
        .font-size-22 {
            font-size: 22px;
        }
        .font-size-18 {
            font-size: 18px;
        }
        .font-color-c8c8c8 {
            color: #c8c8c8;
        }
    </style>

</head>
<body class="desktop">
    <div id="section_container">
        <section id="index_section" class="active">
            <div data-role="page">
                <!--Header-->
                <div class="header" data-fixed="top">
                    <div class="title row-box" style="background: #4783e2 !important;border-color: #4783e2 !important;text-shadow: none;">
                        <div class="box-left">
                            <div data-role="BTButton" data-type="image" onClick="app.back();">
                                <img src="image/icon_back.jpg" alt="" width="30" height="30"/>
                            </div>
                        </div>
                        <div class="span1">
                            <!--<h1>营养热帖</h1>-->
                            <span style="font-size: 25px;">营养热帖</span>
                        </div>
                        <div class="box-right">
                            <div data-role="BTButton" data-type="image" onclick="app.refresh();">
                                <img src="image/icon_refresh.jpg" alt="" width="30" height="30"/>
                            </div>
                        </div>
                    </div>
                </div>

                <!--Content-->
                <div class="content pullrefresh-wrapper" style="background: #f5f5f5;">

                   <div>
                       <div class="container-fluid">
                           <ul class="list-view" id="hot_blog_list">

                           </ul>

                           <script type="text/html" id="hot_blog_list_item">
                               <li style="margin-bottom: 10px;">
                                   <div data-role="BTButton" mousedown="false" onclick="openDongtaiDetail('<%=blogId%>');" style="border-radius: 10px;">
                                       <div class="row-box" style="padding: 10px 0 0 0;margin: 0;">
                                           <div class="box-left">
                                               <% if (blogAttachments.length == 0) { %>
                                               <img src="image/default_image_hot_blog.jpg" height="75" width="100" style="border-radius: 5px;"/>
                                               <% } else { %>
                                               <img src="<%=blogAttachments[0].thumbnailsImageUrl%>" height="75" width="100" style="border-radius: 5px;"/>
                                               <% } %>
                                           </div>
                                           <div class="span1" style="padding-left: 10px;">
                                               <div class="thumbnail-text">
                                                   <h4><%=blogTitle%></h4>
                                                   <!--<span class="font-size-22" style="line-height: 1.5;"><%=blogTitle%></span>-->
                                                   <!--<div style="width: 70%;">-->
                                                       <!--<span class="font-size-22" style="line-height: 1.5;"><%=blogTitle%></span>-->
                                                   <!--</div>-->
                                                   <div class="row-box" style="margin-top: 10px;height: 25px;line-height: 25px;color: gray;">
                                                       <!--<div class="span8"><%=appTime%></div>-->
                                                       <div>
                                                           <img class="user_id_<%=userId%>" src="image/default_user_photo.png" style="width: 22px;height: 22px;border-radius: 10px;"/>
                                                       </div>
                                                       <div style="padding-top: 3px;padding-left: 10px;">
                                                           <span class="font-size-18 font-color-c8c8c8"><%=userName%></span>
                                                       </div>
                                                       <div style="padding-top: 3px;padding-left: 20px;">
                                                           <span class="font-size-18 font-color-c8c8c8"><%=getDateTime(createTime)%></span>
                                                       </div>
                                                   </div>
                                               </div>
                                           </div>
                                       </div>
                                   </div>
                               </li>
                           </script>

                           <script type="text/html" id="hot_blog_list_item2">
                               <li style="padding-bottom: 10px;padding-top: 10px;">
                                   <table onclick="openDongtaiDetail('<%=blogId%>');">
                                       <tr>
                                           <td style="width: 30%;text-align: left;vertical-align: top;">
                                               <% if (blogAttachments.length == 0) { %>
                                               <img src="image/default_pic.jpg" style="width: 90%;border-radius: 5px;"/>
                                               <% } else { %>
                                               <img src="<%=blogAttachments[0].thumbnailsImageUrl%>" style="width: 90%;border-radius: 5px;"/>
                                               <% } %>
                                           </td>
                                           <td style="width: 65%;vertical-align: top;">
                                               <div>
                                                   <span class="font-size-22" style="line-height: 1.5;"><%=blogTitle%></span>
                                               </div>
                                               <div style="margin-top: 7px;">
                                                   <div class="row-box">
                                                       <div>
                                                           <img class="user_id_<%=userId%>" src="image/default_user_photo.png" style="width: 22px;height: 22px;border-radius: 10px;"/>
                                                       </div>
                                                       <div style="padding-top: 3px;padding-left: 10px;">
                                                           <span class="font-size-18 font-color-c8c8c8"><%=userName%></span>
                                                       </div>
                                                       <div style="padding-top: 3px;padding-left: 20px;">
                                                           <span class="font-size-18 font-color-c8c8c8"><%=getDateTime(createTime)%></span>
                                                       </div>
                                                   </div>
                                               </div>
                                           </td>
                                           <td style="width: 15%;vertical-align: top;padding-top: 5px;text-align: right;padding-right: 15px;">
                                               <span class="font-size-16 font-color-ffffff" style="background: #1a5df9;padding: 3px 7px 3px 15px;border-top-left-radius: 11px;border-bottom-left-radius: 11px;">顶置</span>
                                           </td>
                                       </tr>
                                   </table>
                               </li>
                           </script>
                       </div>
                   </div>

                </div>

                <!--Footer-->
                <div class="footer" data-fixed="bottom"></div>
            </div>
        </section>


    </div>



</body>
</html>
