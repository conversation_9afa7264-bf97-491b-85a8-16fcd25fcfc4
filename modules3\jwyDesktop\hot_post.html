<!DOCTYPE HTML>
<html lang="en-US">
<head>
    <meta http-equiv="Content-Type" content="text/html;charset=UTF-8" />
    <meta name="format-detection" content="telephone=no" />
    <meta name="viewport" content="width=480,user-scalable=no" />
    <!--CSS-->
    <link rel="stylesheet" href="../../frame3/css/bingotouch.css" />
    <!-- 应用样式 -->
    <link rel="stylesheet" href="../../frame3/css/app.css" />

    <script src=../../js/vconsole.min.js></script>
    <script>
        //var vconsole = new VConsole()

    </script>


    <!-- frame 3.0 JS-->
    <script src="../../frame3/js/cordova.js"></script>
    <script src="../../frame3/js/zepto.js"></script>
    <script src="../../frame3/js/iscroll.js"></script>
    <script src="../../frame3/js/baiduTemplate.js"></script>
    <script src="../../frame3/js/bingotouch.js"></script>
    <script src="../../frame3/js/require.js"></script>

    <!-- 应用脚本 -->
    <script src="../../frame3/js/plugin/linkplugins.js"></script>
    <script src="../../frame3/js/app/app.js"></script>

    <!--old js-->
    <script src="../../js/xh_public.js"></script>
    <script src="../../js/SzgaPlugin.js"></script>

    <!--引用url定义文件-->
    <script src="url.js" type="text/javascript"></script>
    <style type="text/css">
        *{
            padding: 0;
            margin: 0;
            list-style-type:none;
        }
        .shenglvhao{
            overflow : hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
        }

        .blogTitle{
            overflow : hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
        }
    </style>
    <title>精华帖</title>

    <script type="text/javascript">
        
        var exitFlag = true;
        var userInfo;
        var deptCode;
        var terminalId;
        var access_token;

        //var currKey = "";
        //var status =1;
        var pageNo =1;
        var pageSize = 12;

        var mPullscroll;

        app.page.onReady = function() {

        };

        app.page.onLoad = function() {

            document.addEventListener("deviceready", function() {
                document.addEventListener("backbutton", function() {
                    app.back();
                }, false);
            }, false);
            initScroll();
            shuiyin();
            xh.getLoginInfo(function (result) {
                userInfo = result;
                app.link.getUserInfo(function (res) {
                    deptCode = res.deptCode;
                    app.getMeid(function(result){
                        terminalId = result;
                        ui.showMask("请稍候...");
                        xh.getToken(userInfo.loginId,function(res) {
                            access_token = res.returnValue;
                            pageNo = 1;
                            getBlogs();
                        })
                    });
                }, function (res) {
                    console.log(res);
                }, userInfo.userId);
            });

        };
        app.page.onError = function(){

        };

        /*function initview(){
            $("#btn_search2").tap(function() {
                currKey = $("#edt_keyword").val();
                pageNo = 1;
                getBlogs()
            });
        }*/
        function initScroll() {
            mPullscroll = ui.IScroll.init(".pullrefresh-wrapper", {
                scrollBar: true,      //是否出现滚动条
                enablePullDown: false,   //是否允许下拉刷新
                enablePullUp: true,     //是否允许上拉加载
                pullDownAction:null,  //下拉刷新调用方法*/
                pullUpAction: pullupRefresh  //上拉加载调用方法
            });
            $(".pullUp").hide();
        }


        function pullupRefresh(refreshCallback) {
            ui.showMask("请稍候...");
            pageNo = pageNo + 1;
            getBlogs(refreshCallback);
        }

        /*function getPagedata( refreshCallback){

            app.progress.start("温馨提示", "正在加载数据，请稍候...");
            xh.post2('http://172.28.0.56:9999/service/private/bingo/getGoodBlogPage',{
                pageNo:pageNo,
                pageSize:pageSize,
                userName:currKey,
                APP_URL:'http://172.28.0.56:9999/service/private/bingo/getGoodBlogPage'
            },function(res){
                app.progress.stop();
                var result=eval("("+res.returnValue+")");
                //alert(JSON.stringify(result));
                var data=result.obj;
//        alert(JSON.stringify(data.length))
//        alert(pageNo)
                //如果有数据，则加载，没有的话不要增加索引值
                if (data.length >0) {
                    if(pageNo === 1){
                        $("#jhdtList").empty();
                    }
                    var bt=baidu.template;
                    var htmlBj='';
                    for(var i=0;i<data.length;i++){
                        var obj=data[i];
                        obj.fbTime=formatTime(obj.publishTime);
                        if(obj.userImg==''){
                            obj.userImg="../../images/ICON/hedImg.png";
                        }else{
                            obj.userImg="data:image/jpeg;base64,"+obj.userImg;
                        }

                        htmlBj+=bt("jhdtList_app_list",obj);
                    }
                    $("#jhdtList").append(htmlBj);
                    $("#jhdtList").uiwidget();
                    refreshCallback && refreshCallback();
                    mPullscroll.refresh();
                } else {
                    if (pageNo == 1) {
                        app.hint("没有查找到信息！");
                    } else {
                        app.hint("没有更多数据了！");
                    }

                }


            },function(res){

            })
        }*/

        //获得月日得到日期oTime
        //获得月日得到日期oTime
        function formatTime(str) {
            //alert(str);


            var timeToShow = new Date(parseInt(str));
            var year = timeToShow.getFullYear();
            var month = timeToShow.getMonth() + 1;
            var day = timeToShow.getDate();
            var hour = timeToShow.getHours();
            var minutes = timeToShow.getMinutes();

        
            //alert(month + day);
           
            var oTime = getzf(month) + '-' + getzf(day);//最后拼接时间
            //return oTime;

            return formatTime(str);
        }

        //补0操作
        function getzf(num){
            if(parseInt(num) < 10){
                num = '0'+num;
            }
            return num;
        }


        function formatTime(value) {
            function add0(timeNum) {
                timeNum = parseInt(timeNum)
                if (timeNum < 10) {
                return (timeNum = "0" + timeNum);
                } else {
                return timeNum;
                }
            }
            value = parseInt(value)
            if (!value) return "";
            var timeToShow = new Date(value);
            var year = timeToShow.getFullYear();
            var month = timeToShow.getMonth() + 1;
            var day = timeToShow.getDate();
            var hour = timeToShow.getHours();
            var minutes = timeToShow.getMinutes();

            var now = new Date();
            var yearNow = now.getFullYear();
            var monthNow = now.getMonth() + 1;
            var dayNow = now.getDate();

            var timeStr = '';

            if (now - value <= 1000 * 60 * 6) {
                timeStr = '刚刚'
            } else if (month === monthNow && dayNow === day) {
                timeStr = '今天 ' + add0(hour) + ':' + add0(minutes)
            } else if (month === monthNow && dayNow === day + 1) {
                timeStr = '昨天 ' + add0(hour) + ':' + add0(minutes)
            } else {
                timeStr = year +"-" + add0(month) + "-" + add0(day) + " " + add0(hour) + ":" + add0(minutes);
            }
            return timeStr

        }

        function getBlogs(refreshCallback) {
            xh.post_jw(mpass_url + "/xinghuo-apaas-blogservice/blogService/im/getCreamBlog",{
                //xh.post_jw(blog_url + "/momentsuiservice/v1/moments/ui/getNutritionHotBlog",{
                userId: userInfo.userId,
                accessToken: access_token,
                backwords: true, 
                lastBlogMsgid: 0, 
                lastUpdateTime: "0", 
                blogNum: 10, 
                imId: userInfo.userCode,

                contentType: "application/json"
            },function (res) {
                $(".pullUp").hide();
                ui.hideMask();
                //alert(JSON.stringify(res.returnValue));
                console.log(res.returnValue);
                renderBlog(res.returnValue);
                refreshCallback && refreshCallback();
                mPullscroll.refresh();
            },function (e) {
                ui.hideMask();
                console.log(e);
                refreshCallback && refreshCallback();
                mPullscroll.refresh();
                $(".pullUp").hide();
            },function (header) {
                header.setRequestHeader("accessToken",access_token);
                header.setRequestHeader("requestType","app");
                header.setRequestHeader("applyID",applyId);
                header.setRequestHeader("secretKey",secretKey);
                header.setRequestHeader("userId",userInfo.userId);
                header.setRequestHeader("userCode",userInfo.userCode);
                header.setRequestHeader("terminalId",terminalId);
            })
        }
        function renderBlog(result) {
            var data = result.data;

            var bt = baidu.template;
            var html = '';

            var length = data.length > 5 ? 5 : data.length;

            for (var i = 0; i < length; i++) {
                var obj = data[i].blog;
                if (obj) {
                    obj.thumbUpNum = data[i].thumbUpNum;

                    obj.createTime = formatTime(obj.updateTime);

                    //var date = new Date(obj.createTime)
                    //obj.createTime = date.Format("HH:mm");
                    if (obj.msgContent != null) {
                        var msg = JSON.parse(obj.msgContent);
                        //alert(JSON.stringify(msg));
                        obj.blogTitle = msg.text.msg;

                        if (msg.picture != null && msg.picture.length > 0) {
                            obj.cover = msg.picture[0].pictureUrl;
                            console.log( obj.cover);
                        } else {
                            obj.cover = "";
                        }
                    }

                    html += bt("nutrition_blog_item", obj);
                }

            }

            $("#nutritionBlogList").html(html);
            $("#nutritionBlogList").uiwidget();
        }

        //进入精华帖详情
        function loadBlogDetail(blogId) {

            if (exitFlag) {
                exitFlag = false;
                setTimeout("exitFlag = true;", 1000);
                /*var params = {
                    appId: "com.xinghuodongtai.com",
                    entry:
                    "index.html?imi?imid=" + userInfo.userId +
                    "&blogid=" + blogId +
                    "&target=dynamicDetail&position=left",
                    query: {
                        IntegralNum: "",
                        spreadGrade: 2
                    },
                    appName: "新动态"
                };

                newPage(params);*/

                var url = mpass_url +
                        "/blogservice_front/index.html?accessToken=" + access_token +
                        "&imid=" + userInfo.userCode +
                        "&page=detail&msgId=" + blogId;
                        console.log(url);
                loadWebView(url, "");
            }
        }
        function loadWebView(url, params) {
            Cordova.exec(null, null, "Interactive", "loadWebView", [url, params]);
        }
        function newPage(params) {
            params = params || {};

            //console.log(params);

            console.log([params.appId, params.entry, params.query, params.appName]);

            Cordova.exec(null, null, "Page", "newPage", [
                params.appId,
                params.entry,
                params.query,
                params.appName
            ]);
        }
        function openNativeBlog(msgId) {
            Cordova.exec(function (res) {
                console.error("open blog " + msgId + " success.");
            }, function (error) {
                console.error(JSON.stringify(error));
            }, "DynamicPlugin", "toDynamicDetail", [msgId]);
        }
    </script>

    <!-- <script type="text/html" id="nutrition_blog_item">
        <li onclick="loadBlogDetail('<%=msgId%>')"
            style="overflow: hidden;margin: 0;padding: 0;border-bottom: 1px solid #EEEEEE;padding: 8px 0;">

            <div class="row-fluid" style="margin-top: 10px;">
                <div class="span2" style="width: 10%;text-align: center;">
                    <img style="width: 60px;height: 80px;margin-top: -8px;margin-left: 15px"
                         src="<%=cover%>"/>
                </div>
                <div class="span6 shenglvhao"
                     style="width: 62%;height: 30px;line-height: 30px;letter-spacing: 2px;color: #8F9193;padding-left: 10px;">
                    <%=blogTitle%>
                </div>
                <div class="span2" style="width: 20%;text-align: right;color: #8F9193;line-height: 30px;">
                    <%=createTime%>
                </div>

                <div class="span6"
                     style="text-align: left;color: #216793;margin-top: 10px;padding-left: 10px;">
                    <%=userName%>
                </div>
            </div>

        </li>
    </script> -->

    <script type="text/html" id="nutrition_blog_item">
        <li onclick="openNativeBlog('<%=msgId%>')"
            style="overflow: hidden;margin: 0;padding: 0;border-bottom: 1px solid #EEEEEE;padding: 15px;">


            <div style="display:flex;">
                <div style="display:flex;flex-direction:column;flex:1;min-height:80px;">
                    <div class="blogTitle thumbnail-text"
                         style="font-size:24px">
                        <%=blogTitle%>
                        
                    </div>


                    <div style="display:flex;align-items:center;align-content:center;flex:1;align-items:flex-end;">

                        <div class=""
                             style="text-align: left;color: #8F9193;">
                            <%=userName%>
                        </div>
                        <img style="width: 20px;height: 20px;margin-left: 15px"
                         src="img/read.png"/>
                        <div class=""
                             style="text-align: left;margin-left:5px;color: #8F9193;">
                            <%=thumbUpNum%>
                        </div>
                        <div style="display:flex;flex-direction:row-reverse;flex:1;">
                            <div class="" style="color: #8F9193;">
                                <%=createTime%>
                            </div>
                        </div>
                       

                        
                    </div>
                    
                </div>

                <%if(cover!=""){%>
                <div style="text-align: center;">
                    <img style="width: 124px;height: 100px;margin-left: 15px"
                         src="<%=cover%>"/>
                </div>
                <%}%>
            </div>

        </li>
    </script>
</head>
<body class="desktop">
<div id="section_container">
    <section id="hot_post_section" class="active">
        <!--Header-->
        <div class="header" data-fixed="top">
            <div class="title row-box" style="border: none;">
                <div class="box-left" style="background:  #d93a49">
                    <div data-role="BTButton" data-type="image" onclick="app.back()">
                        <img src="../../css/images/icons/navicon/icon-back.png" alt=""/>
                    </div>
                </div>
                <div class="span1" style="background:  #d93a49">
                    <h1>精华帖</h1>
                </div>
                <div class="box-right" style="background: #d93a49">
                    <div data-role="BTButton" data-type="image" onclick="app.refresh();">
                        <img src="../../css/images/icons/navicon/icon-refresh.png" alt=""/>
                    </div>
                </div>
            </div>

            <!--<div class="container-fluid"style="padding-top:7px;margin: 0;padding-bottom: 7px;;background: #fff;" id="soSuoLan">
                <div class="row-box" style="margin: 0;padding: 0">
                    <div class="span1">
                        <div data-role="BTSearchbar" class="row-box" data-corner="all">
                            <div class="span1">
                                <input type="text" placeholder="请输入姓名" value="" id="edt_keyword" />
                            </div>
                            <div class="btn-del">
                                &lt;!&ndash;<img src="../../frame3/css/images/icon-del.png" alt="" id="btn_del" />&ndash;&gt;
                            </div>
                        </div>
                    </div>
                    <div class="box-right" style="background: #b63635;color: #fff;height: 52px;line-height: 52px;padding: 0 20px;border-radius: 4px;margin: 0 0 0 10px;"id="btn_search2">
                        搜索
                    </div>
                </div>
            </div>-->
        </div>
        <!--Content-->
        <div class="content" style="margin:10px 0 0 0;width: 100%;padding:0;overflow: hidden">
            <div class="container-fluid" style="margin: 0; padding: 0 ">
                <div class="pullrefresh-wrapper" style="margin: 0; padding: 0 ">
                    <div>
                        <ul id="nutritionBlogList" style="width: 100%;margin: 0;padding: 0">

                        </ul>
                    </div>
                </div>
            </div>

        </div>
    </section>
</div>
</body>
</html>