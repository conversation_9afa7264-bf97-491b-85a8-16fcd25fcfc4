/*!Extend zepto.extend.js*/
(function(a){a.extend(a,{contains:function(b,c){return b.compareDocumentPosition?!!(b.compareDocumentPosition(c)&16):b!==c&&b.contains(c)}})})(Zepto);(function(a,c){a.extend(a,{toString:function(d){return Object.prototype.toString.call(d)},slice:function(e,d){return Array.prototype.slice.call(e,d||0)},later:function(f,d,h,e,g){return window["set"+(h?"Interval":"Timeout")](function(){f.apply(e,g)},d||0)},parseTpl:function(g,f){var d="var __p=[],print=function(){__p.push.apply(__p,arguments);};with(obj||{}){__p.push('"+g.replace(/\\/g,"\\\\").replace(/'/g,"\\'").replace(/<%=([\s\S]+?)%>/g,function(h,i){return"',"+i.replace(/\\'/g,"'")+",'"}).replace(/<%([\s\S]+?)%>/g,function(h,i){return"');"+i.replace(/\\'/g,"'").replace(/[\r\n\t]/g," ")+"__p.push('"}).replace(/\r/g,"\\r").replace(/\n/g,"\\n").replace(/\t/g,"\\t")+"');}return __p.join('');";var e=new Function("obj",d);return f?e(f):e},throttle:function(d,e,i){var g=0,f;if(typeof e!=="function"){i=e;e=d;d=250}function h(){var m=this,n=Date.now()-g,l=arguments;function k(){g=Date.now();e.apply(m,l)}function j(){f=c}if(i&&!f){k()}f&&clearTimeout(f);if(i===c&&n>d){k()}else{f=setTimeout(i?j:k,i===c?d-n:d)}}h._zid=e._zid=e._zid||a.proxy(e)._zid;return h},debounce:function(d,f,e){return f===c?a.throttle(250,d,false):a.throttle(d,f,e===c?false:e!==false)}});a.each("String Boolean RegExp Number Date Object Null Undefined".split(" "),function(e,d){var f;if("is"+d in a){return}switch(d){case"Null":f=function(g){return g===null};break;case"Undefined":f=function(g){return g===c};break;default:f=function(g){return new RegExp(d+"]","i").test(b(g))}}a["is"+d]=f});var b=a.toString})(Zepto);(function(d,g){var c=navigator.userAgent,a=navigator.appVersion,b=d.browser;d.extend(b,{qq:/qq/i.test(c),uc:/UC/i.test(c)||/UC/i.test(a)});b.uc=b.uc||!b.qq&&!b.chrome&&!b.firefox&&!/safari/i.test(c);try{b.version=b.uc?a.match(/UC(?:Browser)?\/([\d.]+)/)[1]:b.qq?c.match(/MQQBrowser\/([\d.]+)/)[1]:b.version}catch(f){}d.support=d.extend(d.support||{},{orientation:!(b.uc||(parseFloat(d.os.version)<5&&(b.qq||b.chrome)))&&!(d.os.android&&parseFloat(d.os.version)>3)&&"orientation" in window&&"onorientationchange" in window,touch:"ontouchend" in document,cssTransitions:"WebKitTransitionEvent" in window,has3d:"WebKitCSSMatrix" in window&&"m11" in new WebKitCSSMatrix()})})(Zepto);(function(b){b.matchMedia=(function(){var g=0,e="gmu-media-detect",d=b.fx.transitionEnd,h=b.fx.cssPrefix,f=b("<style></style>").append("."+e+"{"+h+"transition: width 0.001ms; width: 0; position: relative; bottom: -999999px;}\n").appendTo("head");return function(k){var m=e+g++,l=b('<div class="'+e+'" id="'+m+'"></div>').appendTo("body"),j=[],i;f.append("@media "+k+" { #"+m+" { width: 100px; } }\n");l.on(d,function(){i.matches=l.width()===100;b.each(j,function(n,o){b.isFunction(o)&&o.call(i,i)})});i={matches:l.width()===100,media:k,addListener:function(n){j.push(n);return this},removeListener:function(o){var n=j.indexOf(o);~n&&j.splice(n,1);return this}};return i}}());b(function(){var d=function(f){if(e!==f.matches){b(window).trigger("ortchange");e=f.matches}},e=true;b.mediaQuery={ortchange:"screen and (width: "+window.innerWidth+"px)"};b.matchMedia(b.mediaQuery.ortchange).addListener(d)});function a(){b(window).on("scroll",b.debounce(80,function(){b(document).trigger("scrollStop")},false))}function c(){b(window).off("scroll");a()}a();b(window).on("pageshow",function(d){if(d.persisted){b(document).off("touchstart",c).one("touchstart",c)}})})(Zepto);
/*!Extend zepto.highlight.js*/
(function(e){var d,a=false,f,c,b=function(){clearTimeout(f);if(d&&(c=d.attr("highlight-cls"))){d.removeClass(c).attr("highlight-cls","");d=null}};e.extend(e.fn,{highlight:function(g){a=a||!!e(document).on("touchend.highlight touchmove.highlight touchcancel.highlight",b);b();return this.each(function(){var h=e(this);h.css("-webkit-tap-highlight-color","rgba(255,255,255,0)").off("touchstart.highlight");g&&h.on("touchstart.highlight",function(){f=e.later(function(){d=h.attr("highlight-cls",g).addClass(g)},100)})})}})})(Zepto);
/*!Extend zepto.ui.js*/
(function(f,c){var b=1,e=function(){},j="<%=name%>-<%=id%>",h=(function(){var n={},o=0,m="GMUWidget"+(+new Date());return function(s,r,t){var p=s[m]||(s[m]=++o),q=n[p]||(n[p]={});!f.isUndefined(t)&&(q[r]=t);f.isNull(t)&&delete q[r];return q[r]}})();f.ui=f.ui||{version:"2.0.5",guid:g,define:function(n,p,o){if(o){p.inherit=o}var m=f.ui[n]=d(function(r,q){var s=k(m.prototype,{_id:f.parseTpl(j,{name:n,id:g()})});s._createWidget.call(s,r,q,m.plugins);return s},p);return i(n,m)},isWidget:function(n,m){return n instanceof (m===c?l:f.ui[m]||e)}};function g(){return b++}function k(m,n){var o={};Object.create?o=Object.create(m):o.__proto__=m;return f.extend(o,n||{})}function d(m,n){if(n){a(m,n);f.extend(m.prototype,n)}return f.extend(m,{plugins:[],register:function(o){if(f.isObject(o)){f.extend(this.prototype,o);return}this.plugins.push(o)}})}function a(m,p){var n=p.inherit||l,o=n.prototype,q;q=m.prototype=k(o,{$factory:m,$super:function(r){var s=o[r];return f.isFunction(s)?s.apply(this,f.slice(arguments,1)):s}});q._data=f.extend({},o._data,p._data);delete p._data;return m}function i(m){f.fn[m]=function(p){var o,q,n=f.slice(arguments,1);f.each(this,function(r,s){q=h(s,m)||f.ui[m](s,f.extend(f.isPlainObject(p)?p:{},{setup:true}));if(f.isString(p)){if(!f.isFunction(q[p])&&p!=="this"){throw new Error(m+"\u7ec4\u4ef6\u6ca1\u6709\u6b64\u65b9\u6cd5")}o=f.isFunction(q[p])?q[p].apply(q,n):c}if(o!==c&&o!==q||p==="this"&&(o=q)){return false}o=c});return o!==c?o:this}}var l=function(){};f.extend(l.prototype,{_data:{status:true},data:function(m,o){var n=this._data;if(f.isObject(m)){return f.extend(n,m)}else{return !f.isUndefined(o)?n[m]=o:n[m]}},_createWidget:function(o,q,m){if(f.isObject(o)){q=o||{};o=c}var r=f.extend({},this._data,q);f.extend(this,{_el:o?f(o):c,_data:r});var p=this;f.each(m,function(u,v){var s=v.apply(p);if(s&&f.isPlainObject(s)){var t=p._data.disablePlugin;if(!t||f.isString(t)&&!~t.indexOf(s.pluginName)){delete s.pluginName;f.each(s,function(w,y){var x;if((x=p[w])&&f.isFunction(y)){p[w]=function(){p[w+"Org"]=x;return y.apply(p,arguments)}}else{p[w]=y}})}}});if(r.setup){this._setup(o&&o.getAttribute("data-mode"))}else{this._create()}this._init();var p=this,n=this.trigger("init").root();n.on("tap",function(s){(s.bubblesList||(s.bubblesList=[])).push(p)});h(n[0],p._id.split("-")[0],p)},_create:function(){},_setup:function(m){},root:function(m){return this._el=m||this._el},id:function(m){return this._id=m||this._id},destroy:function(){var n=this,m;m=this.trigger("destroy").off().root();m.find("*").off();h(m[0],n._id.split("-")[0],null);m.off().remove();this.__proto__=null;f.each(this,function(o){delete n[o]})},on:function(m,n){this.root().on(m,f.proxy(n,this));return this},off:function(m,n){this.root().off(m,n);return this},trigger:function(n,o){n=f.isString(n)?f.Event(n):n;var p=this.data(n.type),m;if(p&&f.isFunction(p)){n.data=o;m=p.apply(this,[n].concat(o));if(m===false||n.defaultPrevented){return this}}this.root().trigger(n,o);return this}})})(Zepto);

/*!Widget slider.js*/
(function(a,b){a.ui.define("slider",{_data:{viewNum:1,imgInit:2,itemRender:null,imgZoom:false,loop:false,stopPropagation:false,springBackDis:15,autoPlay:true,autoPlayTime:4000,animationTime:400,showArr:true,showDot:true,slide:null,slideend:null,index:0,_stepLength:1,_direction:1},_create:function(){var g=this,e=0,d,c=[],f=g.data("content");g._initConfig();(g.root()||g.root(a("<div></div>"))).addClass("ui-slider").appendTo(g.data("container")||(g.root().parent().length?"":document.body)).html('<div class="ui-slider-wheel"><div class="ui-slider-group">'+(function(){if(g.data("itemRender")){var h=g.data("itemRender");while(d=h.call(g,e++)){c.push('<div class="ui-slider-item">'+d+"</div>")}}else{while(d=f[e++]){c.push('<div class="ui-slider-item"><a href="'+d.href+'"><img lazyload="'+d.pic+'"/></a>'+(d.title?"<p>"+d.title+"</p>":"")+"</div>")}}c.push(g.data("loop")?'</div><div class="ui-slider-group">'+c.join("")+"</div></div>":"</div></div>");return c.join("")}()));g._addDots()},_setup:function(g){var e=this,c=e.root().addClass("ui-slider");e._initConfig();if(!g){var d=c.children(),f=a('<div class="ui-slider-group"></div>').append(d.addClass("ui-slider-item"));c.empty().append(a('<div class="ui-slider-wheel"></div>').append(f).append(e.data("loop")?f.clone():""));e._addDots()}else{e.data("loop")&&a(".ui-slider-wheel",c).append(a(".ui-slider-group",c).clone())}},_init:function(){var f=this,d=f.data("index"),c=f.root(),e=a.proxy(f._eventHandler,f);f._setWidth();a(f.data("wheel")).on("touchstart touchmove touchend touchcancel webkitTransitionEnd",e);a(window).on("ortchange",e);a(".ui-slider-pre",c).on("tap",function(){f.pre()});a(".ui-slider-next",c).on("tap",function(){f.next()});f.on("destroy",function(){clearTimeout(f.data("play"));a(window).off("ortchange",e)});f.data("autoPlay")&&f._setTimeout()},_initConfig:function(){var c=this._data;if(c.viewNum>1){c.loop=false;c.showDot=false;c.imgInit=c.viewNum+1}},_addDots:function(){var f=this,c=f.root(),e=a(".ui-slider-item",c).length/(f.data("loop")?2:1),d=[];if(f.data("showDot")){d.push('<p class="ui-slider-dots">');while(e--){d.push("<b></b>")}d.push("</p>")}f.data("showArr")&&(d.push('<span class="ui-slider-pre"><b></b></span><span class="ui-slider-next"><b></b></span>'));c.append(d.join(""))},_setWidth:function(){var s=this,f=s._data,t=s.root(),c=Math.ceil(t.width()/f.viewNum),u=t.height(),n=f.loop,q=a(".ui-slider-item",t).toArray(),g=q.length,p=a(".ui-slider-wheel",t).width(c*g)[0],r=a(".ui-slider-dots b",t).toArray(),e=a("img",t).toArray(),v=e.concat(),d={},m,k,h=f.imgInit||g;f.showDot&&(r[0].className="ui-slider-dot-select");if(f.imgZoom){a(v).on("load",function(){var l=this.height,i=this.width,o=Math.min(l,u),j=Math.min(i,c);if(l/u>i/c){this.style.cssText+="height:"+o+"px;width:"+o/l*i+"px;"}else{this.style.cssText+="height:"+j/i*l+"px;width:"+j+"px"}this.onload=null})}for(m=0;m<g;m++){q[m].style.cssText+="width:"+c+"px;position:absolute;-webkit-transform:translate3d("+m*c+"px,0,0);z-index:"+(900-m);d[m]=n?(m>g/2-1?m-g/2:m):m;if(m<h){k=v.shift();k&&(k.src=k.getAttribute("lazyload"));if(f.loop){k=e[m+g/2];k&&(k.src=k.getAttribute("lazyload"))}}}s.data({root:t[0],wheel:p,items:q,lazyImgs:v,allImgs:e,length:g,width:c,height:u,dots:r,dotIndex:d,dot:r[0]});return s},_eventHandler:function(d){var c=this;switch(d.type){case"touchmove":c._touchMove(d);break;case"touchstart":c._touchStart(d);break;case"touchcancel":case"touchend":c._touchEnd();break;case"webkitTransitionEnd":c._transitionEnd();break;case"ortchange":c._resize.call(c);break}},_touchStart:function(d){var c=this;c.data({pageX:d.touches[0].pageX,pageY:d.touches[0].pageY,S:false,T:false,X:0});c.data("wheel").style.webkitTransitionDuration="0ms"},_touchMove:function(g){var h=this._data,i=h.X=g.touches[0].pageX-h.pageX;if(!h.T){var c=h.index,f=h.length,d=Math.abs(i)<Math.abs(g.touches[0].pageY-h.pageY);h.loop&&(h.index=c>0&&(c<f-1)?c:(c===f-1)&&i<0?f/2-1:c===0&&i>0?f/2:c);d||clearTimeout(h.play);h.T=true;h.S=d}if(!h.S){h.stopPropagation&&g.stopPropagation();g.preventDefault();h.wheel.style.webkitTransform="translate3d("+(i-h.index*h.width)+"px,0,0)"}},_touchEnd:function(){var d=this,e=d._data;if(!e.S){var f=e.springBackDis,c=e.X<=-f?Math.ceil(-e.X/e.width):(e.X>f)?-Math.ceil(e.X/e.width):0;e._stepLength=Math.abs(c);d._slide(e.index+c)}},_slide:function(d,h){var f=this,g=f._data,e=g.length,c=e-g.viewNum+1;if(-1<d&&d<c){f._move(d)}else{if(d>=c){if(!g.loop){f._move(c-(h?2:1));g._direction=-1}else{g.wheel.style.cssText+="-webkit-transition:0ms;-webkit-transform:translate3d(-"+(e/2-1)*g.width+"px,0,0);";g._direction=1;a.later(function(){f._move(e/2)},20)}}else{if(!g.loop){f._move(h?1:0)}else{g.wheel.style.cssText+="-webkit-transition:0ms;-webkit-transform:translate3d(-"+(e/2)*g.width+"px,0,0);";a.later(function(){f._move(e/2-1)},20)}g._direction=1}}return f},_move:function(d){var f=this._data,e=f.dotIndex[d];this.trigger("slide",e);if(f.lazyImgs.length){var c=f.allImgs[d];c&&c.src||(c.src=c.getAttribute("lazyload"))}if(f.showDot){f.dot.className="";f.dots[e].className="ui-slider-dot-select";f.dot=f.dots[e]}f.index=d;f.wheel.style.cssText+="-webkit-transition:"+f.animationTime+"ms;-webkit-transform:translate3d(-"+d*f.width+"px,0,0);"},_transitionEnd:function(){var f=this,g=f._data;f.trigger("slideend",g.dotIndex[g.index]);if(g.lazyImgs.length){for(var e=g._stepLength,d=0;d<e;d++){var c=g.lazyImgs.shift();c&&(c.src=c.getAttribute("lazyload"));if(g.loop){c=g.allImgs[g.index+g.length/2];c&&!c.src&&(c.src=c.getAttribute("lazyload"))}}g._stepLength=1}f._setTimeout()},_setTimeout:function(){var c=this,d=c._data;if(!d.autoPlay){return c}clearTimeout(d.play);d.play=a.later(function(){c._slide.call(c,d.index+d._direction,true)},d.autoPlayTime);return c},_resize:function(){var g=this,h=g._data,e=h.root.offsetWidth/h.viewNum,f=h.length,c=h.items;if(!e){return g}h.width=e;clearTimeout(h.play);for(var d=0;d<f;d++){c[d].style.cssText+="width:"+e+"px;-webkit-transform:translate3d("+d*e+"px,0,0);"}h.wheel.style.removeProperty("-webkit-transition");h.wheel.style.cssText+="width:"+e*f+"px;-webkit-transform:translate3d(-"+h.index*e+"px,0,0);";h._direction=1;g._setTimeout();return g},pre:function(){var c=this;c._slide(c.data("index")-1);return c},next:function(){var c=this;c._slide(c.data("index")+1);return c},stop:function(){var c=this;clearTimeout(c.data("play"));c.data("autoPlay",false);return c},resume:function(){var c=this;c.data("_direction",1);c.data("autoPlay",true);c._setTimeout();return c}})})(Zepto);
/*!Widget suggestion.js*/
(function(a,b){a.ui.define("suggestion",{_data:{listCount:50,isCache:true,isStorage:true,minChars:0,maxChars:1000,useIscroll:true,offset:{x:0,y:0,w:0},confirmClearHistory:true},_create:function(){var e=this,i=+new Date(),h="ui-input-mask-"+i,d=e.data("id","ui-suggestion-"+a.ui.guid()),g=e.root(a(e.data("container"))).attr("autocomplete","off"),f=e.data("formID"),c=g.parent();e.data({inputWidth:g.get(0).offsetWidth,cacheData:{},form:f?a(f):g.closest("form")});if(c.attr("class")!="ui-input-mask"){c=a('<div id="'+h+'" class="ui-input-mask"></div>').appendTo(g.parent()).append(g)}e.data("maskElem",c);e.data("wrapper",a('<div id="'+d+'" class="ui-suggestion"><div class="ui-suggestion-content"><div class="ui-suggestion-scroller"></div></div><div class="ui-suggestion-button"></div></div>').appendTo(c));e._initSuggestionOffset()},_init:function(){var e=this,f=e.root(),d=e.data("form"),c=a.proxy(e._eventHandler,e);e.data("wrapper").on("touchstart",c);d.length&&d.on("submit",c);f.on("focus input",c).parent().on("touchstart",c);a(window).on("ortchange",c);e.data("autoClose")&&a(document).on("tap",c);e.on("destroy",function(){var h=this,g=h.data("maskElem");a(document).off("tap",c);a(window).off("ortchange",c);clearTimeout(h.data("timeId"));clearTimeout(h.data("hideTimer"));g.find("*").off();h.data("iScroll")&&h.data("iScroll").destroy();g.off().remove()})._setSize()},_setup:function(){var c=this;c.data("container",c.root());c._create()},_initSuggestionOffset:function(){var f=this,e,c=f.data("wrapper"),h=f.root(),g=f.data("offset"),d=2*parseInt(c.css("border-left-width")||0);f.data("pos",h.height()+(g.y||0));f.data("realWidth",(f.data("width")||h.width())-d);c.css({position:"absolute",left:g.x||0});return f},_setSize:function(){var d=this,c=d.data("realWidth"),e=d.root().parent().width()-d.data("inputWidth");d.data("wrapper").css("width",c+e);return d},_posAdapt:function(c){var d=this;c?d._setPos().data("timeId",a.later(function(){d._setPos()},200,true)):clearInterval(d.data("timeId"));return d},_setPos:function(){var g=this,f=window,c=g.data("wrapper"),h=g.root(),k=parseFloat(c.height()),e=g.data("offset"),i=parseFloat(g.data("pos")),j=h.offset().top-f.pageYOffset,d=a(f).height()-j;if(g.data("posAdapt")&&j>d){c.css("top",-k-(e.y||0)+"px")}else{c.css("top",i)}return g},_change:function(g){var d=this,f=d._cacheData(g),c=d.data("isCache"),e=d.data("source");return f&&c?d._render(g,f):d._sendRequest(g)},_eventHandler:function(h){var d=this,c=h.type,g=h.target,f=d.data("maskElem").get(0);if(!d.data("status")){return}switch(c){case"focus":d._setSize()._showList()._setPos().trigger("open");break;case"touchstart":case"mousedown":if(!h.formDelete){break}h.preventDefault();case"input":d._showList();break;case"ortchange":d._setSize()._setPos();break;case"submit":d.data("isStorage")&&d._localStorage(d.getValue());case"click":case"tap":if(!(f.compareDocumentPosition(g)&16)){d.hide()}break}},_showList:function(){var c=this,e=c.getValue(),d=c._localStorage();if(e!==""&&(e.length<parseFloat(c.data("minChars"))||e.length>parseFloat(c.data("maxChars")))){return c}return e?c._change(e):d?c._render(null,{s:d.split(encodeURIComponent(","))}):c.hide()},_bindSuggestionListEvent:function(){var c=this,d=c.root();c.data("wrapper").find(".ui-suggestion-result").on("tap",function(h){var g=h.target,f=this;setTimeout(function(){if(g&&g.className=="ui-suggestion-plus"){d.val(g.getAttribute("data-item")).trigger("input")}else{c._select(f)._submit()}},400)}).highlight("ui-suggestion-result-highlight");return c},_bindCloseEvent:function(){var c=this,d=c.data("wrapper");d.find("span:first-child").on("click",function(){a.later(function(){c.clearHistory()},a.os.android?200:0)});d.find("span:last-child").on("click",function(){c.hide().leaveInput().trigger("close")});return c},_sendRequest:function(g){var f=this,d=f.data("source"),h=f.data("param"),c="suggestion_"+(+new Date()),e=f.data("sendRequest");if(a.isFunction(e)){e(g,function(i){f._render(g,i)._cacheData(g,i)})}else{if(g){d+=(d.indexOf("?")===-1?"?":"")+"&wd="+encodeURIComponent(g);d.indexOf("&cb=")===-1&&(d+="&cb="+c);h&&(d+="&"+h);window[c]=function(i){f._render(g,i)._cacheData(g,i);a('[src="'+d+'"]').remove();delete window[c]};a.ajax({url:d,dataType:"jsonp"})}}return f},getValue:function(){return a.trim(this.root().val())},_render:function(k,h){var l=this,i,f=l.data("wrapper"),e=f.find(".ui-suggestion-content"),c=f.find(".ui-suggestion-button"),d=l.data("renderList"),m=l.data("renderEvent"),g='<span style="display:none;"></span><span>\u5173\u95ed</span>';k===null&&(g="<span>\u6e05\u9664\u5386\u53f2\u8bb0\u5f55</span><span>\u5173\u95ed</span>");i=d?d.apply(l,[k,h]):l._renderList(k,h);if(i){e.find("*").off();e.find(".ui-suggestion-scroller").html(i);c.find("*").off();c.html(g);m?m.apply(l):l._bindSuggestionListEvent();l._bindCloseEvent()._show();if(l.data("useIscroll")){h.s.length>=2?e.css("height",l.data("height")||66):e.css("height",38);var j=(l.data("iScroll")||l.data("iScroll",new iScroll(e.get(0),{topOffset:0,hScroll:false,vScrollbar:false,hScrollbar:false})));j.scrollTo(0,0);j.refresh()}else{e.on("touchstart",function(n){n.preventDefault()})}}else{l.hide()}return l},_renderList:function(i,h){var g=this,c=g.data("listCount"),f=g.data("usePlus"),e="<ul>",d=h.s;if(!h||!d||!d.length){this.hide();return}d=d.slice(0,c);i=this._htmlEncode(i)||null;a.each(d,function(j,k){k=g._htmlEncode(k);var l=a.trim(k).replace(i,"<span>"+i+"</span>");if(f){l+='<div class="ui-suggestion-plus" data-item="'+k+'"></div>'}e+='<li><div class="ui-suggestion-result">'+l+"</div></li>"});return e+"</ul>"},_htmlEncode:function(c){return a("<div></div>").text(c).html()},_submit:function(){var c=this,d=c.root().val();c.trigger("submit");if(!c.data("submit")&&!(c._callbacks&&c._callbacks.submit)){window.location="http://www.baidu.com/s?wd="+encodeURIComponent(d)}return c},_select:function(e){var d=this,c=e.textContent;d.root().val(c);d.data("isStorage")&&d._localStorage(c);return d.trigger("select",e).hide()},_cacheData:function(c,e){var d=this;if(d.data("isCache")){return e!==b?d.data("cacheData")[c]=e:d.data("cacheData")[c]}},_localStorage:function(h){var f=this,d,j,g,c=f.data("shareName"),k=f.data("isSharing")?c?c+"-SUG-Sharing-History":"SUG-Sharing-History":f.data("id");try{if(h===null){window.localStorage[k]=""}else{if(h!==b){j=window.localStorage[k];g=j?j.split(encodeURIComponent(",")):[];if(!~a.inArray(h,g)){g.unshift(h);window.localStorage[k]=g.join(encodeURIComponent(","))}}}d=window.localStorage[k]}catch(i){}return d},_show:function(){var c=this;if(c.data("hideTimer")){clearTimeout(c.data("hideTimer"));c.data("hideTimer",null)}c.data("wrapper").css("display","block");c.data("posAdapt")&&c._posAdapt(1);return c.trigger("show")},hide:function(){var c=this;c.data("hideTimer",a.later(function(){c.data("wrapper").css("display","none");c.data("hideTimer",null)},200));return c._posAdapt(0).trigger("hide")},clearHistory:function(){var d=this,c=function(){d._localStorage(null);d.hide()};d.data("confirmClearHistory")?window.confirm("\u6e05\u9664\u5168\u90e8\u67e5\u8be2\u5386\u53f2\u8bb0\u5f55\uff1f")&&c():c()},history:function(c){return this._localStorage(c)},focusInput:function(){this.root().get(0).focus();return this},leaveInput:function(){this.root().get(0).blur();return this}})})(Zepto);
/*!Widget dialog.js*/
(function(b,c){var a={close:'<a class="ui-dialog-close" title="\u5173\u95ed"><span class="ui-icon ui-icon-delete"></span></a>',mask:'<div class="ui-mask"></div>',title:'<div class="ui-dialog-title"><h3><%=title%></h3></div>',wrap:'<div class="ui-dialog"><div class="ui-dialog-content"></div><% if(btns){ %><div class="ui-dialog-btns"><% for(var i=0, length=btns.length; i<length; i++){var item = btns[i]; %><a class="ui-btn ui-btn-<%=item.index%>" data-key="<%=item.key%>"><%=item.text%></a><% } %></div><% } %></div> '};b.ui.define("dialog",{_data:{autoOpen:true,buttons:null,closeBtn:true,mask:true,width:300,height:"auto",title:null,content:null,scrollMove:true,container:null,maskClick:null,position:null},getWrap:function(){return this._data._wrap},_setup:function(){var d=this._data;d.content=d.content||this._el.show();d.title=d.title||this._el.attr("title")},_init:function(){var g=this,h=g._data,f,e=0,d=b.proxy(g._eventHandler,g),j={};h._container=b(h.container||document.body);(h._cIsBody=h._container.is("body"))||h._container.addClass("ui-dialog-container");j.btns=f=[];h.buttons&&b.each(h.buttons,function(i){f.push({index:++e,text:i,key:i})});h._mask=h.mask?b(a.mask).appendTo(h._container):null;h._wrap=b(b.parseTpl(a.wrap,j)).appendTo(h._container);h._content=b(".ui-dialog-content",h._wrap);h._title=b(a.title);h._close=h.closeBtn&&b(a.close).highlight("ui-dialog-close-hover");g._el=g._el||h._content;g.title(h.title);g.content(h.content);f.length&&b(".ui-dialog-btns .ui-btn",h._wrap).highlight("ui-state-hover");h._wrap.css({width:h.width,height:h.height});b(window).on("ortchange",d);h._wrap.on("click",d);h._mask&&h._mask.on("click",d);h.autoOpen&&g.root().one("init",function(){g.open()})},_eventHandler:function(j){var h=this,d,g,i=h._data,f;switch(j.type){case"ortchange":this.refresh();break;case"touchmove":i.scrollMove&&j.preventDefault();break;case"click":if(i._mask&&(b.contains(i._mask[0],j.target)||i._mask[0]===j.target)){return h.trigger("maskClick")}g=i._wrap.get(0);if((d=b(j.target).closest(".ui-dialog-close",g))&&d.length){h.close()}else{if((d=b(j.target).closest(".ui-dialog-btns .ui-btn",g))&&d.length){f=i.buttons[d.attr("data-key")];f&&f.apply(h,arguments)}}}},_calculate:function(){var i=this,j=i._data,h,k,e=document.body,g={},d=j._cIsBody,f=Math.round;j.mask&&(g.mask=d?{width:"100%",height:Math.max(e.scrollHeight,e.clientHeight)-1}:{width:"100%",height:"100%"});h=j._wrap.offset();k=b(window);g.wrap={left:"50%",marginLeft:-f(h.width/2)+"px",top:d?f(k.height()/2)+window.pageYOffset:"50%",marginTop:-f(h.height/2)+"px"};return g},refresh:function(){var e=this,g=e._data,d,f;if(g._isOpen){f=function(){d=e._calculate();d.mask&&g._mask.css(d.mask);g._wrap.css(d.wrap)};if(b.os.ios&&document.activeElement&&/input|textarea|select/i.test(document.activeElement.tagName)){document.body.scrollLeft=0;b.later(f,200)}else{f()}}return e},open:function(d,f){var e=this._data;e._isOpen=true;e._wrap.css("display","block");e._mask&&e._mask.css("display","block");d!==c&&this.position?this.position(d,f):this.refresh();b(document).on("touchmove",b.proxy(this._eventHandler,this));return this.trigger("open")},close:function(){var d,e=this._data;d=b.Event("beforeClose");this.trigger(d);if(d.defaultPrevented){return this}e._isOpen=false;e._wrap.css("display","none");e._mask&&e._mask.css("display","none");b(document).off("touchmove",this._eventHandler);return this.trigger("close")},title:function(e){var d=this._data,f=e!==c;if(f){e=(d.title=e)?"<h3>"+e+"</h3>":e;d._title.html(e)[e?"prependTo":"remove"](d._wrap);d._close&&d._close.prependTo(d.title?d._title:d._wrap)}return f?this:d.title},content:function(e){var d=this._data,f=e!==c;f&&d._content.empty().append(d.content=e);return f?this:d.content},destroy:function(){var e=this._data,d=this._eventHandler;b(window).off("ortchange",d);b(document).off("touchmove",d);e._wrap.off("click",d).remove();e._mask&&e._mask.off("click",d).remove();e._close&&e._close.highlight();return this.$super("destroy")}})})(Zepto);
/*!Widget panel.js*/
(function(b,c){var d=b.fx.cssPrefix,a=b.fx.transitionEnd;b.ui.define("panel",{_data:{contentWrap:"",scrollMode:"follow",display:"push",position:"right",dismissible:true,swipeClose:true,beforeopen:null,open:null,beforeclose:null,close:null},_create:function(){throw new Error("panel\u7ec4\u4ef6\u4e0d\u652f\u6301create\u6a21\u5f0f\uff0c\u8bf7\u4f7f\u7528setup\u6a21\u5f0f")},_setup:function(){var f=this,g=f._data,e=f.root().addClass("ui-panel ui-panel-"+g.position);f.panelWidth=e.width()||0;f.$contentWrap=b(g.contentWrap||e.next());g.dismissible&&(f.$panelMask=b('<div class="ui-panel-dismiss"></div>').width(document.body.clientWidth-e.width()).appendTo("body")||null)},_init:function(){var e=this,f=e._data;e.displayFn=e._setDisplay();e.$contentWrap.addClass("ui-panel-animate");e.root().on(a,b.proxy(e._eventHandler,e)).hide();f.dismissible&&e.$panelMask.hide().on("click",b.proxy(e._eventHandler,e));f.scrollMode!=="follow"&&b(document).on("scrollStop",b.proxy(e._eventHandler,e));b(window).on("ortchange",b.proxy(e._eventHandler,e))},_setDisplay:function(){var i=this,k=i.root(),f=i.$contentWrap,e=d+"transform",l=i._transDisplayToPos(),j={},h,g;b.each(["push","overlay","reveal"],function(m,n){j[n]=function(p,q,o){h=l[n].panel,g=l[n].cont;k.css(e,"translate3d("+i._transDirectionToPos(q,h[p])+"px,0,0)");if(!o){f.css(e,"translate3d("+i._transDirectionToPos(q,g[p])+"px,0,0)");i.maskTimer=b.later(function(){i.$panelMask&&i.$panelMask.css(q,k.width()).toggle(p)},400)}return i}});return j},_initPanelPos:function(e,f){this.displayFn[e](0,f,true);this.root().get(0).clientLeft;return this},_transDirectionToPos:function(f,e){return f==="left"?e:-e},_transDisplayToPos:function(){var e=this,f=e.panelWidth;return{push:{panel:[-f,0],cont:[0,f]},overlay:{panel:[-f,0],cont:[0,0]},reveal:{panel:[0,0],cont:[0,f]}}},_setShow:function(f,g,n){var m=this,k=m._data,l=f?"open":"close",o=b.Event("before"+l),j=f!==m.state(),e=f?"on":"off",h=f?b.proxy(m._eventHandler,m):m._eventHandler,i=g||k.display,p=n||k.position;m.trigger(o,[g,n]);if(o.defaultPrevented){return m}if(j){m._dealState(f,i,p);m.displayFn[i](m.isOpen=Number(f),p);k.swipeClose&&m.root()[e](b.camelCase("swipe-"+p),h);k.display=i,k.position=p}return m},_dealState:function(e,g,k){var i=this,h=i._data,l=i.root(),f=i.$contentWrap,m="ui-panel-"+g+" ui-panel-"+k,j="ui-panel-"+h.display+" ui-panel-"+h.position+" ui-panel-animate";if(e){l.removeClass(j).addClass(m).show();h.scrollMode==="fix"&&l.css("top",b(window).scrollTop());i._initPanelPos(g,k);if(g==="reveal"){f.addClass("ui-panel-contentWrap").on(a,b.proxy(i._eventHandler,i))}else{f.removeClass("ui-panel-contentWrap").off(a,b.proxy(i._eventHandler,i));l.addClass("ui-panel-animate")}i.$panelMask&&i.$panelMask.css({left:"auto",right:"auto",height:document.body.clientHeight})}return i},_eventHandler:function(i){var g=this,h=g._data,j=h.scrollMode,f=g.state()?"open":"close";switch(i.type){case"click":case"swipeLeft":case"swipeRight":g.close();break;case"scrollStop":j==="fix"?g.root().css("top",b(window).scrollTop()):g.close();break;case a:g.trigger(f,[h.display,h.position]);break;case"ortchange":g.$panelMask&&g.$panelMask.css("height",document.body.clientHeight);j==="fix"&&g.root().css("top",b(window).scrollTop());break}},open:function(f,e){return this._setShow(true,f,e)},close:function(){return this._setShow(false)},toggle:function(f,e){return this[this.isOpen?"close":"open"](f,e)},state:function(){return !!this.isOpen},destroy:function(){this.$panelMask&&this.$panelMask.off().remove();this.maskTimer&&clearTimeout(this.maskTimer);this.$contentWrap.removeClass("ui-panel-animate");b(document).off("scrollStop",this._eventHandler);b(window).off("ortchange",this._eventHandler);return this.$super("destroy")}})})(Zepto);
/*!Widget tabs.js*/
(function(e,f){var c=1,b=function(){return c++},a={nav:'<ul class="ui-tabs-nav"><% var item; for(var i=0, length=items.length; i<length; i++) { item=items[i]; %><li<% if(i==active){ %> class="ui-state-active"<% } %>><a href="javascript:;"><%=item.title%></a></li><% } %></ul>',content:'<div class="ui-viewport ui-tabs-content"><% var item; for(var i=0, length=items.length; i<length; i++) { item=items[i]; %><div<% if(item.id){ %> id="<%=item.id%>"<% } %> class="ui-panel ui-tabs-panel <%=transition%><% if(i==active){ %> ui-state-active<% } %>"><%=item.content%></div><% } %></div>'},d=/^#(.+)$/;e.ui.define("tabs",{_data:{active:0,items:null,transition:"slide",activate:null,beforeActivate:null,animateComplete:null},_prepareDom:function(k,j){var n=this,l,o=n._el,m,g,i,h;switch(k){case"fullsetup":case"setup":j._nav=n._findElement("ul").first();if(j._nav){j._content=n._findElement("div.ui-tabs-content");j._content=((j._content&&j._content.first())||e("<div></div>").appendTo(o)).addClass("ui-viewport ui-tabs-content");m=[];j._nav.addClass("ui-tabs-nav").children().each(function(){var r=n._findElement("a",this),p=r?r.attr("href"):e(this).attr("data-url"),s,q;s=d.test(p)?RegExp.$1:"tabs_"+b();(q=n._findElement("#"+s)||e('<div id="'+s+'"></div>')).addClass("ui-panel ui-tabs-panel"+(j.transition?" "+j.transition:"")).appendTo(j._content);m.push({id:s,href:p,title:r?r.attr("href","javascript:;").text():e(this).text(),content:q})});j.items=m;j.active=Math.max(0,Math.min(m.length-1,j.active||e(".ui-state-active",j._nav).index()||0));n._getPanel().add(j._nav.children().eq(j.active)).addClass("ui-state-active");break}default:m=j.items=j.items||[];g=[];i=[];j.active=Math.max(0,Math.min(m.length-1,j.active));e.each(m,function(p,q){h="tabs_"+b();g.push({href:q.href||"#"+h,title:q.title});i.push({content:q.content||"",id:h});m[p].id=h});j._nav=e(e.parseTpl(a.nav,{items:g,active:j.active})).prependTo(o);j._content=e(e.parseTpl(a.content,{items:i,active:j.active,transition:j.transition})).appendTo(o);j.container=j.container||(o.parent().length?null:"body")}j.container&&o.appendTo(j.container);n._fitToContent(n._getPanel())},_getPanel:function(g){var h=this._data;return e("#"+h.items[g===f?h.active:g].id)},_findElement:function(g,i){var h=e(i||this._el).find(g);return h.length?h:null},_create:function(){var g=this,h=g._data;g._el=g._el||e("<div></div>");g._prepareDom("create",h)},_setup:function(i){var g=this,h=g._data;g._prepareDom(i?"fullsetup":"setup",h)},_init:function(){var i=this,j=i._data,h=i.root(),g=e.proxy(i._eventHandler,i);h.addClass("ui-tabs");j._nav.on("tap",g).children().highlight("ui-state-hover");e(window).on("ortchange",g)},_eventHandler:function(i){var g,h=this._data;switch(i.type){case"ortchange":this.refresh();break;default:if((g=e(i.target).closest("li",h._nav.get(0)))&&g.length){i.preventDefault();this.switchTo(g.index())}}},_fitToContent:function(i){var h=this._data,g=h._content;h._plus===f&&(h._plus=parseFloat(g.css("border-top-width"))+parseFloat(g.css("border-bottom-width")));g.height(i.height()+h._plus);return this},switchTo:function(h){var k=this,g=k._data,j=g.items,n,l,m,i,o;if(!g._buzy&&g.active!=(h=Math.max(0,Math.min(j.length-1,h)))){l=e.extend({},j[h]);l.div=k._getPanel(h);l.index=h;m=e.extend({},j[g.active]);m.div=k._getPanel();m.index=g.active;n=e.Event("beforeActivate");k.trigger(n,[l,m]);if(n.defaultPrevented){return k}g._content.children().removeClass("ui-state-active");l.div.addClass("ui-state-active");g._nav.children().removeClass("ui-state-active").eq(l.index).addClass("ui-state-active");if(g.transition){g._buzy=true;o=e.fx.animationEnd+".tabs";i=h>g.active?"":" reverse";g._content.addClass("ui-viewport-transitioning");m.div.addClass("out"+i);l.div.addClass("in"+i).on(o,function(p){if(p.target!=p.currentTarget){return}l.div.off(o,arguments.callee);g._buzy=false;m.div.removeClass("out reverse");l.div.removeClass("in reverse");g._content.removeClass("ui-viewport-transitioning");k.trigger("animateComplete",[l,m]);k._fitToContent(l.div)})}g.active=h;k.trigger("activate",[l,m]);g.transition||k._fitToContent(l.div)}return k},refresh:function(){return this._fitToContent(this._getPanel())},destroy:function(){var h=this._data,g=this._eventHandler;h._nav.off("tap",g).children().highlight();h.swipe&&h._content.off("swipeLeft swipeRight",g);return this.$super("destroy")}})})(Zepto);
/*!Widget navigator.js*/
(function(b,c){var a='<% for (var i=0, len=left.length; i<len; i++) { %><a href="<%=left[i].url%>" class="ui-navigator-fix ui-navigator-fixleft"><%=left[i].text%></a><% } %><ul class="ui-navigator-list"><% for (var i=0, len=mid.length; i<len; i++) { %><li><a href="<%=mid[i].url%>"><%=mid[i].text%></a></li><% } %></ul><% for (var i=0, len=right.length; i<len; i++) { %><a href="<%=right[i].url%>" class="ui-navigator-fix ui-navigator-fixright"><%=right[i].text%></a><% } %>';b.ui.define("navigator",{_data:{container:"",content:[],defTab:0,beforetabselect:null,tabselect:null},_create:function(){var h=this,i=h._data,g=h.root(),d=b(i.container||document.body).get(0),e={left:[],mid:[],right:[]},f;b.each(i.content,function(){e[this.pos?this.pos:"mid"].push(this)});f=b.parseTpl(a,e);if(g){g.append(f);(!g.parent().length||d!==document.body)&&g.appendTo(d)}else{h.root(b("<div></div>").append(f)).appendTo(d)}},_setup:function(e){var g=this,h=g._data,f=h.defTab,d=g.root();if(!e){d.children("a").addClass("ui-navigator-fix");d.children("ul").addClass("ui-navigator-list")}d.find("a").each(function(j){f===0?b(this).hasClass("cur")&&(h.defTab=j):b(this).removeClass("cur")})},_init:function(){var f=this,g=f._data,d=f.root(),e=g.content,h=d.find("a");h.each(function(j){this.index=j;e.length&&e[j].attr&&b(this).attr(e[j].attr)});g._$tabList=h;g._lastIndex=-1;d.addClass("ui-navigator").on("click",b.proxy(f._switchTabHandler,f));f.switchTo(g.defTab,true)},_switchTabHandler:function(g){var d=this,f=g.target;b(f).closest("a").get(0)&&d.switchTo(f.index,false,g);return d},switchTo:function(g,f,k){var h=this,i=h._data,l=i._lastIndex,j=i._$tabList,d=b.Event("beforetabselect");h.trigger(d,[j[g]]);if(d.defaultPrevented){k&&k.preventDefault();return h}if(l==g){k&&k.preventDefault();return h}l>=0&&j.eq(l).removeClass("cur");j.eq(g).addClass("cur");i._lastIndex=g;return h.trigger("tabselect",[j.get(g),g])},getCurTab:function(){var d=this,e=d._data,f=e._lastIndex;return{index:f,info:e._$tabList[f]}}})})(Zepto);
