<!DOCTYPE HTML>
<html lang="en-US">
<head>
    <meta http-equiv="Content-Type" content="text/html;charset=UTF-8" />
    <meta name="format-detection" content="telephone=no" />
    <meta name="viewport" content="width=480,user-scalable=no" />
    <script src=../../js/vconsole.min.js></script>
    <!--CSS-->
    <link rel="stylesheet" href="../../frame3/css/bingotouch.css" />
    <!-- 应用样式 -->
    <link rel="stylesheet" href="../../frame3/css/app.css" />
    <!-- frame 3.0 JS-->
    <script src="../../frame3/js/cordova.js"></script>
    <script src="../../frame3/js/zepto.js"></script>
    <script src="../../frame3/js/iscroll.js"></script>
    <script src="../../frame3/js/baiduTemplate.js"></script>
    <script src="../../frame3/js/bingotouch.js"></script>
    <script src="../../frame3/js/require.js"></script>

    <!-- 应用脚本 -->
    <script src="../../frame3/js/plugin/linkplugins.js"></script>
    <script src="../../frame3/js/app/app.js"></script>

    <!--old js-->
    <script src="../../js/xh_public.js"></script>
    <script src="../../js/SzgaPlugin.js"></script>

    <!--引用url定义文件-->
    <script src="url.js" type="text/javascript"></script>
    <style type="text/css">
        .bdcontents {
            border-top: 1px solid #dedede;
            width: 100%;
            background-color: #FFFFFF;
            display: flex;
            flex-wrap: wrap;
            clear: none !important;
        }

        .bdcontents dl {
            width: 20%;
            height: 100px;
            margin: 0;
        }

        .bdcontents dl dt {
            width: 80%;
            margin-top: 10px;
            margin-left: 10px;
            border: none;
        }

        .bdcontents dl dt img {
            width: 40px;
            height: 40px;
            border: none;
        }

        .bdcontents dl dd {
            color: #5e5e5e;
            font-size: 16px;
            text-align: center;
            line-height: 16px;
            width: 100%;
            margin: 5px 0 0;
            border: none;
        }

        .bdYingYong {
            border-top: 1px solid #dedede;
            width: 100%;
            background-color: #FFFFFF;
            display: flex;
            flex-wrap: wrap;
            clear: none !important;
        }
        .bdYingYong dl {
            border-bottom: 1px solid #dedede;
            border-right: 1px solid #dedede;
            width: 25%;
            height: 100px;
            margin: 0;
        }
        .bdYingYong dl dt {
            width: 80%;
            margin-top: 15px;;
            margin-left: 10px;;
        }
        .bdYingYong dl dt img {
            width: 50px;
            height: 50px;
        }
        .bdYingYong dl dd {
            color: #5e5e5e;
            font-size: 16px;
            text-align: center;
            line-height: 16px;
            width: 100%;
            margin: 5px 0 0;
            border: none;
        }

        .con_end {
            width: 100%;

            margin-top: 8px;
            background-color: #FFFFFF;

            font-size: 20px;
            padding-left: 10px;
            color: #5e5e5e;
            line-height: 50px;
            letter-spacing: 1px;
        }

        .badges {
            min-width: 30px;
            height: 30px;
            top: -4px;
            right: 1px;
            background-color: #de223e;
            border: none;
        }

        .shenglvhao {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .tj [data-role='BTButton'] {
            border: 0 !important;
            background-color: transparent !important;
        }
        
        .navbar[data-theme="a"] [data-role="BTButton"].btn-active {
            border-bottom: 5px solid #d93a49!important;
            color: #d93a49!important;
        }
    </style>
    <title>常用应用</title>

    <script type="text/javascript">
        var userInfo;
        var deptCode;
        var terminalId;
        var access_token;
        var exitFlag = true;
        var appList = [];
        var allApp1=[];
        var currentTab = "my-app";
        var installedApps = [];
        var insapps=[];
        app.page.onReady = function() {

        };

        app.page.onLoad = function() {
            document.addEventListener("deviceready", function() {
                document.addEventListener("backbutton", function() {
                    app.back();
                }, false);
                
            }, false);

            shuiyin();
            xh.getLoginInfo(function (result) {
                userInfo = result;
                app.link.getUserInfo(function (res) {
                    deptCode = res.deptCode;
                    app.getMeid(function(result){
                        terminalId = result;
                        ui.showMask("请稍候...");
                        xh.getToken(userInfo.loginId,function(res) {
                            access_token = res.returnValue;
                            getApps();
                        })
                    });
                }, function (res) {
                    console.log(res);
                }, userInfo.userId);
            });

            onTabChangeListener();
            
        };
        app.page.onError = function() {

        };

        function tapAppSearch() {
            app.loadWithUrl("app_search.html", {apps: appList});
        }
        
        function onTabChangeListener() {
            $(".navbar-item").tap(function () {
                var value = $(this).attr("value");
                currentTab = value;
                console.log(value);
                renderApps(appList);
                renderApps(allApp1,"1");
            })
        }

        function getApps() {
            console.log(userInfo);
            xh.post(applist_url+"list/",{
            },function (res) {
                ui.hideMask();
                console.log(res);

                var ret = eval('(' + res.returnValue + ')');
                var allApp = ret.data[0].appList;
                appList = [];
                for (var index = 0; index < allApp.length; index++) {
                    if (allApp[index].appCode != "com.page.index" && allApp[index].appName != "新警信") {
                        appList.push(allApp[index]);
                    }
                }
                renderApps(appList);
                getAppscs();
            },function (e) {
                ui.hideMask();
                console.log(e);
            },function (header) {
                header.setRequestHeader("accessToken",access_token);
                header.setRequestHeader("requestType","app");
                header.setRequestHeader("applyID",applyId);
                header.setRequestHeader("secretKey",secretKey);
                header.setRequestHeader("userId",userInfo.userId);
                header.setRequestHeader("userCode",userInfo.userCode);
                header.setRequestHeader("terminalId",terminalId);
            })
        }

        function getAppscs() {
            xh.post(applist_url+"tsetList",{
                userCode: userInfo.userCode,
                accessToken: access_token,
                contentType: "application/json"
            },function (res) {
                ui.hideMask();
                //console.log(res);

                var ret = eval('(' + res.returnValue + ')');
                var appList = [];
                allApp1 = ret.data[0].appList;
                //appList.push({appName:"政务微信",appId:"chat",appEntry:"",md5:"",enclosure:"",version:"",appUrl:"",isInstall:1,activityName:"",appIcon:"img/ic_chat.png"});
                for (var index = 0; index < allApp1.length; index++) {
                    if (allApp1[index].appCode != "com.page.index" && allApp1[index].appName != "新警信") {
                        appList.push(allApp1[index]);
                    }
                }

                if(appList!=""){
                    renderApps(allApp1,"1");
                }
                //console.info(JSON.stringify(appList));
            },function (e) {
                ui.hideMask();
                //alert(e);
                console.log(e);
            },function (header) {
                header.setRequestHeader("accessToken", access_token);
                header.setRequestHeader("requestType", "app");
                header.setRequestHeader("applyID", applyId);
                header.setRequestHeader("secretKey", secretKey);
                header.setRequestHeader("userId", userInfo.userId);
                header.setRequestHeader("userCode", userInfo.userCode);
                header.setRequestHeader("terminalId", terminalId);
            })
        }
        function renderApps(result,typeapp) {
            //alert(JSON.stringify(result.data.result));
            var bt = baidu.template;
            
            //console.log(JSON.stringify(appList));
            var html = "";



                if (currentTab == "my-app") {
                $("#appAllList").hide();
                $("#appList").show();
                installedApps = [];
                for (var i=0;i<result.length;i++) {
                    var isInstall = result[i].isInstall;
 
                    if (isInstall == 1||typeapp=="1") {
                        installedApps.push(result[i]);
                    }
                }

                var length = installedApps.length;
                if(length > 0) {
                    for (var j = 0; j < length; j++) {
                        var app = installedApps[j];
                        app.index = j;
                        html += bt("app_list", app);
                    }
                    if(typeapp=="1"){
                        $("#appList").append(html);
                    }else{
                        $("#appList").html(html);
                    }
                    $("#appList").uiwidget();
                    $("#noAPP").hide();
                } else {
                    if(typeapp!="1"){
                        $("#appList").empty();
                        $("#noAPP").show();
                    }
                }

                longPress(".app_item",function(index){
                    var app = installedApps[index];
                    openApp(app.appId,app.appEntry,app.appName,app.md5,app.enclosure,app.version,app.appUrl,app.isInstall,app.activityName,app.ip)
                },function(index){

                    var app = installedApps[index];

                    

                    ui.confirm("提示", "是否卸载App?",function() {
                        console.log("1");
                        uninstallApp(app.appId);   
                    }, function() {
                        console.log("2");
                    });
                    // ui.alert("提示","是否卸载App？",function(){
                    //     console.log(1);
                    // },function(){
                    //     console.log(2);
                    // })
                });
            } else {
                $("#appAllList").show();
                $("#appList").hide();
                var length = result.length;
                if(length > 0) {
                    for (var j = 0; j < length; j++) {
                        var app = result[j];
                        app.index = j;
                        html += bt("apps", app);
                    }
                    if(typeapp=="1"){
                        $("#appAllList").append(html);
                    }else{
                        $("#appAllList").html(html);
                    }
                    $("#appAllList").uiwidget();
                    $("#noAPP").hide();
                } else {

                    if(typeapp!="1"){
                        $("#appAllList").empty();
                        $("#noAPP").show();
                    }
                }
            }
            
            
        }
        
        function openDetail(appId) {
            console.log(appId);
            
            for(var i=0;i<appList.length;i++) {
                if (appId == appList[i].appId) {
                    
                    var appDetail = appList[i];
                    app.loadWithUrl("app_detail.html", {app_detail: appDetail})
                    
                    break;
                }
            }
            
            //app.loadWithUrl("app_detail.html", {app_detail: appDetail});
        }

        function downloadApp(params) {
            console.log(userInfo);
            xh.post(applist_url+"List",{
            },function (res) {
                ui.hideMask();
                console.log(res);
                setLocalItem(params.appId,params.version);//保存下载时的版本信息

                console.log("download app appUrl = " + params.appUrl);
                console.log(params.appUrl.includes("http"));
                if (params.appUrl.includes("http")) {
                    var url = params.appUrl +
                            "?imid=" + userInfo.userCode +
                            "&accessToken=" + access_token;
                    console.log(url);
                    loadWebView(params.appUrl);
                } else {
                        

                    if (DEBUG) {
                        console.log("打开应用参数：" +JSON.stringify(params));
                    }
                    
                        
                    openH5(params);
                }
                getApps();
            },function (e) {
                ui.hideMask();
                console.log(e);
            },function (header) {
                header.setRequestHeader("accessToken",access_token);
                header.setRequestHeader("requestType","app");
                header.setRequestHeader("applyID",applyId);
                header.setRequestHeader("secretKey",secretKey);
                header.setRequestHeader("userId",userInfo.userId);
                header.setRequestHeader("userCode",userInfo.userCode);
                header.setRequestHeader("terminalId",terminalId);
            })
        }
        function uninstallApp(appId) {


            ui.showMask("正在加载...");
            console.log(userInfo);
            xh.post(applist_url+"List",{
            },function (res) {
                ui.hideMask();
                console.log(res);
                removeItem(appId);//保存下载时的版本信息
                app.hint("卸载成功");
                getApps();
            },function (e) {
                ui.hideMask();
                console.log(e);
            },function (header) {
                header.setRequestHeader("accessToken",access_token);
                header.setRequestHeader("requestType","app");
                header.setRequestHeader("applyID",applyId);
                header.setRequestHeader("secretKey",secretKey);
                header.setRequestHeader("userId",userInfo.userId);
                header.setRequestHeader("userCode",userInfo.userCode);
                header.setRequestHeader("terminalId",terminalId);
            })
        }
        function openApp(appId, entry, appName,md5,enclosure,version,appUrl,isInstalled,activityName,ip) {
            if (exitFlag) {
                exitFlag = false;
                setTimeout("exitFlag = true;", 1000);
                /*var params = {
                    appId: appId,
                    entry: entry,
                    query: query,
                    appName: appName
                };

                newPage(params);*/

                var params = {
                    appId: appId,
                    enclosure: enclosure,
                    md5: md5,
                    appUrl: appUrl,
                    version: version,
                    appEntry: entry,
                    activityName: activityName,
                    ip: mpass_url
                };      
                

                console.log("isInstall = " + isInstalled);
                console.log( JSON.stringify(params));


                var localVersion = getLocalItem(appId);
                if (DEBUG) {
                    console.log("localVersion = " + localVersion);
                    console.log("shouldDownload = " + (localVersion != version || isInstalled != 1 || localVersion == null || localVersion == 'undefined'));
                }
                if (localVersion != version || isInstalled != 1 || localVersion == null || localVersion == 'undefined') {
                    if (DEBUG) {
                        console.log("appUrl:" + appUrl);
                        console.log(params);
                    }
                    if (appUrl.includes("http")) { 
                        downloadApp(params);
                    } else {
                        //
                        downloadZip(params,function(e){

                            downloadApp(params);
                        });
                    }
                } else {

                    if (appUrl.includes("http")) {
                        var url = appUrl +
                            "?imid=" + userInfo.userCode +
                            "&accessToken=" + access_token;
                        console.log(url);
                        loadWebView(url);
                    } else {
                        

                        if (DEBUG) {
                            console.log("打开应用参数：" +JSON.stringify(params));
                        }
                        
                        openH5(params);
                    }
                }

                // if (isInstalled == 1) {

                //     if (appUrl.includes("http")) {
                //         var url = appUrl +
                //             "?imid=" + userInfo.userCode +
                //             "&accessToken=" + access_token;
                //         console.log(url);
                //         loadWebView(url);
                //     } else {
                        

                //         if (DEBUG) {
                //             console.log("打开应用参数：" +JSON.stringify(params));
                //         }
                        
                //         openH5(params);
                //     }
                // } else {
                //     if (DEBUG) {
                //         console.log("appUrl:" + appUrl);
                //         console.log(params);
                //     }
                //     if (appUrl.includes("http")) { 
                //         downloadApp(params);
                //     } else {
                //         //
                //         downloadZip(params,function(e){

                //             downloadApp(params);
                //         });
                //     }
                // }

            }
        }

        /**
         * @desc downloadZip下载apk
         * @param {*} 下载apk应用参数(appId,enclosure,md5,version,appEntry,activityName,ip)
         */
        function downloadZip(params, callback) {
            var callback = callback || function () {};
            var successCallback = function (result) {
                
                console.log(result);
            
                callback(result);
            };

            var failureCallback = function (result) {
                console.log(result);
            };
            

            Cordova.exec(successCallback, failureCallback, "Interactive", "downloadZip", [params]);
        }

        function loadWebView(url, params) {

            //alert(url);
            var successCallback = function (res) {
                console.log(res);
            };
            var failureCallback = function (res) {
                console.log(res);
            };
            Cordova.exec(successCallback, failureCallback, "Interactive", "loadWebView", [url, params]);
        }


        function newPage(params) {
            params = params || {};

            console.log(params);
            //alert(JSON.stringify(params));
            console.log([params.appId, params.entry, params.query, params.appName]);

            Cordova.exec(null, null, "Page", "newPage", [
                params.appId,
                params.entry,
                params.query,
                params.appName
            ]);
        }
        function downloadZip(params, callback) {
            var callback = callback || function () {};
            var successCallback = function (result) {
                console.log(result);
            
                callback(result);
            };

            var failureCallback = function (result) {
            
              
            };
        

          Cordova.exec(successCallback, failureCallback, "Interactive", "downloadZip", [params]);
        }
        /**
         * desc openH5
         * @param {*} 打开h5应用参数(appId,enclosure,md5,version,appEntry,activityName,ip)
         * @param {*} 可传空  
         * @param {*} callback 
         */
        function openH5(param1, param2, callback) {
          var callback = callback || function () {};
          var successCallback = function (res) {
            callback(res);
          };
          Cordova.exec(successCallback, null, "Interactive", "openH5", [param1, param2]);
        }
        function getLocalItem(key) {
            var result = window.localStorage.getItem(key)
            try {
              result = JSON.parse(result)
            } catch (e) {
              result = result
            }
            return result
        }

        function setLocalItem(key, value) {
            value = JSON.stringify(value);
            window.localStorage.setItem(key, value)
        }

        function removeItem(key) {
            window.localStorage.removeItem(key);
        }

        function longPress(element,onTap,onLongTap) {
            var timeOutEvent = 0;
            

            $(".app_item").on({
                touchstart: function (e) {

                    var that = this;
                    console.log("touchstart");
                    console.log(e);
                    timeOutEvent = setTimeout(function () {
                        clearTimeout(timeOutEvent);
                        timeOutEvent = 0;
                        var index = $(that).attr("index");
                        onLongTap(index);
                    }, 1500);
                },
                touchmove: function () {
                    console.log("touchmove");
                    //clearTimeout(timeOutEvent);
                    //timeOutEvent = 0;
                },
                touchend: function (e) {
                    console.log("touchend");

                    if (timeOutEvent > 0) {
                        var index = $(this).attr("index");
//                        onclick="openApp('<%=appId%>','<%=appEntry%>','<%=appName%>','<%=md5%>','<%=enclosure%>','<%=version%>','<%=appUrl%>','<%=isInstall%>','<%=activityName%>','<%=ip%>')"
                        console.log(index);
                        onTap(index);
                    }

                    clearTimeout(timeOutEvent);
                }
            })
        }
        function tapAppDetail(index) {

            // for(var i=0;i<appList.length;i++) {
            //     if (appId == appList[i].appId) {

            //     }
            // }
            console.log(index);
            var appDetail = appList[index];
            //console.log(JSON.stringify(appDetail));
            app.loadWithUrl("app_detail.html", {app_detail: appDetail});
        }
    </script>

    <script type="text/html" id="app_list">
        <dl class="app_item" index="<%=index%>">
            <dt data-role="BTButton"><img src="<%=appIcon%>"/></dt>
            <dd class="shenglvhao">
                <%=appName%>
            </dd>
        </dl>
    </script>
    <script type="text/html" id="apps">

        <div 
            style="text-align: left;border-bottom: 1px solid #EEEEEE;border-radius: 0;background: #FFFFFF">
            
            <div style="padding: 15px;display:flex;align-items:center" >

                <div style="display:flex;flex:1;align-items:center" onclick="tapAppDetail('<%=index%>')" >
                    <div>
                        <img src="<%=appIcon%>" style="width: 60px;height: 60px;margin-right:15px" alt=""/>
                    </div>
                    <div>
                        <h3> <%=appName%></h3>
                    </div>
                </div>
                


                <div style="display:flex;">
                    <%if(isInstall==1){%>
                        <div data-role="BTButton" data-theme="b" style="text-align: center;width:100px"
                        onclick="openApp('<%=appId%>','<%=appEntry%>','<%=appName%>','<%=md5%>','<%=enclosure%>','<%=version%>','<%=appUrl%>','<%=isInstall%>','<%=activityName%>','<%=ip%>')">
                            打开
                        </div>
                    <%}else{%>
                        <div data-role="BTButton" data-theme="c" style="text-align: center;width:100px"
                        onclick="openApp('<%=appId%>','<%=appEntry%>','<%=appName%>','<%=md5%>','<%=enclosure%>','<%=version%>','<%=appUrl%>','<%=isInstall%>','<%=activityName%>','<%=ip%>')">
                            下载
                        </div>
                    <%}%>
                </div>
            </div>

            
        </div>
    </script>  

</head>
<body class="desktop">
<div id="section_container">
    <section id="exam_detail_section" class="active">
        <!--Header-->
        <div class="header" data-fixed="top">
            <div class="title row-box" style="border: none;">
                <div class="box-left" style="background:  #d93a49">
                    <div data-role="BTButton" data-type="image" onclick="app.back()">
                    <img src="../../css/images/icons/navicon/icon-back.png" alt=""/>
                    </div>
                </div>
                <div class="span1" style="background:  #d93a49">
                    <h1>应用</h1>
                </div>
                <div class="box-right" style="background: #d93a49">
                    <!-- <div data-role="BTButton" data-type="image" onclick="tapAppSearch();">
                        <img src="img/ic_search_white.png" alt=""/>
                    </div> -->
                    <div data-role="BTButton" data-type="image" onclick="app.refresh();">
                        <img src="../../css/images/icons/navicon/icon-refresh.png" alt=""/>
                    </div>
                </div>
            </div>
            <div onclick="tapAppSearch();" class="row-fluid" style="padding:10px 10px 0;background: #fff;">
                <div class="span10">
                    <div class="row-box" data-role="BTSearchbar" data-corner="all" style="background: #f7f6fa">
                        <div class="btn-search"><img width="24px" src="img/ic_search.png" alt=""/></div>
                        <div class="span1"><input id="input_task_title" type="text" value="" placeholder="请输入搜索内容"
                                                  disabled="disabled"/></div>
                    </div>
                </div>
                <div class="span2">
                    <div data-role="BTButton"
                         style=" padding: 0 10px; background-color: #0082ec;color: #fff;border:1px solid #0082ec">搜索
                    </div>
                </div>
            </div>
            <div class="navbar" data-theme="a" style="display: block">
                <ul class="grid-b">
                    <li>
                        <div data-role="BTButton" class="navbar-item btn-active" value="my-app"
                             style="height: 70px;line-height: 70px;background: #ffffff"><span
                                style="height: 70px;line-height: 70px;">我的应用</span></div>
                    </li>
                    <li>
                        <div data-role="BTButton" class="navbar-item" value="all-app"
                             style="height: 70px;line-height: 70px;background: #ffffff"><span
                                style="height: 70px;line-height: 70px;">全部应用</span></div>
                    </li>
                </ul>
            </div>


            
        </div>

       

        <!--Content-->
        <div class="content iscroll-wrapper" style="background:#FFFFFF;">

            <div>
                <div class="tj bdYingYong" id="appList" style="margin-top:-5px;">
                </div>

                <div id="appAllList">
                </div>

                <div id="noAPP" style="display: none;font-size: 22px;margin-top: 50px;text-align: center;color: #8F9193;">暂无应用</div>
            </div>
            
        </div>

        
        
    </section>

    
</div>
</body>
</html>