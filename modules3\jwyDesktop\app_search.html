<!DOCTYPE HTML>
<html lang="en-US">
<head>
    <meta http-equiv="Content-Type" content="text/html;charset=UTF-8" />
    <meta name="format-detection" content="telephone=no" />
    <meta name="viewport" content="width=480,user-scalable=no" />
    <!--CSS-->
    <link rel="stylesheet" href="../../frame3/css/bingotouch.css" />
    <!-- 应用样式 -->
    <link rel="stylesheet" href="../../frame3/css/app.css" />
    <script src=../../js/vconsole.min.js></script>

    <!-- frame 3.0 JS-->
    <script src="../../frame3/js/cordova.js"></script>
    <script src="../../frame3/js/zepto.js"></script>
    <script src="../../frame3/js/iscroll.js"></script>
    <script src="../../frame3/js/baiduTemplate.js"></script>
    <script src="../../frame3/js/bingotouch.js"></script>
    <script src="../../frame3/js/require.js"></script>

    <!-- 应用脚本 -->
    <script src="../../frame3/js/plugin/linkplugins.js"></script>
    <script src="../../frame3/js/app/app.js"></script>

    <!--old js-->
    <script src="../../js/xh_public.js"></script>
    <script src="../../js/SzgaPlugin.js"></script>

    <!--引用url定义文件-->
    <script src="url.js" type="text/javascript"></script>
    <style type="text/css">

    </style>
    <title>开始答题</title>

    <style type="text/css">
        body {
            height: auto;
            overflow: scroll;
        }
        textarea:focus, input[type="text"]:focus, input[type="password"]:focus, input[type="datetime"]:focus, input[type="datetime-local"]:focus, input[type="date"]:focus, input[type="month"]:focus, input[type="time"]:focus, input[type="week"]:focus, input[type="number"]:focus, input[type="email"]:focus, input[type="url"]:focus, input[type="search"]:focus, input[type="tel"]:focus, input[type="color"]:focus, .uneditable-input:focus {
            border: 1px solid #b9b9b9;
            border-radius: 6px;
            box-shadow: none;
        }

        .tj {
            margin: 0;
        }


        .tj ul li {
            line-height: 20px;
            font-size: 16px;

            margin-right: 10px;
        }
        .user-img {
            width: 20%;
            padding: 10px 0 10px 5px;
            text-align: center;
        }
        .user-img img {
            width: 80px;
            height: 80px;
        }
        .bdcontents {
            border-top: 1px solid #dedede;
            width: 100%;
            background-color: #FFFFFF;
            display: flex;
            flex-wrap: wrap;
            clear: none !important;
        }

        .bdcontents dl {
            width: 20%;
            height: 100px;
            margin: 0;
        }

        .bdcontents dl dt {
            width: 80%;
            margin-top: 10px;
            margin-left: 10px;
            border: none;
        }

        .bdcontents dl dt img {
            width: 40px;
            height: 40px;
            border: none;
        }

        .bdcontents dl dd {
            color: #5e5e5e;
            font-size: 16px;
            text-align: center;
            line-height: 16px;
            width: 100%;
            margin: 5px 0 0;
            border: none;
        }

        .bdYingYong {
            border-top: 1px solid #dedede;
            width: 100%;
            background-color: #FFFFFF;
            display: flex;
            flex-wrap: wrap;
            clear: none !important;
        }
        .bdYingYong dl {
            width: 25%;
            height: 100px;
            margin: 0;
        }
        .bdYingYong dl dt {
            width: 80%;
            margin-top: 15px;;
            margin-left: 10px;;
        }
        .bdYingYong dl dt img {
            width: 50px;
            height: 50px;
        }
        .bdYingYong dl dd {
            color: #5e5e5e;
            font-size: 16px;
            text-align: center;
            line-height: 16px;
            width: 100%;
            margin: 5px 0 0;
            border: none;
        }

        .con_end {
            width: 100%;

            margin-top: 8px;
            background-color: #FFFFFF;

            font-size: 20px;
            padding-left: 10px;
            color: #5e5e5e;
            line-height: 50px;
            letter-spacing: 1px;
        }

        .badges {
            min-width: 30px;
            height: 30px;
            top: -4px;
            right: 1px;
            background-color: #de223e;
            border: none;
        }

        .shenglvhao {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .tj [data-role='BTButton'] {
            border: 0 !important;
            background-color: transparent !important;
        }
    </style>
    <script type="text/javascript">
        var userInfo;
        var deptCode;
        var terminalId;
        var access_token;
        var exitFlag = true;
        var appList = {};
        app.page.onReady = function() {

        };

        app.page.onLoad = function() {
            document.addEventListener("deviceready", function() {
                document.addEventListener("backbutton", function() {
                    app.back();
                }, false);
            }, false);
            //initScroll();
            initView();
            shuiyin();
            app.getPageParams(function(result){
                appList =result.apps;

                //console.log(JSON.stringify(appList));
                //ui.showMask("请稍候...");
            });
            xh.getLoginInfo(function (result) {
                userInfo = result;
                app.link.getUserInfo(function (res) {
                    deptCode = res.deptCode;
                    //alert(JSON.stringify(res));

                    app.getMeid(function(result){
                        terminalId = result;

                        //ui.showMask("请稍候...");
                        xh.getToken(userInfo.loginId,function(res) {
                            access_token = res.returnValue;
                            //getApps();
                        })
                    });
                }, function (res) {
                    console.log(res);
                }, userInfo.userId);
            });
        };

       
        app.page.onError = function() {

        };
        function initView(){

            /*$("#btn_search").tap(function(){
                var key_value = $("#key_value").val();
                alert(key_value);
                if(key_value == "" || key_value == null){
                    app.hint("请输入APP名称");
                }else{
                    var apps = [];
                    for (var i=0;i<appList.length;i++) {
                        var appName = appList[i].appName;

                        if (appName.includes(key_value)){
                            alert(appName);
                            apps.push(appList[i]);
                        }
                    }
                    alert(JSON.stringify(apps));
                    if (apps.length > 0) {
                        renderApps(apps);
                    }
                }
            });*/
        }

        function renderApps(result) {
            //alert(JSON.stringify(result.data.result));
            var bt = baidu.template;
            var apps = result;
            console.log(JSON.stringify(apps));
            var html = "";
            var gwLength = apps.length;
            if(gwLength > 0) {
                for (var j = 0; j < gwLength; j++) {
                    html += bt("apps", apps[j]);
                }
                //alert(html);
                $("#appList").prepend(html);
                $("#appList").uiwidget();
                $("#noAPP").hide();
            } else {
                $("#appList").empty();
                $("#noAPP").show();
            }

        }


        function downloadApp(params) {
            console.log(userInfo);
            ui.showMask();
            xh.post_jw(mpass_url + "/xinghuo-apaas-appservice/appservice/mobile/app/installOrUninstallApp",{
                userCode: userInfo.userCode,
                accessToken: access_token,
                appIds: params.appId,
                oprFlag: 1,
                contentType: "application/json"
            },function (res) {
                ui.hideMask();
                console.log(res);
                setLocalItem(params.appId,params.version);
                if (params.appUrl.includes("http")) {
                    var url = params.appUrl +
                            "?imid=" + userInfo.userCode +
                            "&accessToken=" + access_token;
                    console.log(url);
                    loadWebView(params.appUrl);
                } else {
                        

                    if (DEBUG) {
                        console.log("打开应用参数：" +JSON.stringify(params));
                    }
                        
                    openH5(params);
                }
            },function (e) {
                ui.hideMask();
                console.log(e);
            },function (header) {
                header.setRequestHeader("accessToken",access_token);
                header.setRequestHeader("requestType","app");
                header.setRequestHeader("applyID",applyId);
                header.setRequestHeader("secretKey",secretKey);
                header.setRequestHeader("userId",userInfo.userId);
                header.setRequestHeader("userCode",userInfo.userCode);
                header.setRequestHeader("terminalId",terminalId);
            })
        }

        function openApp(appId, entry, appName,md5,enclosure,version,appUrl,isInstalled,activityName,ip) {
            if (exitFlag) {
                exitFlag = false;
                setTimeout("exitFlag = true;", 1000);
                /*var params = {
                    appId: appId,
                    entry: entry,
                    query: query,
                    appName: appName
                };

                newPage(params);*/

                var params = {
                    appId: appId,
                    enclosure: enclosure,
                    md5: md5,
                    appUrl: appUrl,
                    version: version,
                    appEntry: entry,
                    activityName: activityName,
                    ip: mpass_url
                };      
                

                console.log("isInstall = " + isInstalled);
                console.log( JSON.stringify(params));

                var localVersion = getLocalItem(appId);
                if (DEBUG) {
                    console.log("localVersion = " + localVersion);
                    console.log("shouldDownload = " + (localVersion != version || isInstalled != 1 || localVersion == null || localVersion == 'undefined'));

                }
                if (localVersion != version || isInstalled != 1 || localVersion == null || localVersion == 'undefined') {
                    if (DEBUG) {
                        console.log("appUrl:" + appUrl);
                        console.log(params);
                    }
                    if (appUrl.includes("http")) { 
                        downloadApp(params);
                    } else {
                        //
                        downloadZip(params,function(e){

                            downloadApp(params);
                        });
                    }
                } else {

                    if (appUrl.includes("http")) {
                        var url = appUrl +
                            "?imid=" + userInfo.userCode +
                            "&accessToken=" + access_token;
                        console.log(url);
                        loadWebView(url);
                    } else {
                        

                        if (DEBUG) {
                            console.log("打开应用参数：" +JSON.stringify(params));
                        }
                        
                        openH5(params);
                    }
                }
            }
        }
        function loadWebView(url, params) {

        
            var successCallback = function (res) {
                console.log(res);
            };
            var failureCallback = function (res) {
                console.log(res);
            };
            Cordova.exec(successCallback, failureCallback, "Interactive", "loadWebView", [url, params]);
        }


        function downloadZip(params, callback) {
            var callback = callback || function () {};
            var successCallback = function (result) {
                console.log(result);
                
            
                callback(result);
            };

            var failureCallback = function (result) {
                console.log(result);
                
            };
            console.log(params);

            Cordova.exec(successCallback, failureCallback, "Interactive", "downloadZip", [params]);
        }

        function newPage(params) {
            params = params || {};

            console.log(params);

            console.log([params.appId, params.entry, params.query, params.appName]);

            Cordova.exec(null, null, "Page", "newPage", [
                params.appId,
                params.entry,
                params.query,
                params.appName
            ]);
        }
        /**
         * desc openH5
         * @param {*} 打开h5应用参数(appId,enclosure,md5,version,appEntry,activityName,ip)
         * @param {*} 可传空  
         * @param {*} callback 
         */
        function openH5(param1, param2, callback) {
          var callback = callback || function () {};
          var successCallback = function (res) {
            callback(res);
          };
          Cordova.exec(successCallback, null, "Interactive", "openH5", [param1, param2]);
        }
        
        function onInput (event) {
            //alert ("The new content: " + event.target.value);
            $("#appList").empty();
            var key_value = event.target.value;

            if(key_value != "" && key_value != null){
                var apps = [];
                for (var i=0;i<appList.length;i++) {
                    var appName = appList[i].appName;

                    if (appName.includes(key_value)){
                        apps.push(appList[i]);
                    }
                }
                if (apps.length > 0) {
                    renderApps(apps);
                } else {
                    $("#appList").empty();
                }
            } else {
                $("#appList").empty();
            }
        }
        /*function zsMany(objdx){
            var display = $(objdx.parentNode).find(".p3").css("display");
            if(display === "none"){
                $(objdx.parentNode).find(".p3").css("display","block");
                $(objdx.parentNode).find("#many_btn").css("display","none");
            }else{
                $(objdx.parentNode).find(".p3").css("display","none");
                $(objdx.parentNode).find("#many_btn").css("display","inline");
            }
        }*/

        /*function runAPP(code,appService) {
            if (appService === "StartNewDisk") {
                app.link.openCloudDisk();
            }else{
                var params = {
                    appCode: code+''
                };
                app.link.runApp(params);
            }
        }*/
        //保存收藏
        //保存收藏
        /*function saveApp(appService,obj){
            var isCollect = $(obj).attr("data-value");

            if(isCollect === "1"){
                isCollect="0";
                xh.post(url+"/supp/mdmDevice/savePersonApp",{
                    userLoginId:userInfo.loginId,
                    appService:appService,
                    isCollect:isCollect,
                    APP_URL:_url + "/supp/mdmDevice/savePersonApp"
                },function(res){
                    var ret = eval("(" + res.returnValue + ")");
//                        alert(JSON.stringify(ret))
                    if(ret.success){
                        $(obj).attr("class","icon icon-star3");
                        $(obj).attr("data-value","0");

                        app.hint("收藏成功");
                    }else{
                        app.hint("操作失败！");
                    }
                },function(res){
                    app.hint("亲，您的网络不给力哦！");
                });
            }else{
                isCollect="1";
                xh.post(url+"/supp/mdmDevice/savePersonApp",{
                    userLoginId:userInfo.loginId,
                    appService:appService,
                    isCollect:isCollect,
                    APP_URL:_url + "/supp/mdmDevice/savePersonApp"
                },function(res){
                    var ret = eval("(" + res.returnValue + ")");
                    if(ret.success){
                        $(obj).attr("class","icon icon-star");
                        $(obj).attr("data-value","1");

                        app.hint("取消收藏成功");
                    }else{
                        app.hint("操作失败！");
                    }
                },function(res){
                    app.hint("亲，您的网络不给力哦！");
                });

            }

        }*/
        function getLocalItem(key) {

            if (!window.localStorage) {
                alert("不支持local storage");
                return null;
            } else {
                var storage = window.localStorage;

                var value = storage.getItem(key);
            
            
                return value;
            }

            
        }

        function setLocalItem(key, value) {
            if (!window.localStorage) {
                alert("不支持local storage");
                return;
            } 
            window.localStorage.setItem(key, value);
        }
        function tapAppDetail(appId) {
            console.log(appId);
            for(var i=0;i<appList.length;i++) {
                if (appId == appList[i].appId) {
                    
                    var appDetail = appList[i];
                    
                    //console.log(JSON.stringify(appDetail));
                    app.back("openDetail("+appDetail.appId+")");
                    //app.loadWithUrl("app_detail.html", {app_detail: appDetail})
                    
                    break;
                }
            }
        }
    </script>
    <script type="text/html" id="apps">

        <div 
            style="text-align: left;border-bottom: 1px solid #EEEEEE;border-radius: 0;background: #FFFFFF">
            
            <div style="padding: 15px;display:flex;align-items:center" >

                <div style="display:flex;flex:1;align-items:center" onclick="tapAppDetail('<%=appId%>')" >
                    <div>
                        <img src="<%=appIcon%>" style="width: 60px;height: 60px;margin-right:15px" alt=""/>
                    </div>
                    <div>
                        <h3> <%=appName%></h3>
                    </div>
                </div>
                


                <div style="display:flex;">
                    <%if(isInstall==1){%>
                        <div data-role="BTButton" data-theme="b" style="text-align: center;width:100px"
                        onclick="openApp('<%=appId%>','<%=appEntry%>','<%=appName%>','<%=md5%>','<%=enclosure%>','<%=version%>','<%=appUrl%>','<%=isInstall%>','<%=activityName%>','<%=ip%>')">
                            打开
                        </div>
                    <%}else{%>
                        <div data-role="BTButton" data-theme="c" style="text-align: center;width:100px"
                        onclick="openApp('<%=appId%>','<%=appEntry%>','<%=appName%>','<%=md5%>','<%=enclosure%>','<%=version%>','<%=appUrl%>','<%=isInstall%>','<%=activityName%>','<%=ip%>')">
                            下载
                        </div>
                    <%}%>
                </div>
            </div>

            
        </div>
    </script>

</head>
<body class="desktop">
<div id="section_container">
    <section id="app_search_section" class="active">
        <!--Header-->
        <div class="header" data-fixed="top">
            <div class="title row-box" style="border: none;background-color: #d93a49!important;">
                <div class="box-left" style="background:  #d93a49">
                    <div data-role="BTButton" data-type="image" onclick="app.back()">
                    <img src="../../css/images/icons/navicon/icon-back.png" alt=""/>
                    </div>
                </div>
                <div class="span1" data-role="BTSearchbar" data-corner="all" style="background-color: #d93a49;border: none;padding-right: 10px;">
                    <input type="text" oninput="onInput (event)" placeholder="输入关键字，搜索App" value="" id="key_value" style="padding-top: -30px;width: 100%;"/>
                </div>
                <!--<div class="box-right" style="background: #0082ec">
                    <div style="background: #2C9DE2;color: #fff;height: 52px;line-height: 52px;padding: 0 20px;border-radius: 4px;margin: 10px 0 0 10px;" id="btn_search">
                        搜索
                    </div>
                </div>-->
            </div>
            
        </div>
        <!--Content-->
        <div class="content iscroll-wrapper" style="background:#FFFFFF;">
            <div  id="appList">


            </div>

            <!--<script type="text/html" id="ml_list">
                <li>

                    <div data-role="BTButton"  data-status="1">
                        <span class="btn-text">
							<div class="thumbnail">
                                <img src="<%=imgPath%>" alt=""/>
                            </div>
							<div class="thumbnail-text" style="width:80%;">
                                <h3><%=appName%>

                                </h3>
                                &lt;!&ndash;<p>建设单位:<span><%=buildDept%></span></p>
                                <p onclick="zsMany(this);">研发公司:<span><%=developCompany%></span><span id="many_btn" style="color: #0088CC;">>></span></p>&ndash;&gt;
                                <p>研发公司:<span><%=developCompany%></span></p>
                                &lt;!&ndash;<p class="p3" style="display: none;"><%=description%></p>&ndash;&gt;
                            </div>
                            <div style="width:10%;margin-top: 10px;">
                                <%if(isCollect=="1"){%>
                                    <span class="icon icon-star" style="font-size: 27px;color: #ffc55a;" onclick="saveApp('<%=appService%>',this);" data-value="<%=isCollect%>"></span>
                                <%}else{%>
                                    <span class="icon icon-star3" style="font-size: 27px;color: #ffc55a;" onclick="saveApp('<%=appService%>',this);" data-value="<%=isCollect%>"></span>
                                <%}%>
                            </div>
                        </span>
                    </div>
                </li>
            </script>-->
            <div id="noAPP" style="display: none;font-size: 22px;margin-top: 50px;text-align: center">暂无APP应用</div>
        </div>
    </section>
</div>
</body>
</html>